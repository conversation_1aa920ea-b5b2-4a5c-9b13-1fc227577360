/*
 * Copyright (C) 2024 mendhak
 *
 * This file is part of GPSLogger for Android.
 *
 * GPSLogger is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 2 of the License, or
 * (at your option) any later version.
 *
 * GPSLogger is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with GPSLogger.  If not, see <http://www.gnu.org/licenses/>.
 */

package com.mendhak.gpslogger.ui.models;

import java.util.ArrayList;
import java.util.List;

/**
 * 表示位置点分组的数据模型
 */
public class LocationGroup {
    private String groupName;
    private List<LocationPoint> locationPoints;
    private boolean isExpanded;
    private String iconName;
    private String iconId; // 图标ID，用于图标选择
    
    public LocationGroup(String groupName) {
        this.groupName = groupName;
        this.locationPoints = new ArrayList<>();
        this.isExpanded = true; // 默认展开
        this.iconName = "default"; // 默认图标
        this.iconId = "folder"; // 默认图标ID
    }
    
    public LocationGroup(String groupName, List<LocationPoint> locationPoints) {
        this.groupName = groupName;
        this.locationPoints = locationPoints != null ? locationPoints : new ArrayList<>();
        this.isExpanded = true;
        this.iconName = "default";
        this.iconId = "folder"; // 默认图标ID
        updateSequenceNumbers();
    }
    
    // Getters and Setters
    public String getGroupName() {
        return groupName;
    }
    
    public void setGroupName(String groupName) {
        this.groupName = groupName;
    }
    
    public List<LocationPoint> getLocationPoints() {
        return locationPoints;
    }
    
    public void setLocationPoints(List<LocationPoint> locationPoints) {
        this.locationPoints = locationPoints != null ? locationPoints : new ArrayList<>();
        updateSequenceNumbers();
    }
    
    public boolean isExpanded() {
        return isExpanded;
    }
    
    public void setExpanded(boolean expanded) {
        isExpanded = expanded;
    }
    
    public String getIconName() {
        return iconName;
    }

    public void setIconName(String iconName) {
        this.iconName = iconName;
    }

    public String getIconId() {
        return iconId;
    }

    public void setIconId(String iconId) {
        this.iconId = iconId;
    }
    
    /**
     * 添加位置点
     */
    public void addLocationPoint(LocationPoint point) {
        if (point != null) {
            locationPoints.add(point);
            updateSequenceNumbers();
        }
    }
    
    /**
     * 移除位置点
     */
    public boolean removeLocationPoint(LocationPoint point) {
        boolean removed = locationPoints.remove(point);
        if (removed) {
            updateSequenceNumbers();
        }
        return removed;
    }
    
    /**
     * 获取位置点数量
     */
    public int getPointCount() {
        return locationPoints.size();
    }
    
    /**
     * 更新序号
     */
    private void updateSequenceNumbers() {
        for (int i = 0; i < locationPoints.size(); i++) {
            locationPoints.get(i).setSequenceNumber(i + 1);
        }
    }
    
    /**
     * 根据序号获取位置点
     */
    public LocationPoint getLocationPointBySequence(int sequenceNumber) {
        if (sequenceNumber >= 1 && sequenceNumber <= locationPoints.size()) {
            return locationPoints.get(sequenceNumber - 1);
        }
        return null;
    }
    
    /**
     * 切换展开/折叠状态
     */
    public void toggleExpanded() {
        isExpanded = !isExpanded;
    }
    
    @Override
    public String toString() {
        return "LocationGroup{" +
                "groupName='" + groupName + '\'' +
                ", pointCount=" + getPointCount() +
                ", isExpanded=" + isExpanded +
                ", iconName='" + iconName + '\'' +
                '}';
    }
    
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        
        LocationGroup that = (LocationGroup) o;
        
        return groupName != null ? groupName.equals(that.groupName) : that.groupName == null;
    }
    
    @Override
    public int hashCode() {
        return groupName != null ? groupName.hashCode() : 0;
    }
}
