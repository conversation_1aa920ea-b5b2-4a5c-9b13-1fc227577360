<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:gravity="center"
    android:padding="8dp"
    android:background="?android:attr/selectableItemBackground"
    android:clickable="true"
    android:focusable="true">

    <!-- 图标容器 -->
    <FrameLayout
        android:layout_width="48dp"
        android:layout_height="48dp"
        android:layout_marginBottom="4dp">

        <!-- 背景圆圈 -->
        <View
            android:id="@+id/icon_background"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@drawable/circle_background" />

        <!-- 选中状态指示器 -->
        <View
            android:id="@+id/selection_indicator"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@drawable/circle_background"
            android:backgroundTint="#2196F3"
            android:visibility="gone" />

        <!-- 图标 -->
        <ImageView
            android:id="@+id/icon_image"
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:layout_gravity="center"
            android:src="@drawable/ic_folder_24dp"
            android:tint="@android:color/black" />

    </FrameLayout>

    <!-- 图标名称 -->
    <TextView
        android:id="@+id/icon_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="文件夹"
        android:textSize="10sp"
        android:textColor="@android:color/black"
        android:gravity="center"
        android:maxLines="1"
        android:ellipsize="end" />

</LinearLayout>
