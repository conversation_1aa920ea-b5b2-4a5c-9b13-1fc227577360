/*
 * Copyright (C) 2024 mendhak
 *
 * This file is part of GPSLogger for Android.
 *
 * GPSLogger is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 2 of the License, or
 * (at your option) any later version.
 *
 * GPSLogger is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with GPSLogger.  If not, see <http://www.gnu.org/licenses/>.
 */

package com.mendhak.gpslogger.ui.models;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.HashSet;
import java.util.Set;

/**
 * 位置点状态数据结构
 * 存储位置点的推送、通过、不通过状态，支持JSON序列化
 */
public class LocationPointState {
    
    private Set<Integer> pushedPointIndices = new HashSet<>();
    private Set<Integer> passedPointIndices = new HashSet<>();
    private Set<Integer> failedPointIndices = new HashSet<>();
    private int startIndex = 0; // 顺序推送起点索引，默认从0开始
    private long lastModified = System.currentTimeMillis();
    
    public LocationPointState() {
        // 默认构造函数
    }
    
    /**
     * 从JSON字符串创建LocationPointState对象
     */
    public static LocationPointState fromJson(String jsonString) throws JSONException {
        LocationPointState state = new LocationPointState();
        JSONObject json = new JSONObject(jsonString);
        
        // 解析推送状态
        if (json.has("pushedPoints")) {
            JSONArray pushedArray = json.getJSONArray("pushedPoints");
            for (int i = 0; i < pushedArray.length(); i++) {
                state.pushedPointIndices.add(pushedArray.getInt(i));
            }
        }
        
        // 解析通过状态
        if (json.has("passedPoints")) {
            JSONArray passedArray = json.getJSONArray("passedPoints");
            for (int i = 0; i < passedArray.length(); i++) {
                state.passedPointIndices.add(passedArray.getInt(i));
            }
        }
        
        // 解析不通过状态
        if (json.has("failedPoints")) {
            JSONArray failedArray = json.getJSONArray("failedPoints");
            for (int i = 0; i < failedArray.length(); i++) {
                state.failedPointIndices.add(failedArray.getInt(i));
            }
        }
        
        // 解析起点索引
        if (json.has("startIndex")) {
            state.startIndex = json.getInt("startIndex");
        }

        // 解析最后修改时间
        if (json.has("lastModified")) {
            state.lastModified = json.getLong("lastModified");
        }

        return state;
    }
    
    /**
     * 转换为JSON字符串
     */
    public String toJson() throws JSONException {
        JSONObject json = new JSONObject();
        
        // 序列化推送状态
        JSONArray pushedArray = new JSONArray();
        for (Integer index : pushedPointIndices) {
            pushedArray.put(index);
        }
        json.put("pushedPoints", pushedArray);
        
        // 序列化通过状态
        JSONArray passedArray = new JSONArray();
        for (Integer index : passedPointIndices) {
            passedArray.put(index);
        }
        json.put("passedPoints", passedArray);
        
        // 序列化不通过状态
        JSONArray failedArray = new JSONArray();
        for (Integer index : failedPointIndices) {
            failedArray.put(index);
        }
        json.put("failedPoints", failedArray);

        // 序列化起点索引
        json.put("startIndex", startIndex);

        // 序列化最后修改时间
        json.put("lastModified", lastModified);

        return json.toString();
    }
    
    // Getters and Setters
    public Set<Integer> getPushedPointIndices() {
        return new HashSet<>(pushedPointIndices);
    }
    
    public void setPushedPointIndices(Set<Integer> pushedPointIndices) {
        this.pushedPointIndices = new HashSet<>(pushedPointIndices);
        updateLastModified();
    }
    
    public Set<Integer> getPassedPointIndices() {
        return new HashSet<>(passedPointIndices);
    }
    
    public void setPassedPointIndices(Set<Integer> passedPointIndices) {
        this.passedPointIndices = new HashSet<>(passedPointIndices);
        updateLastModified();
    }
    
    public Set<Integer> getFailedPointIndices() {
        return new HashSet<>(failedPointIndices);
    }
    
    public void setFailedPointIndices(Set<Integer> failedPointIndices) {
        this.failedPointIndices = new HashSet<>(failedPointIndices);
        updateLastModified();
    }
    
    public long getLastModified() {
        return lastModified;
    }

    public int getStartIndex() {
        return startIndex;
    }

    public void setStartIndex(int startIndex) {
        this.startIndex = Math.max(0, startIndex); // 确保起点索引不小于0
        updateLastModified();
    }

    // 状态操作方法
    public void addPushedPoint(int pointIndex) {
        pushedPointIndices.add(pointIndex);
        updateLastModified();
    }
    
    public void removePushedPoint(int pointIndex) {
        pushedPointIndices.remove(pointIndex);
        updateLastModified();
    }
    
    public void addPassedPoint(int pointIndex) {
        passedPointIndices.add(pointIndex);
        failedPointIndices.remove(pointIndex); // 移除不通过状态
        updateLastModified();
    }
    
    public void addFailedPoint(int pointIndex) {
        failedPointIndices.add(pointIndex);
        passedPointIndices.remove(pointIndex); // 移除通过状态
        updateLastModified();
    }
    
    public void removePassFailStatus(int pointIndex) {
        passedPointIndices.remove(pointIndex);
        failedPointIndices.remove(pointIndex);
        updateLastModified();
    }
    
    public boolean isPushed(int pointIndex) {
        return pushedPointIndices.contains(pointIndex);
    }
    
    public boolean isPassed(int pointIndex) {
        return passedPointIndices.contains(pointIndex);
    }
    
    public boolean isFailed(int pointIndex) {
        return failedPointIndices.contains(pointIndex);
    }
    
    public void clear() {
        pushedPointIndices.clear();
        passedPointIndices.clear();
        failedPointIndices.clear();
        startIndex = 0; // 重置起点索引
        updateLastModified();
    }
    
    private void updateLastModified() {
        lastModified = System.currentTimeMillis();
    }
}
