/*
 * Copyright (C) 2016 mendhak
 *
 * This file is part of GPSLogger for Android.
 *
 * GPSLogger for Android is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 2 of the License, or
 * (at your option) any later version.
 *
 * GPSLogger for Android is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with GPSLogger for Android.  If not, see <http://www.gnu.org/licenses/>.
 */

package com.mendhak.gpslogger.ui.adapters;

import android.graphics.Color;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.mendhak.gpslogger.R;
import com.mendhak.gpslogger.ui.fragments.display.AnnotationViewFragment.SecondaryButton;
import com.mendhak.gpslogger.ui.components.LayoutEnums.TriggerMode;

import java.util.List;

/**
 * 二级按钮网格适配器
 */
public class SecondaryButtonAdapter extends RecyclerView.Adapter<SecondaryButtonAdapter.ViewHolder> {
    
    public interface OnSecondaryButtonClickListener {
        void onSecondaryButtonClick(SecondaryButton secondaryButton);
        void onSecondaryButtonLongClick(SecondaryButton secondaryButton);
    }
    
    private List<SecondaryButton> buttons;
    private OnSecondaryButtonClickListener listener;
    
    public SecondaryButtonAdapter(List<SecondaryButton> buttons, OnSecondaryButtonClickListener listener) {
        this.buttons = buttons;
        this.listener = listener;
    }
    
    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext())
                .inflate(R.layout.item_secondary_button, parent, false);
        return new ViewHolder(view);
    }
    
    @Override
    public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
        SecondaryButton button = buttons.get(position);
        
        // 设置按钮文本
        holder.buttonText.setText(button.getText());
        
        // 设置按钮颜色
        try {
            int color = Color.parseColor(button.getColor());
            holder.buttonContainer.setBackgroundColor(color);
        } catch (Exception e) {
            holder.buttonContainer.setBackgroundColor(Color.parseColor("#808080"));
        }
        
        // 设置触发模式图标和文本
        setupTriggerModeDisplay(holder, button.getTriggerMode());
        
        // 设置模板变量指示器
        setupTemplateVariableIndicator(holder, button);
        
        // 设置点击事件
        holder.itemView.setOnClickListener(v -> {
            if (listener != null) {
                listener.onSecondaryButtonClick(button);
            }
        });
        
        // 设置长按事件
        holder.itemView.setOnLongClickListener(v -> {
            if (listener != null) {
                listener.onSecondaryButtonLongClick(button);
            }
            return true;
        });
        
        // 设置启用/禁用状态
        holder.itemView.setAlpha(button.isEnabled() ? 1.0f : 0.5f);
        holder.itemView.setEnabled(button.isEnabled());
    }
    
    private void setupTriggerModeDisplay(ViewHolder holder, TriggerMode triggerMode) {
        if (triggerMode == null) {
            triggerMode = TriggerMode.VOICE_INPUT;
        }
        
        switch (triggerMode) {
            case VOICE_INPUT:
                holder.triggerModeIcon.setImageResource(R.drawable.ic_mic);
                holder.triggerModeText.setText("语音");
                break;
            case TEXT_INPUT:
                holder.triggerModeIcon.setImageResource(R.drawable.ic_edit);
                holder.triggerModeText.setText("文本");
                break;
            case COUNTER_ONLY:
                holder.triggerModeIcon.setImageResource(R.drawable.ic_add_circle);
                holder.triggerModeText.setText("计数");
                break;
            default:
                holder.triggerModeIcon.setImageResource(R.drawable.ic_help);
                holder.triggerModeText.setText("未知");
                break;
        }
    }
    
    private void setupTemplateVariableIndicator(ViewHolder holder, SecondaryButton button) {
        String templateVariable = button.getTemplateVariable();
        if (templateVariable != null && !templateVariable.trim().isEmpty()) {
            holder.templateVariableIndicator.setText("{" + templateVariable + "}");
            holder.templateVariableIndicator.setVisibility(View.VISIBLE);
        } else {
            holder.templateVariableIndicator.setVisibility(View.GONE);
        }
    }
    
    @Override
    public int getItemCount() {
        return buttons != null ? buttons.size() : 0;
    }
    
    /**
     * 更新按钮列表
     */
    public void updateButtons(List<SecondaryButton> newButtons) {
        this.buttons = newButtons;
        notifyDataSetChanged();
    }
    
    /**
     * 添加按钮
     */
    public void addButton(SecondaryButton button) {
        if (buttons != null && button != null) {
            buttons.add(button);
            notifyItemInserted(buttons.size() - 1);
        }
    }
    
    /**
     * 删除按钮
     */
    public void removeButton(int position) {
        if (buttons != null && position >= 0 && position < buttons.size()) {
            buttons.remove(position);
            notifyItemRemoved(position);
        }
    }
    
    /**
     * 获取指定位置的按钮
     */
    public SecondaryButton getButton(int position) {
        if (buttons != null && position >= 0 && position < buttons.size()) {
            return buttons.get(position);
        }
        return null;
    }
    
    static class ViewHolder extends RecyclerView.ViewHolder {
        LinearLayout buttonContainer;
        TextView buttonText;
        ImageView triggerModeIcon;
        TextView triggerModeText;
        TextView templateVariableIndicator;
        
        ViewHolder(View itemView) {
            super(itemView);
            buttonContainer = itemView.findViewById(R.id.secondary_button_container);
            buttonText = itemView.findViewById(R.id.secondary_button_text);
            triggerModeIcon = itemView.findViewById(R.id.trigger_mode_icon);
            triggerModeText = itemView.findViewById(R.id.trigger_mode_text);
            templateVariableIndicator = itemView.findViewById(R.id.template_variable_indicator);
        }
    }
}
