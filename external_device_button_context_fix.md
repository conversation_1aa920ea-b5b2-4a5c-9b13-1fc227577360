# 外部设备注释按钮上下文修复

## 🎯 问题描述

用户反馈：当外部设备控制映射的按钮是annotation面板中的语音模式按钮、文本模式按钮或计数器按钮时，触发功能后，写入txt中的组名为"快速输入"，按键名为"一键语音输入"或"一键文本输入"，而非annotation中设置的组名和button名。

## 🔍 问题分析

### 根本原因
之前的修复错误地将外部设备触发的注释按钮重定向到了快速输入功能，导致：
1. 使用了快速输入的组名（"快速输入"）而不是注释按钮配置的组名
2. 使用了快速输入的按钮名（"一键语音输入"/"一键文本输入"）而不是注释按钮配置的按钮名
3. 丢失了注释按钮的上下文信息

### 技术细节
- **错误的事件流**：`RequestAnnotationButton` → `RequestQuickVoiceInput`/`RequestQuickTextInput`
- **正确的事件流**：`RequestAnnotationButton` → `RequestAnnotationVoiceInput`/`RequestAnnotationTextInput` → 保留按钮上下文

## ✅ 修复方案

### 1. 创建新的事件类型
在`CommandEvents.java`中添加了两个新的事件类型来保留注释按钮上下文：

```java
/**
 * Request to trigger annotation button voice input with preserved context
 */
public static class RequestAnnotationVoiceInput {
    public String buttonText;
    public int buttonIndex;
    public String groupName;

    public RequestAnnotationVoiceInput(String buttonText, int buttonIndex, String groupName) {
        this.buttonText = buttonText;
        this.buttonIndex = buttonIndex;
        this.groupName = groupName;
    }
}

/**
 * Request to trigger annotation button text input with preserved context
 */
public static class RequestAnnotationTextInput {
    public String buttonText;
    public int buttonIndex;
    public String groupName;

    public RequestAnnotationTextInput(String buttonText, int buttonIndex, String groupName) {
        this.buttonText = buttonText;
        this.buttonIndex = buttonIndex;
        this.groupName = groupName;
    }
}
```

### 2. 修改直接触发方法
在`GpsMainActivity.java`中修改了`handleDirectVoiceInput`和`handleDirectTextInput`方法：

```java
// 修改前：使用快速输入事件
EventBus.getDefault().post(new CommandEvents.RequestQuickVoiceInput(standardizedSource));

// 修改后：使用注释按钮专用事件，保留上下文
EventBus.getDefault().post(new CommandEvents.RequestAnnotationVoiceInput(buttonText, buttonIndex, groupName));
```

### 3. 添加事件处理方法
在`GpsMainActivity.java`中添加了专门处理注释按钮上下文的事件处理方法：

```java
@EventBusHook
public void onEventMainThread(CommandEvents.RequestAnnotationVoiceInput requestAnnotationVoiceInput) {
    // 使用注释按钮的名称和组名启动语音输入
    showVoiceInputDialog(requestAnnotationVoiceInput.buttonText, 
                        requestAnnotationVoiceInput.buttonIndex, 
                        requestAnnotationVoiceInput.groupName);
}

@EventBusHook
public void onEventMainThread(CommandEvents.RequestAnnotationTextInput requestAnnotationTextInput) {
    // 使用注释按钮的名称和组名启动文本输入
    showTextInputDialog(requestAnnotationTextInput.buttonText, 
                       requestAnnotationTextInput.buttonIndex, 
                       requestAnnotationTextInput.groupName);
}
```

### 4. 实现上下文保留的输入对话框
创建了`showVoiceInputDialog`和`showTextInputDialog`方法，确保：
- 使用注释按钮配置的按钮名称
- 使用注释按钮配置的组名
- 保留按钮索引信息
- 正确传递上下文到`CommandEvents.Annotate`事件

## 🔧 技术实现细节

### 语音输入上下文保留
```java
private void showVoiceInputDialog(String buttonText, int buttonIndex, String groupName) {
    VoiceInputManager buttonVoiceInputManager = new VoiceInputManager(this, new VoiceInputManager.VoiceInputListener() {
        @Override
        public void onVoiceInputResult(String text) {
            // 使用注释按钮的上下文信息
            EventBus.getDefault().post(new CommandEvents.Annotate(text.trim(), text.trim(),
                                                                  buttonText, buttonIndex, groupName));
        }
        // ... 其他回调方法
    });
    
    // 启动语音输入并传递上下文
    android.location.Location location = Session.getInstance().getCurrentLocationInfo();
    buttonVoiceInputManager.startVoiceInputWithContext("", buttonText, buttonIndex, groupName, location);
}
```

### 文本输入上下文保留
```java
private void showTextInputDialog(String buttonText, int buttonIndex, String groupName) {
    com.mendhak.gpslogger.ui.dialogs.TextInputDialog.show(
        this,
        buttonText,  // 使用注释按钮的名称作为对话框标题
        new com.mendhak.gpslogger.ui.dialogs.TextInputDialog.OnTextInputListener() {
            @Override
            public void onTextInputResult(String inputText) {
                // 使用注释按钮的上下文信息
                EventBus.getDefault().post(new CommandEvents.Annotate(inputText, null,
                                                                      buttonText, buttonIndex, groupName));
            }
        }
    );
}
```

## 📊 修复效果

### 修复前
- **组名**：快速输入
- **按钮名**：一键语音输入 / 一键文本输入
- **上下文**：丢失注释按钮配置信息

### 修复后
- **组名**：使用注释面板中配置的组名
- **按钮名**：使用注释面板中配置的按钮名称
- **上下文**：完整保留注释按钮配置信息

## 🎯 验证方法

### 测试步骤
1. 在注释面板中配置按钮：
   - Button1：名称"测试语音"，组名"测试组"，模式"语音输入"
   - Button2：名称"测试文本"，组名"测试组"，模式"文本输入"

2. 在外部设备控制中映射：
   - NUMPAD_1 → ANNOTATION_BUTTON_1
   - NUMPAD_2 → ANNOTATION_BUTTON_2

3. 按下外部设备按键并完成输入

4. 检查生成的注释文件：
   - 组名应显示为"测试组"
   - 按钮名应显示为"测试语音"/"测试文本"

### 预期结果
- ✅ 模板变量`{group_name}`显示注释按钮配置的组名
- ✅ 模板变量`{button_name}`显示注释按钮配置的按钮名称
- ✅ 模板变量`{button_index}`显示正确的按钮索引
- ✅ 语音/文本输入功能正常工作
- ✅ 上下文信息完整传递到注释系统

## 🔄 事件流对比

### 修复前（错误）
```
外部设备按键 → ButtonActionMapper → CommandEvents.RequestAnnotationButton
    ↓
GpsMainActivity.handleDirectVoiceInput()
    ↓
CommandEvents.RequestQuickVoiceInput("外部设备-按钮名")  ❌ 丢失上下文
    ↓
快速语音输入处理 → 使用"快速输入"组名和"一键语音输入"按钮名
```

### 修复后（正确）
```
外部设备按键 → ButtonActionMapper → CommandEvents.RequestAnnotationButton
    ↓
GpsMainActivity.handleDirectVoiceInput()
    ↓
CommandEvents.RequestAnnotationVoiceInput(buttonText, buttonIndex, groupName)  ✅ 保留上下文
    ↓
showVoiceInputDialog() → 使用注释按钮配置的组名和按钮名
    ↓
CommandEvents.Annotate(text, text, buttonText, buttonIndex, groupName)  ✅ 完整上下文
```

## 📝 关键改进点

1. **上下文保留**：完整保留注释按钮的配置信息
2. **事件分离**：区分快速输入和注释按钮输入的事件处理
3. **接口完整性**：实现了VoiceInputListener的所有必需方法
4. **错误处理**：添加了完善的错误处理和日志记录
5. **用户体验**：保持了一致的用户界面和反馈

这个修复确保了外部设备触发的注释按钮能够正确使用注释面板中配置的按钮名称和组名，而不是被错误地重定向到快速输入功能。
