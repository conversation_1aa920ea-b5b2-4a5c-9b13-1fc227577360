<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="20dp"
    android:background="@drawable/dialog_background">

    <!-- 标题 -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="选择图标"
        android:textSize="18sp"
        android:textStyle="bold"
        android:textColor="@android:color/black"
        android:gravity="center"
        android:layout_marginBottom="16dp" />

    <!-- 图标网格 -->
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/icons_recycler"
        android:layout_width="match_parent"
        android:layout_height="300dp"
        android:layout_marginBottom="16dp" />

    <!-- 操作按钮 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="end">

        <!-- 取消按钮 -->
        <Button
            android:id="@+id/cancel_button"
            android:layout_width="wrap_content"
            android:layout_height="40dp"
            android:background="@drawable/button_secondary_background"
            android:text="取消"
            android:textColor="@android:color/black"
            android:textSize="14sp"
            android:paddingHorizontal="16dp"
            android:layout_marginEnd="8dp" />

        <!-- 确定按钮 -->
        <Button
            android:id="@+id/confirm_button"
            android:layout_width="wrap_content"
            android:layout_height="40dp"
            android:background="@drawable/button_primary_background"
            android:text="确定"
            android:textColor="@android:color/white"
            android:textSize="14sp"
            android:paddingHorizontal="16dp" />

    </LinearLayout>

</LinearLayout>
