<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:background="@drawable/dialog_background">

    <!-- 可滚动内容区域 -->
    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:maxHeight="450dp"
        android:fillViewport="true">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

    <!-- 标题 -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="分组设置"
        android:textSize="18sp"
        android:textStyle="bold"
        android:textColor="@android:color/black"
        android:gravity="center"
        android:layout_marginBottom="16dp" />

    <!-- 分组名称设置 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:layout_marginBottom="12dp">

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="分组名称"
            android:textSize="13sp"
            android:textStyle="bold"
            android:textColor="@android:color/black"
            android:layout_marginBottom="6dp" />

        <EditText
            android:id="@+id/group_name_edit"
            android:layout_width="match_parent"
            android:layout_height="42dp"
            android:background="@drawable/edittext_background"
            android:padding="10dp"
            android:textSize="13sp"
            android:textColor="@android:color/black"
            android:hint="请输入分组名称"
            android:textColorHint="@android:color/darker_gray"
            android:maxLines="1"
            android:inputType="text" />

    </LinearLayout>

    <!-- 分组图标设置 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:layout_marginBottom="12dp">

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="分组图标"
            android:textSize="13sp"
            android:textStyle="bold"
            android:textColor="@android:color/black"
            android:layout_marginBottom="6dp" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical">

            <!-- 当前图标显示 -->
            <ImageView
                android:id="@+id/current_icon_view"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:src="@drawable/ic_folder_24dp"
                android:background="@drawable/circle_background"
                android:padding="8dp"
                android:layout_marginEnd="12dp" />

            <!-- 选择图标按钮 -->
            <Button
                android:id="@+id/select_icon_button"
                android:layout_width="0dp"
                android:layout_height="40dp"
                android:layout_weight="1"
                android:background="@drawable/button_secondary_background"
                android:text="选择图标"
                android:textColor="@android:color/black"
                android:textSize="14sp" />

        </LinearLayout>

    </LinearLayout>

    <!-- 分组统计信息 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:layout_marginBottom="16dp"
        android:background="@drawable/preview_background"
        android:padding="10dp">

        <!-- 分组信息标题和重置按钮 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginBottom="6dp">

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="分组信息"
                android:textSize="13sp"
                android:textStyle="bold"
                android:textColor="@android:color/black" />

            <ImageButton
                android:id="@+id/quick_reset_button"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:src="@drawable/ic_refresh_24dp"
                android:background="?android:attr/selectableItemBackgroundBorderless"
                android:scaleType="centerInside"
                android:contentDescription="快速重置持久化数据" />

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="位置点总数："
                android:textSize="12sp"
                android:textColor="@android:color/black" />

            <TextView
                android:id="@+id/total_points_text"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="0"
                android:textSize="12sp"
                android:textStyle="bold"
                android:textColor="@android:color/black" />

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="已推送："
                android:textSize="12sp"
                android:textColor="@android:color/black" />

            <TextView
                android:id="@+id/pushed_points_text"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="0"
                android:textSize="12sp"
                android:textStyle="bold"
                android:textColor="#2196F3" />

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="已通过："
                android:textSize="12sp"
                android:textColor="@android:color/black" />

            <TextView
                android:id="@+id/passed_points_text"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="0"
                android:textSize="12sp"
                android:textStyle="bold"
                android:textColor="#4CAF50" />

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="不通过："
                android:textSize="12sp"
                android:textColor="@android:color/black" />

            <TextView
                android:id="@+id/failed_points_text"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="0"
                android:textSize="12sp"
                android:textStyle="bold"
                android:textColor="#F44336" />

        </LinearLayout>

    </LinearLayout>

    <!-- 按钮模式配置 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:layout_marginBottom="16dp"
        android:background="@drawable/preview_background"
        android:padding="10dp">

        <TextView
            android:id="@+id/button_mode_label"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="按钮模式配置"
            android:textSize="13sp"
            android:textStyle="bold"
            android:textColor="@android:color/black"
            android:layout_marginBottom="8dp" />

        <!-- 通过按钮配置 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:layout_marginBottom="12dp">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="通过按钮模式"
                android:textSize="11sp"
                android:textStyle="bold"
                android:textColor="@android:color/black"
                android:layout_marginBottom="6dp" />

            <RadioGroup
                android:id="@+id/pass_button_mode_group"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <RadioButton
                    android:id="@+id/radio_pass_text_mode"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="文本模式 - 弹出文本输入框"
                    android:textSize="10sp"
                    android:textColor="@android:color/black"
                    android:paddingVertical="1dp" />

                <RadioButton
                    android:id="@+id/radio_pass_voice_mode"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="语音模式 - 弹出语音识别界面"
                    android:textSize="10sp"
                    android:textColor="@android:color/black"
                    android:paddingVertical="1dp" />

                <RadioButton
                    android:id="@+id/radio_pass_counter_mode"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="计数器模式 - 递增计数器"
                    android:textSize="10sp"
                    android:textColor="@android:color/black"
                    android:paddingVertical="1dp" />

            </RadioGroup>

        </LinearLayout>

        <!-- 不过按钮配置 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:layout_marginBottom="12dp">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="不过按钮模式"
                android:textSize="11sp"
                android:textStyle="bold"
                android:textColor="@android:color/black"
                android:layout_marginBottom="6dp" />

            <RadioGroup
                android:id="@+id/fail_button_mode_group"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <RadioButton
                    android:id="@+id/radio_fail_text_mode"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="文本模式 - 弹出文本输入框"
                    android:textSize="10sp"
                    android:textColor="@android:color/black"
                    android:paddingVertical="1dp" />

                <RadioButton
                    android:id="@+id/radio_fail_voice_mode"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="语音模式 - 弹出语音识别界面"
                    android:textSize="10sp"
                    android:textColor="@android:color/black"
                    android:paddingVertical="1dp" />

                <RadioButton
                    android:id="@+id/radio_fail_counter_mode"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="计数器模式 - 递增计数器"
                    android:textSize="10sp"
                    android:textColor="@android:color/black"
                    android:paddingVertical="1dp" />

            </RadioGroup>

        </LinearLayout>

        <!-- 配置操作按钮 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <Button
                android:id="@+id/configure_buttons_button"
                android:layout_width="0dp"
                android:layout_height="32dp"
                android:layout_weight="1"
                android:background="@drawable/button_secondary_background"
                android:text="详细配置"
                android:textColor="@android:color/black"
                android:textSize="11sp"
                android:layout_marginEnd="8dp" />

            <Button
                android:id="@+id/reset_buttons_button"
                android:layout_width="0dp"
                android:layout_height="32dp"
                android:layout_weight="1"
                android:background="@drawable/button_secondary_background"
                android:text="重置默认"
                android:textColor="@android:color/black"
                android:textSize="11sp" />

        </LinearLayout>

        </LinearLayout>

        </LinearLayout>

    </ScrollView>

    <!-- 操作按钮 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center"
        android:padding="8dp">

        <!-- 重置数据按钮 -->
        <Button
            android:id="@+id/reset_data_button"
            android:layout_width="0dp"
            android:layout_height="36dp"
            android:layout_weight="1"
            android:background="@drawable/button_background_orange"
            android:text="重置"
            android:textColor="@android:color/white"
            android:textSize="11sp"
            android:layout_marginEnd="4dp" />

        <!-- 删除按钮 -->
        <Button
            android:id="@+id/delete_group_button"
            android:layout_width="0dp"
            android:layout_height="36dp"
            android:layout_weight="1"
            android:background="@drawable/button_background_red"
            android:text="删除"
            android:textColor="@android:color/white"
            android:textSize="11sp"
            android:layout_marginEnd="4dp" />

        <!-- 取消按钮 -->
        <Button
            android:id="@+id/cancel_button"
            android:layout_width="0dp"
            android:layout_height="36dp"
            android:layout_weight="1"
            android:background="@drawable/button_secondary_background"
            android:text="取消"
            android:textColor="@android:color/black"
            android:textSize="11sp"
            android:layout_marginEnd="4dp" />

        <!-- 确定按钮 -->
        <Button
            android:id="@+id/confirm_button"
            android:layout_width="0dp"
            android:layout_height="36dp"
            android:layout_weight="1"
            android:background="@drawable/button_primary_background"
            android:text="保存"
            android:textColor="@android:color/white"
            android:textSize="11sp" />

    </LinearLayout>

</LinearLayout>
