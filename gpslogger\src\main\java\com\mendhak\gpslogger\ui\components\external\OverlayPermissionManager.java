/*
 * Copyright (C) 2024 mendhak
 *
 * This file is part of GPSLogger for Android.
 *
 * GPSLogger is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 2 of the License, or
 * (at your option) any later version.
 *
 * GPSLogger is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with GPSLogger.  If not, see <http://www.gnu.org/licenses/>.
 */

package com.mendhak.gpslogger.ui.components.external;

import android.content.Context;
import android.content.Intent;
import android.net.Uri;
import android.os.Build;
import android.provider.Settings;
import com.mendhak.gpslogger.common.slf4j.Logs;
import org.slf4j.Logger;

/**
 * Manager for handling overlay (SYSTEM_ALERT_WINDOW) permissions
 * Required for displaying floating windows over other apps
 */
public class OverlayPermissionManager {
    
    private static final Logger LOG = Logs.of(OverlayPermissionManager.class);
    
    /**
     * Check if overlay permission is granted
     * @param context Android context
     * @return true if permission is granted, false otherwise
     */
    public static boolean hasOverlayPermission(Context context) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            boolean canDrawOverlays = Settings.canDrawOverlays(context);
            LOG.debug("Overlay permission check: {}", canDrawOverlays);
            return canDrawOverlays;
        } else {
            // Android 6.0以下默认有权限
            LOG.debug("Android version < 6.0, overlay permission granted by default");
            return true;
        }
    }
    
    /**
     * Request overlay permission from user
     * @param context Android context
     */
    public static void requestOverlayPermission(Context context) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            try {
                Intent intent = new Intent(Settings.ACTION_MANAGE_OVERLAY_PERMISSION);
                intent.setData(Uri.parse("package:" + context.getPackageName()));
                intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                context.startActivity(intent);
                LOG.info("Overlay permission request intent started");
            } catch (Exception e) {
                LOG.error("Failed to start overlay permission request", e);
            }
        } else {
            LOG.debug("Android version < 6.0, no need to request overlay permission");
        }
    }
    
    /**
     * Check if overlay permission can be requested
     * @param context Android context
     * @return true if permission can be requested, false otherwise
     */
    public static boolean canRequestOverlayPermission(Context context) {
        return Build.VERSION.SDK_INT >= Build.VERSION_CODES.M;
    }
    
    /**
     * Get overlay permission status description
     * @param context Android context
     * @return human-readable permission status
     */
    public static String getOverlayPermissionStatus(Context context) {
        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.M) {
            return "已授权（系统默认）";
        }
        
        if (hasOverlayPermission(context)) {
            return "已授权";
        } else {
            return "未授权";
        }
    }
    
    /**
     * Check if overlay features are supported on this device
     * @return true if supported, false otherwise
     */
    public static boolean isOverlaySupported() {
        // 悬浮窗功能在所有Android版本都支持，只是权限要求不同
        return true;
    }
    
    /**
     * Get the appropriate window type for overlay
     * @return WindowManager.LayoutParams type constant
     */
    public static int getOverlayWindowType() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            // Android 8.0+ 使用 TYPE_APPLICATION_OVERLAY
            return android.view.WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY;
        } else if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            // Android 6.0-7.1 使用 TYPE_PHONE
            return android.view.WindowManager.LayoutParams.TYPE_PHONE;
        } else {
            // Android 6.0以下使用 TYPE_SYSTEM_ALERT
            return android.view.WindowManager.LayoutParams.TYPE_SYSTEM_ALERT;
        }
    }
    
    /**
     * Interface for overlay permission result callback
     */
    public interface OverlayPermissionCallback {
        /**
         * Called when overlay permission is granted
         */
        void onPermissionGranted();
        
        /**
         * Called when overlay permission is denied
         */
        void onPermissionDenied();
    }
    
    /**
     * Check overlay permission and request if needed
     * @param context Android context
     * @param callback Result callback
     */
    public static void checkAndRequestOverlayPermission(Context context, OverlayPermissionCallback callback) {
        if (hasOverlayPermission(context)) {
            LOG.debug("Overlay permission already granted");
            if (callback != null) {
                callback.onPermissionGranted();
            }
        } else {
            LOG.info("Overlay permission not granted, requesting...");
            requestOverlayPermission(context);
            // Note: 由于权限请求是异步的，这里无法立即回调结果
            // 需要在应用恢复时重新检查权限状态
            if (callback != null) {
                callback.onPermissionDenied();
            }
        }
    }
    
    /**
     * Get user-friendly explanation for overlay permission
     * @return explanation text
     */
    public static String getOverlayPermissionExplanation() {
        return "悬浮窗权限允许应用在其他应用上方显示内容，" +
               "用于在后台直接弹出文本输入框，提升外部设备控制的使用体验。" +
               "请在系统设置中为GPSLogger授权此权限。";
    }
    
    /**
     * Get overlay permission settings intent
     * @param context Android context
     * @return Intent to open overlay permission settings, or null if not supported
     */
    public static Intent getOverlayPermissionSettingsIntent(Context context) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            Intent intent = new Intent(Settings.ACTION_MANAGE_OVERLAY_PERMISSION);
            intent.setData(Uri.parse("package:" + context.getPackageName()));
            return intent;
        }
        return null;
    }



    /**
     * Open overlay permission settings for the app
     * @param context Android context
     */
    public static void openOverlaySettings(Context context) {
        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                Intent intent = new Intent(Settings.ACTION_MANAGE_OVERLAY_PERMISSION);
                intent.setData(Uri.parse("package:" + context.getPackageName()));
                intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                context.startActivity(intent);
                LOG.info("Opened overlay permission settings");
            } else {
                LOG.debug("Overlay permission settings not needed for Android < 6.0");
            }
        } catch (Exception e) {
            LOG.error("Failed to open overlay permission settings", e);
            try {
                // Fallback to general app settings
                Intent intent = new Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS);
                intent.setData(Uri.parse("package:" + context.getPackageName()));
                intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                context.startActivity(intent);
                LOG.info("Opened app settings as fallback");
            } catch (Exception fallbackException) {
                LOG.error("Failed to open app settings as fallback", fallbackException);
            }
        }
    }
}
