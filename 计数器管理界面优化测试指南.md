# 计数器管理界面优化测试指南

## 🔧 优化内容

### 新增功能
1. **设置当前值功能**：
   - 每个数值计数器旁边新增"设置值"按钮，允许用户直接修改计数器当前值
   - **全局序号、每日序号、会话序号也新增"设置值"按钮**，实现统一的设置值功能
2. **应用按钮**：底部新增"应用"按钮，保存更改但不关闭对话框
3. **按钮文本优化**："重置所有计数器"按钮文本改为"重置"

### 界面改进
1. **底部按钮布局**：从2个按钮扩展为4个按钮（重置、取消、应用、保存）
2. **统一按钮样式**：所有底部按钮使用Material Design风格（`Widget.AppCompat.Button.Colored`）
3. **按钮颜色区分**：
   - 重置：灰色（secondaryColorText）
   - 取消：深灰色（darker_gray）
   - 应用：主色调（primaryColor）
   - 保存：强调色（accentColor）

### 技术实现
- **输入验证**：设置当前值时验证数字格式和范围限制
- **自动限制应用**：新值自动应用最小值/最大值限制
- **实时更新**：设置值后立即更新界面显示
- **用户反馈**：提供Toast提示和详细的操作反馈

## 📱 测试步骤

### 第一步：界面布局验证

1. **打开计数器管理界面**
   - 进入"插入变量" → "预设模板" → "管理计数器"
   - 或通过其他入口访问计数器管理界面

2. **检查界面元素**
   - ✅ 标题显示"计数器管理"
   - ✅ 副标题显示"查看和管理模板中使用的各种计数器变量"
   - ✅ 其他计数器部分正常显示（全局序号、每日序号、会话序号）
   - ✅ 数值计数器部分正常显示（num1-num20）

3. **检查其他计数器设置值按钮**
   - ✅ 全局序号旁边有"设置值"和"重置"两个按钮
   - ✅ 每日序号旁边有"设置值"和"重置"两个按钮
   - ✅ 会话序号旁边有"设置值"和"重置"两个按钮
   - ✅ "设置值"按钮使用强调色（蓝色），"重置"按钮使用灰色

4. **检查底部按钮布局**
   - ✅ 四个按钮水平排列：重置、取消、应用、保存
   - ✅ 按钮大小一致，间距均匀
   - ✅ 按钮颜色区分明显
   - ✅ 文字清晰可读

### 第二步：设置当前值功能测试

#### 测试用例1：序号计数器设置值功能
1. **测试全局序号设置值**：
   - 点击全局序号的"设置值"按钮
   - 验证对话框标题显示"设置当前值"
   - 验证消息显示"请输入全局序号的新值："
   - 输入一个有效数字（如：50）并确定
   - 验证界面立即更新显示"当前值: 050"

2. **测试每日序号设置值**：
   - 点击每日序号的"设置值"按钮
   - 输入一个有效数字（如：25）并确定
   - 验证界面立即更新显示"当前值: 025"

3. **测试会话序号设置值**：
   - 点击会话序号的"设置值"按钮
   - 输入一个有效数字（如：10）并确定
   - 验证界面立即更新显示"当前值: 010"

#### 测试用例2：数值计数器设置值功能
1. **选择数值计数器**：找到任意一个数值计数器（如num1）
2. **记录当前值**：记录显示的当前值
3. **点击"设置值"按钮**：应该弹出设置值对话框
4. **验证对话框内容**：
   - ✅ 标题显示"设置当前值"
   - ✅ 消息显示"请输入数值计数器X的新值："
   - ✅ 输入框显示当前值并全选
   - ✅ 有"确定"和"取消"按钮

#### 测试用例3：序号计数器负数验证
1. **测试负数输入**：
   - 在任意序号计数器的设置值对话框中输入负数（如：-5）
   - 点击确定
   - 验证显示Toast提示"序号值不能为负数"
   - 验证对话框不关闭，允许重新输入

#### 测试用例4：有效值设置
1. **输入新值**：在输入框中输入一个有效数字（如：100）
2. **点击确定**：应该成功设置新值
3. **验证结果**：
   - ✅ 界面立即更新显示新值"当前值: 100"
   - ✅ 显示Toast提示"数值计数器X 已设置为: 100"
   - ✅ 对话框自动关闭

#### 测试用例5：无效值处理
1. **输入非数字**：输入字母或特殊字符
2. **点击确定**：应该显示错误提示
3. **验证结果**：
   - ✅ 显示Toast提示"请输入有效的数字"
   - ✅ 对话框不关闭，允许重新输入

#### 测试用例6：空值处理
1. **清空输入框**：删除所有内容
2. **点击确定**：应该显示错误提示
3. **验证结果**：
   - ✅ 显示Toast提示"请输入数值"
   - ✅ 对话框不关闭

#### 测试用例7：范围限制测试
1. **配置有限制的计数器**：
   - 先通过"配置数值计数器"设置某个计数器的最小值=0，最大值=50
2. **测试超出范围的值**：
   - 尝试设置值为-10（低于最小值）
   - 尝试设置值为100（高于最大值）
3. **验证结果**：
   - ✅ 实际设置的值被限制在范围内（0或50）
   - ✅ Toast提示包含"(受最小值/最大值限制)"

### 第三步：底部按钮功能测试

#### 测试用例8：重置按钮
1. **点击"重置"按钮**：应该重置所有计数器
2. **验证结果**：
   - ✅ 所有其他计数器（全局序号、每日序号、会话序号）重置为0
   - ✅ 所有数值计数器重置为各自的初始值
   - ✅ 界面立即更新显示新值
   - ✅ 显示相应的Toast提示

#### 测试用例9：取消按钮
1. **进行一些操作**：设置几个计数器的值
2. **点击"取消"按钮**：应该关闭对话框
3. **验证结果**：
   - ✅ 对话框立即关闭
   - ✅ 返回到上一个界面

#### 测试用例10：应用按钮
1. **进行一些操作**：设置几个计数器的值
2. **点击"应用"按钮**：应该保存更改但不关闭对话框
3. **验证结果**：
   - ✅ 显示Toast提示"更改已应用"
   - ✅ 对话框保持打开状态
   - ✅ 界面数据已更新
   - ✅ 可以继续进行其他操作

#### 测试用例11：保存按钮
1. **进行一些操作**：设置几个计数器的值
2. **点击"保存"按钮**：应该保存更改并关闭对话框
3. **验证结果**：
   - ✅ 显示Toast提示"更改已应用"
   - ✅ 对话框自动关闭
   - ✅ 返回到上一个界面

### 第四步：持久化验证

#### 测试用例12：数据持久化
1. **设置多个计数器值**：
   - 使用"设置值"功能修改全局序号、每日序号、会话序号
   - 使用"设置值"功能修改几个数值计数器
2. **保存并关闭**：点击"保存"按钮
3. **重新打开界面**：再次进入计数器管理界面
4. **验证结果**：
   - ✅ 所有序号计数器的设置值正确保存和显示
   - ✅ 所有数值计数器的设置值正确保存和显示
   - ✅ 数据在应用重启后仍然保持

#### 测试用例13：应用重启验证
1. **设置计数器值并保存**：包括序号计数器和数值计数器
2. **完全关闭应用**：从最近任务中移除应用
3. **重新启动应用**：重新打开GPSLogger
4. **检查计数器管理界面**：
   - ✅ 所有序号计数器的设置值正确恢复
   - ✅ 所有数值计数器的设置值正确恢复
   - ✅ 界面显示正常

## ✅ 成功标准

### 界面优化验证
- [ ] 底部四个按钮布局美观，间距均匀
- [ ] 按钮颜色区分明显，符合Material Design规范
- [ ] "重置所有计数器"文本已改为"重置"
- [ ] 所有按钮使用统一的样式风格

### 设置当前值功能验证
- [ ] "设置值"按钮在每个序号计数器（全局、每日、会话）旁边正确显示
- [ ] "设置值"按钮在每个数值计数器旁边正确显示
- [ ] 点击按钮弹出设置值对话框
- [ ] 输入框显示当前值并自动全选
- [ ] 有效数字输入正确设置计数器值
- [ ] 序号计数器负数输入显示"序号值不能为负数"错误提示
- [ ] 无效输入显示适当的错误提示
- [ ] 数值计数器范围限制正确应用
- [ ] 界面立即更新显示新值

### 按钮功能验证
- [ ] 重置按钮正确重置所有计数器
- [ ] 取消按钮关闭对话框不保存更改
- [ ] 应用按钮保存更改但保持对话框打开
- [ ] 保存按钮保存更改并关闭对话框
- [ ] 所有操作都有适当的用户反馈

### 数据持久化验证
- [ ] 序号计数器设置的值正确保存到SharedPreferences
- [ ] 数值计数器设置的值正确保存到SharedPreferences
- [ ] 重新打开界面时所有值正确加载
- [ ] 应用重启后所有数据正确恢复
- [ ] 每日序号设置值时同时更新日期信息

## 🚨 注意事项

1. **测试顺序**：建议按照测试用例顺序进行，确保全面覆盖
2. **数据备份**：测试前建议备份重要的计数器数据
3. **多次验证**：每个功能建议测试2-3次确认稳定性
4. **边界测试**：特别注意测试边界值和异常输入
5. **用户体验**：关注界面响应速度和操作流畅性

如果发现任何问题，请详细记录问题现象、复现步骤和预期行为。
