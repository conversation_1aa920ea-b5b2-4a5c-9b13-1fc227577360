<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="8dp"
    android:background="?android:attr/selectableItemBackground"
    android:clickable="true"
    android:focusable="true">

    <!-- 二级按钮容器 -->
    <LinearLayout
        android:id="@+id/secondary_button_container"
        android:layout_width="match_parent"
        android:layout_height="60dp"
        android:orientation="vertical"
        android:gravity="center"
        android:background="@drawable/secondary_button_background"
        android:padding="8dp"
        android:elevation="2dp">

        <!-- 按钮文本 -->
        <TextView
            android:id="@+id/secondary_button_text"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:text="二级按钮"
            android:textSize="12sp"
            android:textColor="@android:color/white"
            android:gravity="center"
            android:maxLines="2"
            android:ellipsize="end"
            android:textStyle="bold" />

        <!-- 触发模式图标 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center"
            android:layout_marginTop="2dp">

            <ImageView
                android:id="@+id/trigger_mode_icon"
                android:layout_width="12dp"
                android:layout_height="12dp"
                android:src="@drawable/ic_mic"
                android:alpha="0.8"
                android:scaleType="centerInside" />

            <TextView
                android:id="@+id/trigger_mode_text"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="语音"
                android:textSize="8sp"
                android:textColor="@android:color/white"
                android:alpha="0.8"
                android:layout_marginStart="2dp" />

        </LinearLayout>

    </LinearLayout>

    <!-- 模板变量指示器（可选） -->
    <TextView
        android:id="@+id/template_variable_indicator"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="{variable}"
        android:textSize="8sp"
        android:textColor="?android:attr/textColorSecondary"
        android:gravity="center"
        android:layout_marginTop="2dp"
        android:visibility="gone"
        android:maxLines="1"
        android:ellipsize="end" />

</LinearLayout>
