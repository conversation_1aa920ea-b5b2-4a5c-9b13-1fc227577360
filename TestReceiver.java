package com.mendhak.gpslogger.test;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.util.Log;

/**
 * 测试广播接收器，用于验证单点推送Intent
 */
public class TestReceiver extends BroadcastReceiver {
    private static final String TAG = "TestReceiver";

    @Override
    public void onReceive(Context context, Intent intent) {
        Log.i(TAG, "=== RECEIVED SINGLE POINT PUSH INTENT ===");
        Log.i(TAG, "Action: " + intent.getAction());
        
        if (intent.getData() != null) {
            Log.i(TAG, "Data URI: " + intent.getData().toString());
        }
        
        if (intent.getType() != null) {
            Log.i(TAG, "MIME Type: " + intent.getType());
        }
        
        // 记录所有附加数据
        if (intent.getExtras() != null) {
            for (String key : intent.getExtras().keySet()) {
                Object value = intent.getExtras().get(key);
                Log.i(TAG, "Extra: " + key + " = " + value);
            }
        }
        
        Log.i(TAG, "=== END SINGLE POINT PUSH INTENT ===");
    }
}
