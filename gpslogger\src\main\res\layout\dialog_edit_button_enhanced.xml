<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:padding="16dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <!-- 标题 -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="编辑按钮"
            android:textSize="18sp"
            android:textStyle="bold"
            android:textColor="?android:attr/textColorPrimary"
            android:paddingBottom="16dp"
            android:gravity="center" />

        <!-- 一级按钮设置 -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="一级按钮设置"
            android:textSize="14sp"
            android:textStyle="bold"
            android:textColor="?android:attr/textColorPrimary"
            android:paddingBottom="8dp" />

        <!-- 按钮文本 -->
        <com.google.android.material.textfield.TextInputLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="12dp"
            android:hint="按钮文本">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/edit_button_text"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:inputType="textMultiLine"
                android:maxLines="3" />

        </com.google.android.material.textfield.TextInputLayout>

        <!-- 按钮颜色 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginBottom="12dp">

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="按钮颜色"
                android:textSize="14sp"
                android:textColor="?android:attr/textColorPrimary" />

            <View
                android:id="@+id/color_preview"
                android:layout_width="32dp"
                android:layout_height="32dp"
                android:layout_marginEnd="8dp"
                android:background="@drawable/circle_background" />

            <Button
                android:id="@+id/btn_choose_color"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="选择颜色"
                android:textSize="12sp"
                style="@style/Widget.AppCompat.Button.Borderless" />

        </LinearLayout>

        <!-- 触发模式 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginBottom="16dp">

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="触发模式"
                android:textSize="14sp"
                android:textColor="?android:attr/textColorPrimary" />

            <Spinner
                android:id="@+id/spinner_trigger_mode"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:minWidth="120dp" />

        </LinearLayout>



        <!-- 分隔线 -->
        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="?android:attr/colorControlNormal"
            android:layout_marginVertical="16dp" />

        <!-- 二级按钮层次结构设置 -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="二级按钮层次结构"
            android:textSize="14sp"
            android:textStyle="bold"
            android:textColor="?android:attr/textColorPrimary"
            android:paddingBottom="8dp" />

        <!-- 启用二级按钮开关 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginBottom="12dp">

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="启用二级按钮"
                android:textSize="14sp"
                android:textColor="?android:attr/textColorPrimary" />

            <Switch
                android:id="@+id/switch_primary_button"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content" />

        </LinearLayout>

        <!-- 二级按钮设置区域 -->
        <LinearLayout
            android:id="@+id/secondary_button_settings"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:visibility="gone">

            <!-- 二级按钮计数 -->
            <TextView
                android:id="@+id/secondary_button_count_text"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="二级按钮 (0)"
                android:textSize="12sp"
                android:textColor="?android:attr/textColorSecondary"
                android:paddingBottom="8dp" />

            <!-- 二级按钮操作按钮 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginBottom="12dp">

                <Button
                    android:id="@+id/btn_add_secondary_button"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:layout_marginEnd="8dp"
                    android:text="添加二级按钮"
                    android:textSize="12sp"
                    android:drawableStart="@drawable/ic_add"
                    android:drawablePadding="4dp"
                    style="@style/Widget.AppCompat.Button.Borderless" />

                <Button
                    android:id="@+id/btn_manage_secondary_buttons"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:layout_marginStart="8dp"
                    android:text="管理二级按钮"
                    android:textSize="12sp"
                    android:drawableStart="@drawable/ic_settings"
                    android:drawablePadding="4dp"
                    style="@style/Widget.AppCompat.Button.Borderless" />

            </LinearLayout>

        </LinearLayout>

        <!-- 操作按钮 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="end"
            android:paddingTop="16dp">

            <Button
                android:id="@+id/btn_cancel"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="取消"
                android:layout_marginEnd="8dp"
                style="@style/Widget.AppCompat.Button.Borderless" />

            <Button
                android:id="@+id/btn_save"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="保存"
                android:background="@drawable/button_primary_background"
                android:textColor="@android:color/white" />

        </LinearLayout>

    </LinearLayout>

</ScrollView>
