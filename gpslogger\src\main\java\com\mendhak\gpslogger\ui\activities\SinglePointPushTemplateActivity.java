/*
 * Copyright (C) 2024 mendhak
 *
 * This file is part of GPSLogger for Android.
 *
 * GPSLogger is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 2 of the License, or
 * (at your option) any later version.
 *
 * GPSLogger is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with GPSLogger.  If not, see <http://www.gnu.org/licenses/>.
 */

package com.mendhak.gpslogger.ui.activities;

import android.content.Intent;
import android.os.Bundle;
import android.view.MenuItem;
import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.widget.Toolbar;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentTransaction;
import com.mendhak.gpslogger.R;
import com.mendhak.gpslogger.common.slf4j.Logs;
import com.mendhak.gpslogger.ui.fragments.settings.SinglePointPushTemplateFragment;
import org.slf4j.Logger;

/**
 * Activity for managing single point push templates
 */
public class SinglePointPushTemplateActivity extends AppCompatActivity {
    
    private static final Logger LOG = Logs.of(SinglePointPushTemplateActivity.class);
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_annotation_template);
        
        LOG.debug("Creating SinglePointPushTemplateActivity");
        
        // Setup toolbar
        Toolbar toolbar = findViewById(R.id.toolbar);
        setSupportActionBar(toolbar);
        
        if (getSupportActionBar() != null) {
            getSupportActionBar().setDisplayHomeAsUpEnabled(true);
            getSupportActionBar().setDisplayShowHomeEnabled(true);
            getSupportActionBar().setTitle("单点推送模板设置");
        }
        
        // Load template fragment
        if (savedInstanceState == null) {
            loadTemplateFragment();
        }
    }
    
    /**
     * Load the single point push template fragment
     */
    private void loadTemplateFragment() {
        FragmentManager fragmentManager = getSupportFragmentManager();
        FragmentTransaction transaction = fragmentManager.beginTransaction();
        
        Fragment fragment = new SinglePointPushTemplateFragment();
        transaction.replace(R.id.fragment_container, fragment);
        transaction.commit();
        
        LOG.debug("Loaded SinglePointPushTemplateFragment");
    }
    
    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        if (item.getItemId() == android.R.id.home) {
            onBackPressed();
            return true;
        }
        return super.onOptionsItemSelected(item);
    }
    
    @Override
    public void onBackPressed() {
        // Return to settings with result
        Intent resultIntent = new Intent();
        setResult(RESULT_OK, resultIntent);
        super.onBackPressed();
    }
}
