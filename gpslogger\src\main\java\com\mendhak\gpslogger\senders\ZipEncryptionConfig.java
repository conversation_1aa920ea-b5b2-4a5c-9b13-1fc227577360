/*
 * Copyright (C) 2016 mendhak
 *
 * This file is part of GPSLogger for Android.
 *
 * GPSLogger for Android is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 2 of the License, or
 * (at your option) any later version.
 *
 * GPSLogger for Android is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with GPSLogger for Android.  If not, see <http://www.gnu.org/licenses/>.
 */

package com.mendhak.gpslogger.senders;

import com.mendhak.gpslogger.common.PreferenceHelper;
import com.mendhak.gpslogger.common.slf4j.Logs;
import org.slf4j.Logger;

/**
 * Configuration class for zip file encryption settings
 */
public class ZipEncryptionConfig {
    private static final Logger LOG = Logs.of(ZipEncryptionConfig.class);
    
    private final boolean encryptionEnabled;
    private final String password;
    
    public ZipEncryptionConfig(PreferenceHelper prefs) {
        this.encryptionEnabled = prefs.shouldEncryptZipFile();
        this.password = encryptionEnabled ? prefs.getZipPassword() : null;
        
        LOG.debug("Zip encryption config - enabled: {}, password set: {}", 
                 encryptionEnabled, password != null && !password.trim().isEmpty());
    }
    
    /**
     * Check if the configuration is valid for encryption
     */
    public boolean isValid() {
        if (!encryptionEnabled) {
            return true; // Valid for non-encrypted zip
        }
        
        boolean passwordValid = password != null && !password.trim().isEmpty();
        if (!passwordValid) {
            LOG.warn("Zip encryption enabled but password is empty or null");
        }
        
        return passwordValid;
    }
    
    /**
     * Whether zip encryption is enabled
     */
    public boolean isEncryptionEnabled() {
        return encryptionEnabled;
    }
    
    /**
     * Get the encryption password (null if encryption disabled)
     */
    public String getPassword() {
        return password;
    }
    
    /**
     * Get a safe password for logging (masked)
     */
    public String getPasswordForLogging() {
        if (password == null || password.isEmpty()) {
            return "null";
        }
        return "***" + password.length() + " chars***";
    }
}
