/*
 * Copyright (C) 2024 mendhak
 *
 * This file is part of GPSLogger for Android.
 *
 * GPSLogger is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 2 of the License, or
 * (at your option) any later version.
 *
 * GPSLogger is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with GPSLogger.  If not, see <http://www.gnu.org/licenses/>.
 */

package com.mendhak.gpslogger.ui.adapters;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.mendhak.gpslogger.R;
import com.mendhak.gpslogger.ui.models.GroupIcon;

import java.util.List;

/**
 * 图标选择器适配器
 */
public class IconSelectorAdapter extends RecyclerView.Adapter<IconSelectorAdapter.IconViewHolder> {
    
    private List<GroupIcon> icons;
    private OnIconSelectedListener listener;
    private int selectedPosition = -1;
    
    public interface OnIconSelectedListener {
        void onIconSelected(GroupIcon icon, int position);
    }
    
    public IconSelectorAdapter(List<GroupIcon> icons) {
        this.icons = icons;
    }
    
    public void setOnIconSelectedListener(OnIconSelectedListener listener) {
        this.listener = listener;
    }
    
    public void setSelectedIcon(GroupIcon selectedIcon) {
        int newPosition = -1;
        if (selectedIcon != null) {
            for (int i = 0; i < icons.size(); i++) {
                if (icons.get(i).equals(selectedIcon)) {
                    newPosition = i;
                    break;
                }
            }
        }
        
        int oldPosition = selectedPosition;
        selectedPosition = newPosition;
        
        if (oldPosition != -1) {
            notifyItemChanged(oldPosition);
        }
        if (newPosition != -1) {
            notifyItemChanged(newPosition);
        }
    }
    
    public GroupIcon getSelectedIcon() {
        if (selectedPosition >= 0 && selectedPosition < icons.size()) {
            return icons.get(selectedPosition);
        }
        return null;
    }
    
    @NonNull
    @Override
    public IconViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext())
                .inflate(R.layout.item_icon_selector, parent, false);
        return new IconViewHolder(view);
    }
    
    @Override
    public void onBindViewHolder(@NonNull IconViewHolder holder, int position) {
        GroupIcon icon = icons.get(position);
        holder.bind(icon, position == selectedPosition);
    }
    
    @Override
    public int getItemCount() {
        return icons != null ? icons.size() : 0;
    }
    
    class IconViewHolder extends RecyclerView.ViewHolder {
        private ImageView iconImage;
        private TextView iconName;
        private View iconBackground;
        private View selectionIndicator;
        
        public IconViewHolder(@NonNull View itemView) {
            super(itemView);
            
            iconImage = itemView.findViewById(R.id.icon_image);
            iconName = itemView.findViewById(R.id.icon_name);
            iconBackground = itemView.findViewById(R.id.icon_background);
            selectionIndicator = itemView.findViewById(R.id.selection_indicator);
            
            itemView.setOnClickListener(v -> {
                int position = getAdapterPosition();
                if (position != RecyclerView.NO_POSITION && listener != null) {
                    // 更新选中状态
                    int oldPosition = selectedPosition;
                    selectedPosition = position;
                    
                    if (oldPosition != -1) {
                        notifyItemChanged(oldPosition);
                    }
                    notifyItemChanged(position);
                    
                    // 通知监听器
                    listener.onIconSelected(icons.get(position), position);
                }
            });
        }
        
        public void bind(GroupIcon icon, boolean isSelected) {
            iconImage.setImageResource(icon.getIconResource());
            iconName.setText(icon.getIconName());
            
            // 设置选中状态
            selectionIndicator.setVisibility(isSelected ? View.VISIBLE : View.GONE);
        }
    }
}
