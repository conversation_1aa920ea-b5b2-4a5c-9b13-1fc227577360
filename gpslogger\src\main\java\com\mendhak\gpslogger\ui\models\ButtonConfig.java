/*
 * Copyright (C) 2024 mendhak
 *
 * This file is part of GPSLogger for Android.
 *
 * GPSLogger is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 2 of the License, or
 * (at your option) any later version.
 *
 * GPSLogger is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with GPSLogger.  If not, see <http://www.gnu.org/licenses/>.
 */

package com.mendhak.gpslogger.ui.models;

import org.json.JSONException;
import org.json.JSONObject;

/**
 * 按钮配置数据结构
 * 支持文本模式、语音模式、计数器模式等不同类型的按钮配置
 */
public class ButtonConfig {
    
    public enum ButtonType {
        TEXT,    // 文本模式：显示"通过"/"不通过"文字
        VOICE,   // 语音模式：显示语音图标，点击播放语音
        COUNTER  // 计数器模式：显示数字，点击递增计数
    }
    
    private ButtonType type = ButtonType.TEXT;
    private String passText = "通过";
    private String failText = "不过";
    private int passColor = 0xFF4CAF50; // 绿色
    private int failColor = 0xFFF44336; // 红色
    private String passVoiceText = "通过";
    private String failVoiceText = "不过";
    private int maxCount = 10;
    private boolean enabled = true;
    
    public ButtonConfig() {
        // 默认构造函数
    }
    
    /**
     * 从JSON字符串创建ButtonConfig对象
     */
    public static ButtonConfig fromJson(String jsonString) throws JSONException {
        ButtonConfig config = new ButtonConfig();
        JSONObject json = new JSONObject(jsonString);
        
        // 解析按钮类型
        if (json.has("type")) {
            String typeStr = json.getString("type");
            config.type = ButtonType.valueOf(typeStr);
        }
        
        // 解析文本配置
        if (json.has("passText")) {
            config.passText = json.getString("passText");
        }
        if (json.has("failText")) {
            config.failText = json.getString("failText");
        }
        
        // 解析颜色配置
        if (json.has("passColor")) {
            config.passColor = json.getInt("passColor");
        }
        if (json.has("failColor")) {
            config.failColor = json.getInt("failColor");
        }
        
        // 解析语音配置
        if (json.has("passVoiceText")) {
            config.passVoiceText = json.getString("passVoiceText");
        }
        if (json.has("failVoiceText")) {
            config.failVoiceText = json.getString("failVoiceText");
        }
        
        // 解析计数器配置
        if (json.has("maxCount")) {
            config.maxCount = json.getInt("maxCount");
        }
        
        // 解析启用状态
        if (json.has("enabled")) {
            config.enabled = json.getBoolean("enabled");
        }
        
        return config;
    }
    
    /**
     * 转换为JSON字符串
     */
    public String toJson() throws JSONException {
        JSONObject json = new JSONObject();
        
        json.put("type", type.name());
        json.put("passText", passText);
        json.put("failText", failText);
        json.put("passColor", passColor);
        json.put("failColor", failColor);
        json.put("passVoiceText", passVoiceText);
        json.put("failVoiceText", failVoiceText);
        json.put("maxCount", maxCount);
        json.put("enabled", enabled);
        
        return json.toString();
    }
    
    // Getters and Setters
    public ButtonType getType() {
        return type;
    }
    
    public void setType(ButtonType type) {
        this.type = type;
    }
    
    public String getPassText() {
        return passText;
    }
    
    public void setPassText(String passText) {
        this.passText = passText;
    }
    
    public String getFailText() {
        return failText;
    }
    
    public void setFailText(String failText) {
        this.failText = failText;
    }
    
    public int getPassColor() {
        return passColor;
    }
    
    public void setPassColor(int passColor) {
        this.passColor = passColor;
    }
    
    public int getFailColor() {
        return failColor;
    }
    
    public void setFailColor(int failColor) {
        this.failColor = failColor;
    }
    
    public String getPassVoiceText() {
        return passVoiceText;
    }
    
    public void setPassVoiceText(String passVoiceText) {
        this.passVoiceText = passVoiceText;
    }
    
    public String getFailVoiceText() {
        return failVoiceText;
    }
    
    public void setFailVoiceText(String failVoiceText) {
        this.failVoiceText = failVoiceText;
    }
    
    public int getMaxCount() {
        return maxCount;
    }
    
    public void setMaxCount(int maxCount) {
        this.maxCount = maxCount;
    }
    
    public boolean isEnabled() {
        return enabled;
    }
    
    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }
    
    /**
     * 创建默认的文本模式配置
     */
    public static ButtonConfig createDefaultTextConfig() {
        ButtonConfig config = new ButtonConfig();
        config.setType(ButtonType.TEXT);
        config.setPassText("通过");
        config.setFailText("不过");
        config.setPassColor(0xFF4CAF50);
        config.setFailColor(0xFFF44336);
        return config;
    }
    
    /**
     * 创建默认的语音模式配置
     */
    public static ButtonConfig createDefaultVoiceConfig() {
        ButtonConfig config = new ButtonConfig();
        config.setType(ButtonType.VOICE);
        config.setPassVoiceText("通过");
        config.setFailVoiceText("不过");
        config.setPassColor(0xFF4CAF50);
        config.setFailColor(0xFFF44336);
        return config;
    }
    
    /**
     * 创建默认的计数器模式配置
     */
    public static ButtonConfig createDefaultCounterConfig() {
        ButtonConfig config = new ButtonConfig();
        config.setType(ButtonType.COUNTER);
        config.setMaxCount(10);
        config.setPassColor(0xFF4CAF50);
        config.setFailColor(0xFFF44336);
        return config;
    }
}
