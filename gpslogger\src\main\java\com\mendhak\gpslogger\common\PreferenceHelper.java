/*
 * Copyright (C) 2016 mendhak
 *
 * This file is part of GPSLogger for Android.
 *
 * GPSLogger for Android is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 2 of the License, or
 * (at your option) any later version.
 *
 * GPSLogger for Android is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with GPSLogger for Android.  If not, see <http://www.gnu.org/licenses/>.
 */

package com.mendhak.gpslogger.common;


import android.content.Context;
import android.content.SharedPreferences;
import androidx.preference.PreferenceManager;
import androidx.security.crypto.EncryptedSharedPreferences;
import androidx.security.crypto.MasterKeys;
import com.mendhak.gpslogger.R;
import com.mendhak.gpslogger.common.slf4j.Logs;
import com.mendhak.gpslogger.loggers.Files;
import org.slf4j.Logger;

import java.io.*;
import java.lang.annotation.Annotation;
import java.lang.reflect.Method;
import java.util.*;

public class PreferenceHelper {

    private static PreferenceHelper instance = null;
    private SharedPreferences prefs;
    private static final Logger LOG = Logs.of(PreferenceHelper.class);

    /**
     * Use PreferenceHelper.getInstance()
     */
    private PreferenceHelper(){

    }

    public static PreferenceHelper getInstance(){
        if(instance==null){
            instance = new PreferenceHelper();
            instance.prefs = PreferenceManager.getDefaultSharedPreferences(AppSettings.getInstance().getApplicationContext());
        }

        return instance;
    }

    /**
     * Whether to auto send to Dropbox
     */
    @ProfilePreference(name= PreferenceNames.AUTOSEND_DROPBOX_ENABLED)
    public  boolean isDropboxAutoSendEnabled() {
        return prefs.getBoolean(PreferenceNames.AUTOSEND_DROPBOX_ENABLED, false);
    }

    /**
     * Long lived tokens were deprecated in 2021, but some users might still have these stored.
     * https://developers.dropbox.com/oauth-guide
     */
    public String getDropboxLongLivedAccessKey() {
        return prefs.getString(PreferenceNames.DROPBOX_LONG_LIVED_ACCESS_TOKEN, null);
    }

    public void setDropboxLongLivedAccessKey(String key) {
        prefs.edit().putString(PreferenceNames.DROPBOX_LONG_LIVED_ACCESS_TOKEN, key).apply();
    }


    public String getDropboxRefreshToken(){
        return prefs.getString(PreferenceNames.DROPBOX_REFRESH_TOKEN, null);
    }

    public void setDropboxRefreshToken(String refreshToken){
        prefs.edit().putString(PreferenceNames.DROPBOX_REFRESH_TOKEN, refreshToken).apply();
    }

    @ProfilePreference(name=PreferenceNames.AUTOSEND_GOOGLE_DRIVE_ENABLED)
    public boolean isGoogleDriveAutoSendEnabled() {
        return prefs.getBoolean(PreferenceNames.AUTOSEND_GOOGLE_DRIVE_ENABLED, false);
    }

    public String getGoogleDriveAuthState(){
        return prefs.getString(PreferenceNames.GOOGLE_DRIVE_AUTH_STATE, null);
    }

    public void setGoogleDriveAuthState(String auth_state_json_serialized){
        prefs.edit().putString(PreferenceNames.GOOGLE_DRIVE_AUTH_STATE, auth_state_json_serialized).apply();
    }

    @ProfilePreference(name=PreferenceNames.GOOGLE_DRIVE_FOLDER_PATH)
    public String getGoogleDriveFolderPath() {
        return prefs.getString(PreferenceNames.GOOGLE_DRIVE_FOLDER_PATH, "GPSLogger for Android");
    }

    public void setGoogleDriveFolderPath(String folderPath){
        prefs.edit().putString(PreferenceNames.GOOGLE_DRIVE_FOLDER_PATH, folderPath).apply();
    }

    /**
     * Whether automatic sending to email is enabled
     */
    @ProfilePreference(name= PreferenceNames.AUTOSEND_EMAIL_ENABLED)
    public boolean isEmailAutoSendEnabled() {
        return prefs.getBoolean(PreferenceNames.AUTOSEND_EMAIL_ENABLED, false);
    }


    /**
     * SMTP Server to use when sending emails
     */
    @ProfilePreference(name= PreferenceNames.EMAIL_SMTP_SERVER)
    public String getSmtpServer() {
        return prefs.getString(PreferenceNames.EMAIL_SMTP_SERVER, "");
    }

    /**
     * Sets SMTP Server to use when sending emails
     */
    public void setSmtpServer(String smtpServer) {
        prefs.edit().putString(PreferenceNames.EMAIL_SMTP_SERVER, smtpServer).apply();
    }

    /**
     * SMTP Port to use when sending emails
     */
    @ProfilePreference(name= PreferenceNames.EMAIL_SMTP_PORT)
    public String getSmtpPort() {
        return prefs.getString(PreferenceNames.EMAIL_SMTP_PORT, "25");
    }

    public void setSmtpPort(String port) {
        prefs.edit().putString(PreferenceNames.EMAIL_SMTP_PORT, port).apply();
    }

    /**
     * SMTP Username to use when sending emails
     */
    @ProfilePreference(name= PreferenceNames.EMAIL_SMTP_USERNAME)
    public String getSmtpUsername() {
        return prefs.getString(PreferenceNames.EMAIL_SMTP_USERNAME, "");
    }

    public void setSmtpUsername(String user){
        prefs.edit().putString(PreferenceNames.EMAIL_SMTP_USERNAME, user).apply();
    }

    /**
     * SMTP Password to use when sending emails
     */
    @ProfilePreference(name= PreferenceNames.EMAIL_SMTP_PASSWORD)
    public String getSmtpPassword() {
        return prefs.getString(PreferenceNames.EMAIL_SMTP_PASSWORD, "");
    }

    public void setSmtpPassword(String pass){
        prefs.edit().putString(PreferenceNames.EMAIL_SMTP_PASSWORD, pass).apply();
    }

    /**
     * Whether SSL is enabled when sending emails
     */
    @ProfilePreference(name= PreferenceNames.EMAIL_SMTP_SSL)
    public boolean isSmtpSsl() {
        return prefs.getBoolean(PreferenceNames.EMAIL_SMTP_SSL, true);
    }

    /**
     * Sets whether SSL is enabled when sending emails
     */
    public void setSmtpSsl(boolean smtpSsl) {
        prefs.edit().putBoolean(PreferenceNames.EMAIL_SMTP_SSL, smtpSsl).apply();
    }


    /**
     * Email addresses to send to
     */
    @ProfilePreference(name= PreferenceNames.EMAIL_TARGET)
    public String getAutoEmailTargets() {
        return prefs.getString(PreferenceNames.EMAIL_TARGET, "");
    }

    public void setAutoEmailTargets(String emailCsv) {
        prefs.edit().putString(PreferenceNames.EMAIL_TARGET, emailCsv).apply();
    }


    /**
     * SMTP from address to use
     */
    @ProfilePreference(name= PreferenceNames.EMAIL_FROM)
    private String getSmtpFrom() {
        return prefs.getString(PreferenceNames.EMAIL_FROM, "");
    }

    public void setSmtpFrom(String from) {
        prefs.edit().putString(PreferenceNames.EMAIL_FROM, from).apply();
    }

    /**
     * The from address to use when sending an email, uses {@link #getSmtpUsername()} if {@link #getSmtpFrom()} is not specified
     */
    public String getSmtpSenderAddress() {
        if (getSmtpFrom() != null && getSmtpFrom().length() > 0) {
            return getSmtpFrom();
        }

        return getSmtpUsername();
    }


    /**
     * FTP Server name for auto send
     */
    @ProfilePreference(name= PreferenceNames.FTP_SERVER)
    public String getFtpServerName() {
        return prefs.getString(PreferenceNames.FTP_SERVER, "");
    }

    public void setFtpServerName(String server){
        prefs.edit().putString(PreferenceNames.FTP_SERVER, server).apply();
    }

    /**
     * FTP Port for auto send
     */
    @ProfilePreference(name= PreferenceNames.FTP_PORT)
    public int getFtpPort() {
        return Strings.toInt(prefs.getString(PreferenceNames.FTP_PORT, "21"), 21);
    }

    public void setFtpPort(String port){
        prefs.edit().putString(PreferenceNames.FTP_PORT, port).apply();
    }

    /**
     * FTP Username for auto send
     */
    @ProfilePreference(name= PreferenceNames.FTP_USERNAME)
    public String getFtpUsername() {
        return prefs.getString(PreferenceNames.FTP_USERNAME, "");
    }


    public void setFtpUsername(String user){
        prefs.edit().putString(PreferenceNames.FTP_USERNAME, user).apply();
    }

    /**
     * FTP Password for auto send
     */
    @ProfilePreference(name= PreferenceNames.FTP_PASSWORD)
    public String getFtpPassword() {
        return prefs.getString(PreferenceNames.FTP_PASSWORD, "");
    }

    public void setFtpPassword(String pass){
        prefs.edit().putString(PreferenceNames.FTP_PASSWORD, pass).apply();
    }

    /**
     * Whether to use FTPS
     */
    @ProfilePreference(name= PreferenceNames.FTP_USE_FTPS)
    public boolean shouldFtpUseFtps() {
        return prefs.getBoolean(PreferenceNames.FTP_USE_FTPS, false);
    }


    /**
     * FTP protocol to use (SSL or TLS)
     */
    @ProfilePreference(name= PreferenceNames.FTP_SSLORTLS)
    public String getFtpProtocol() {
        return prefs.getString(PreferenceNames.FTP_SSLORTLS, "");
    }


    /**
     * Whether to use FTP Implicit mode for auto send
     */
    @ProfilePreference(name= PreferenceNames.FTP_IMPLICIT)
    public boolean isFtpImplicit() {
        return prefs.getBoolean(PreferenceNames.FTP_IMPLICIT, false);
    }


    /**
     * Whether to auto send to FTP target
     */
    @ProfilePreference(name= PreferenceNames.AUTOSEND_FTP_ENABLED)
    public boolean isFtpAutoSendEnabled() {
        return prefs.getBoolean(PreferenceNames.AUTOSEND_FTP_ENABLED, false);
    }


    /**
     * FTP Directory on the server for auto send
     */
    @ProfilePreference(name= PreferenceNames.FTP_DIRECTORY)
    public String getFtpDirectory() {
        return prefs.getString(PreferenceNames.FTP_DIRECTORY, "GPSLogger");
    }

    public void setFtpDirectory(String dir){
        prefs.edit().putString(PreferenceNames.FTP_DIRECTORY, dir).apply();
    }




    /**
     * GPS Logger folder path on phone.  Falls back to {@link Files#storageFolder(Context)} if nothing specified.
     */
    @ProfilePreference(name= PreferenceNames.GPSLOGGER_FOLDER)
    public String getGpsLoggerFolder() {
        return prefs.getString(PreferenceNames.GPSLOGGER_FOLDER, Files.storageFolder(AppSettings.getInstance().getApplicationContext()).getAbsolutePath());
    }


    /**
     * Sets GPS Logger folder path
     */
    public void setGpsLoggerFolder(String folderPath) {
        prefs.edit().putString(PreferenceNames.GPSLOGGER_FOLDER, folderPath).apply();
    }



    /**
     * The minimum seconds interval between logging points
     */
    @ProfilePreference(name= PreferenceNames.MINIMUM_INTERVAL)
    public int getMinimumLoggingInterval() {
        return Strings.toInt(prefs.getString(PreferenceNames.MINIMUM_INTERVAL, "60"), 60);
    }

    /**
     * Sets the minimum time interval between logging points
     *
     * @param minimumSeconds - in seconds
     */
    public void setMinimumLoggingInterval(int minimumSeconds) {
        SharedPreferences.Editor editor = prefs.edit();
        editor.putString(PreferenceNames.MINIMUM_INTERVAL, String.valueOf(minimumSeconds));
        editor.apply();
    }


    /**
     * The minimum distance, in meters, to have traveled before a point is recorded
     */
    @ProfilePreference(name= PreferenceNames.MINIMUM_DISTANCE)
    public int getMinimumDistanceInterval() {
        return (Strings.toInt(prefs.getString(PreferenceNames.MINIMUM_DISTANCE, "0"), 0));
    }

    /**
     * Sets the minimum distance to have traveled before a point is recorded
     *
     * @param distanceBeforeLogging - in meters
     */
    public void setMinimumDistanceInMeters(int distanceBeforeLogging) {
        prefs.edit().putString(PreferenceNames.MINIMUM_DISTANCE, String.valueOf(distanceBeforeLogging)).apply();
    }


    /**
     * The minimum accuracy of a point before the point is recorded, in meters
     */
    @ProfilePreference(name= PreferenceNames.MINIMUM_ACCURACY)
    public int getMinimumAccuracy() {
        return (Strings.toInt(prefs.getString(PreferenceNames.MINIMUM_ACCURACY, "40"), 40));
    }

    public void setMinimumAccuracy(int minimumAccuracy){
        prefs.edit().putString(PreferenceNames.MINIMUM_ACCURACY, String.valueOf(minimumAccuracy)).apply();
    }


    /**
     * Whether to keep GPS on between fixes
     */
    @ProfilePreference(name= PreferenceNames.KEEP_GPS_ON_BETWEEN_FIXES)
    public boolean shouldKeepGPSOnBetweenFixes() {
        return prefs.getBoolean(PreferenceNames.KEEP_GPS_ON_BETWEEN_FIXES, false);
    }

    /**
     * Set whether to keep GPS on between fixes
     */
    public void setShouldKeepGPSOnBetweenFixes(boolean keepFix) {
        prefs.edit().putBoolean(PreferenceNames.KEEP_GPS_ON_BETWEEN_FIXES, keepFix).apply();
    }


    /**
     * How long to keep retrying for a fix if one with the user-specified accuracy hasn't been found
     */
    @ProfilePreference(name= PreferenceNames.LOGGING_RETRY_TIME)
    public int getLoggingRetryPeriod() {
        return (Strings.toInt(prefs.getString(PreferenceNames.LOGGING_RETRY_TIME, "60"), 60));
    }


    /**
     * Sets how long to keep trying for an accurate fix
     *
     * @param retryInterval in seconds
     */
    public void setLoggingRetryPeriod(int retryInterval) {
        prefs.edit().putString(PreferenceNames.LOGGING_RETRY_TIME, String.valueOf(retryInterval)).apply();
    }

    @ProfilePreference(name=PreferenceNames.LOGGING_RETRY_SHOULD_GET_BEST_POSSIBLE_ACCURACY)
    public boolean shouldGetBestPossibleAccuracy() {
        return prefs.getBoolean(PreferenceNames.LOGGING_RETRY_SHOULD_GET_BEST_POSSIBLE_ACCURACY, false);
    }

    public void setShouldGetBestPossibleAccuracy(boolean value){
        prefs.edit().putBoolean(PreferenceNames.LOGGING_RETRY_SHOULD_GET_BEST_POSSIBLE_ACCURACY, value).apply();
    }

    /**
     * How long to keep retrying for an accurate point before giving up
     */
    @ProfilePreference(name= PreferenceNames.ABSOLUTE_TIMEOUT)
    public int getAbsoluteTimeoutForAcquiringPosition() {
        return (Strings.toInt(prefs.getString(PreferenceNames.ABSOLUTE_TIMEOUT, "120"), 120));
    }

    /**
     * Sets how long to keep retrying for an accurate point before giving up
     *
     * @param absoluteTimeout in seconds
     */
    public void setAbsoluteTimeoutForAcquiringPosition(int absoluteTimeout) {
        prefs.edit().putString(PreferenceNames.ABSOLUTE_TIMEOUT, String.valueOf(absoluteTimeout)).apply();
    }
    
    /**
     * Reduce redundant passive location updates by adjusting the minimum collection interval (in seconds).
     */
    @ProfilePreference(name= PreferenceNames.PASSIVE_FILTER_INTERVAL)
    public int getPassiveFilterInterval() {
        return (Strings.toInt(prefs.getString(PreferenceNames.PASSIVE_FILTER_INTERVAL, "1"), 1));
    }

    /**
     * Sets Reduce redundant passive location updates by adjusting the minimum collection interval (in seconds).
     */
    public void setPassiveFilterInterval(int filterInterval) {
        prefs.edit().putString(PreferenceNames.PASSIVE_FILTER_INTERVAL, String.valueOf(filterInterval)).apply();
    }

    /**
     * Whether to start logging on application launch
     */
    @ProfilePreference(name= PreferenceNames.START_LOGGING_ON_APP_LAUNCH)
    public boolean shouldStartLoggingOnAppLaunch() {
        return prefs.getBoolean(PreferenceNames.START_LOGGING_ON_APP_LAUNCH, false);
    }

    /**
     * Whether to stop logging on application launch
     */
    @ProfilePreference(name= PreferenceNames.STOP_LOGGING_ON_APP_LAUNCH)
    public boolean shouldStopLoggingOnAppLaunch() {
        return prefs.getBoolean(PreferenceNames.STOP_LOGGING_ON_APP_LAUNCH, false);
    }

    /**
     * Whether to start logging when phone is booted up
     */
    @ProfilePreference(name= PreferenceNames.START_LOGGING_ON_BOOTUP)
    public boolean shouldStartLoggingOnBootup() {
        return prefs.getBoolean(PreferenceNames.START_LOGGING_ON_BOOTUP, false);
    }


    /**
     * Which navigation item the user selected
     */
    public int getUserSelectedNavigationItem() {
        return Strings.toInt(prefs.getString(PreferenceNames.SELECTED_NAVITEM, "0"), 0);
    }

    /**
     * Sets which navigation item the user selected
     */
    public void setUserSelectedNavigationItem(int position) {
        SharedPreferences.Editor editor = prefs.edit();
        editor.putString(PreferenceNames.SELECTED_NAVITEM, String.valueOf(position));
        editor.apply();
    }

    /**
     * Whether to hide the buttons when displaying the app notification
     */
    @ProfilePreference(name= PreferenceNames.HIDE_NOTIFICATION_BUTTONS)
    public boolean shouldHideNotificationButtons() {
        return prefs.getBoolean(PreferenceNames.HIDE_NOTIFICATION_BUTTONS, false);
    }


    @ProfilePreference(name=PreferenceNames.HIDE_NOTIFICATION_FROM_STATUS_BAR)
    public boolean shouldHideNotificationFromStatusBar(){
        return prefs.getBoolean(PreferenceNames.HIDE_NOTIFICATION_FROM_STATUS_BAR, false);
    }

    @ProfilePreference(name=PreferenceNames.HIDE_NOTIFICATION_FROM_LOCK_SCREEN)
    public boolean shouldHideNotificationFromLockScreen(){
        return prefs.getBoolean(PreferenceNames.HIDE_NOTIFICATION_FROM_LOCK_SCREEN, true);
    }


    /**
     * Whether to display certain values using imperial units
     * Always returns false - imperial units feature removed
     */
    public boolean shouldDisplayImperialUnits() {
        return false;
    }

    @ProfilePreference(name=PreferenceNames.APP_THEME_SETTING)
    public String getAppThemeSetting() {
        return prefs.getString(PreferenceNames.APP_THEME_SETTING, "system");
    }

    /**
     * Whether haptic feedback is enabled for button clicks
     */
    @ProfilePreference(name=PreferenceNames.HAPTIC_FEEDBACK_ENABLED)
    public boolean isHapticFeedbackEnabled() {
        return prefs.getBoolean(PreferenceNames.HAPTIC_FEEDBACK_ENABLED, true);
    }

    /**
     * Set whether haptic feedback is enabled
     */
    public void setHapticFeedbackEnabled(boolean enabled) {
        SharedPreferences.Editor editor = prefs.edit();
        editor.putBoolean(PreferenceNames.HAPTIC_FEEDBACK_ENABLED, enabled);
        editor.apply();
        LOG.debug("Haptic feedback enabled set to: {}", enabled);
    }

    /**
     * Get haptic feedback intensity setting
     */
    @ProfilePreference(name=PreferenceNames.HAPTIC_FEEDBACK_INTENSITY)
    public String getHapticFeedbackIntensity() {
        return prefs.getString(PreferenceNames.HAPTIC_FEEDBACK_INTENSITY, "MEDIUM");
    }

    /**
     * Set haptic feedback intensity
     */
    public void setHapticFeedbackIntensity(String intensity) {
        SharedPreferences.Editor editor = prefs.edit();
        editor.putString(PreferenceNames.HAPTIC_FEEDBACK_INTENSITY, intensity);
        editor.apply();
        LOG.debug("Haptic feedback intensity set to: {}", intensity);
    }

    /**
     * Display format to use for lat long coordinates on screen
     * DEGREES_MINUTES_SECONDS, DEGREES_DECIMAL_MINUTES, DECIMAL_DEGREES
     */
    @ProfilePreference(name=PreferenceNames.LATLONG_DISPLAY_FORMAT)
    public PreferenceNames.DegreesDisplayFormat getDisplayLatLongFormat(){
        String chosenValue = prefs.getString(PreferenceNames.LATLONG_DISPLAY_FORMAT,"DEGREES_MINUTES_SECONDS");
        return PreferenceNames.DegreesDisplayFormat.valueOf(chosenValue);
    }

    public void setDisplayLatLongFormat(PreferenceNames.DegreesDisplayFormat displayFormat){
        prefs.edit().putString(PreferenceNames.LATLONG_DISPLAY_FORMAT, displayFormat.toString()).apply();
    }


    /**
     * Whether to log to KML file
     */
    @ProfilePreference(name= PreferenceNames.LOG_TO_KML)
    public boolean shouldLogToKml() {
        return prefs.getBoolean(PreferenceNames.LOG_TO_KML, false);
    }


    /**
     * Whether to log to GPX file
     */
    @ProfilePreference(name= PreferenceNames.LOG_TO_GPX)
    public boolean shouldLogToGpx() {
        return prefs.getBoolean(PreferenceNames.LOG_TO_GPX, true);
    }

    /**
     * Whether to log to GPX in GPX 1.0 or 1.1 format
     */
    @ProfilePreference(name= PreferenceNames.LOG_AS_GPX_11)
    public boolean shouldLogAsGpx11() {
        return prefs.getBoolean(PreferenceNames.LOG_AS_GPX_11, false);
    }


    /**
     * Whether to log to a CSV file
     */
    @ProfilePreference(name= PreferenceNames.LOG_TO_CSV)
    public boolean shouldLogToCSV() {
        return prefs.getBoolean(PreferenceNames.LOG_TO_CSV, false);
    }

    public void setShouldLogToCSV(boolean enabled){
        prefs.edit().putBoolean(PreferenceNames.LOG_TO_CSV, enabled).apply();
    }

    @ProfilePreference(name=PreferenceNames.LOG_TO_CSV_DELIMITER)
    public String getCSVDelimiter() {
        return prefs.getString(PreferenceNames.LOG_TO_CSV_DELIMITER, ",");
    }

    public void setCSVDelimiter(String delimiter) {
        prefs.edit().putString(PreferenceNames.LOG_TO_CSV_DELIMITER, delimiter).apply();
    }

    @ProfilePreference(name=PreferenceNames.LOG_TO_CSV_DECIMAL_COMMA)
    public boolean shouldCSVUseCommaInsteadOfPoint(){
        return prefs.getBoolean(PreferenceNames.LOG_TO_CSV_DECIMAL_COMMA, false);
    }

    public void setShouldCSVUseCommaInsteadOfDecimal(boolean useComma){
        prefs.edit().putBoolean(PreferenceNames.LOG_TO_CSV_DECIMAL_COMMA, useComma).apply();
    }

    /**
     * Whether to log to a GeoJSON file
     */
    @ProfilePreference(name= PreferenceNames.LOG_TO_GEOJSON)
    public boolean shouldLogToGeoJSON() {
        return prefs.getBoolean(PreferenceNames.LOG_TO_GEOJSON, false);
    }


    /**
     * Whether to log to NMEA file
     */
    @ProfilePreference(name= PreferenceNames.LOG_TO_NMEA)
    public boolean shouldLogToNmea() {
        return prefs.getBoolean(PreferenceNames.LOG_TO_NMEA, false);
    }

    /**
     * Whether to log annotations to TXT file
     */
    @ProfilePreference(name= PreferenceNames.LOG_TO_TXT_ANNOTATIONS)
    public boolean shouldLogToTxtAnnotations() {
        return prefs.getBoolean(PreferenceNames.LOG_TO_TXT_ANNOTATIONS, false);
    }

    /**
     * Whether to use synchronous writing for annotations (for data consistency)
     */
    @ProfilePreference(name= PreferenceNames.ANNOTATION_SYNC_WRITE)
    public boolean shouldUseSyncAnnotationWrite() {
        return prefs.getBoolean(PreferenceNames.ANNOTATION_SYNC_WRITE, false);
    }

    /**
     * Whether to write timestamps with a timezone offset (true), or as UTC (false, default)
     */
    public boolean shouldWriteTimeWithOffset() {
        return prefs.getBoolean(PreferenceNames.LOGGING_WRITE_TIME_WITH_OFFSET, false);
    }


    /**
     * Whether to log to a custom URL. The app will log to the URL returned by {@link #getCustomLoggingUrl()}
     */
    @ProfilePreference(name= PreferenceNames.LOG_TO_URL)
    public boolean shouldLogToCustomUrl() {
        return prefs.getBoolean(PreferenceNames.LOG_TO_URL, false);
    }

    @ProfilePreference(name=PreferenceNames.LOG_TO_URL_METHOD)
    public String getCustomLoggingHTTPMethod(){
        return prefs.getString(PreferenceNames.LOG_TO_URL_METHOD, "GET");
    }

    public void setCustomLoggingHTTPMethod(String method){
        prefs.edit().putString(PreferenceNames.LOG_TO_URL_METHOD, method).apply();
    }

    @ProfilePreference(name=PreferenceNames.LOG_TO_URL_BODY)
    public String getCustomLoggingHTTPBody(){
        return prefs.getString(PreferenceNames.LOG_TO_URL_BODY,"");
    }

    public void setCustomLoggingHTTPBody(String body){
        prefs.edit().putString(PreferenceNames.LOG_TO_URL_BODY, body).apply();
    }

    @ProfilePreference(name=PreferenceNames.LOG_TO_URL_HEADERS)
    public String getCustomLoggingHTTPHeaders(){
        return prefs.getString(PreferenceNames.LOG_TO_URL_HEADERS,"");
    }

    public void setCustomLoggingHTTPHeaders(String headers){
        prefs.edit().putString(PreferenceNames.LOG_TO_URL_HEADERS, headers).apply();
    }

    @ProfilePreference(name=PreferenceNames.LOG_TO_URL_BASICAUTH_USERNAME)
    public String getCustomLoggingBasicAuthUsername() {
        return prefs.getString(PreferenceNames.LOG_TO_URL_BASICAUTH_USERNAME, "");
    }

    public void setCustomLoggingBasicAuthUsername(String username) {
        prefs.edit().putString(PreferenceNames.LOG_TO_URL_BASICAUTH_USERNAME, username).apply();
    }

    @ProfilePreference(name=PreferenceNames.LOG_TO_URL_BASICAUTH_PASSWORD)
    public String getCustomLoggingBasicAuthPassword() {
        return prefs.getString(PreferenceNames.LOG_TO_URL_BASICAUTH_PASSWORD, "");
    }

    public void setCustomLoggingBasicAuthPassword(String password) {
        prefs.edit().putString(PreferenceNames.LOG_TO_URL_BASICAUTH_PASSWORD, password).apply();
    }

    /**
     * The custom URL to log to.  Relevant only if {@link #shouldLogToCustomUrl()} returns true.
     */
    @ProfilePreference(name= PreferenceNames.LOG_TO_URL_PATH)
    public String getCustomLoggingUrl() {
        return prefs.getString(PreferenceNames.LOG_TO_URL_PATH, "http://localhost/log?lat=%LAT&longitude=%LON&time=%TIME&s=%SPD");
    }

    /**
     * Sets custom URL to log to, if {@link #shouldLogToCustomUrl()} returns true.
     */
    public void setCustomLoggingUrl(String customLoggingUrl) {
        prefs.edit().putString(PreferenceNames.LOG_TO_URL_PATH, customLoggingUrl).apply();
    }

    public boolean isCustomURLAutoSendEnabled() {
        return prefs.getBoolean(PreferenceNames.AUTOSEND_CUSTOMURL_ENABLED, false);
    }

    @ProfilePreference(name= PreferenceNames.LOG_TO_URL_DISCARD_OFFLINE_LOCATIONS_ENABLED)
    public boolean shouldCustomURLLoggingDiscardOfflineLocations() {
        return prefs.getBoolean(PreferenceNames.LOG_TO_URL_DISCARD_OFFLINE_LOCATIONS_ENABLED, false);
    }


    /**
     * Whether to log to OpenGTS.  See their <a href="http://opengts.sourceforge.net/OpenGTS_Config.pdf">installation guide</a>
     */
    @ProfilePreference(name= PreferenceNames.LOG_TO_OPENGTS)
    public boolean shouldLogToOpenGTS() {
        return prefs.getBoolean(PreferenceNames.LOG_TO_OPENGTS, false);
    }




    @ProfilePreference(name=PreferenceNames.LOG_PASSIVE_LOCATIONS)
    public boolean shouldLogPassiveLocations(){
        return prefs.getBoolean(PreferenceNames.LOG_PASSIVE_LOCATIONS, false);
    }


    public void setShouldLogPassiveLocations(boolean value){
        prefs.edit().putBoolean(PreferenceNames.LOG_PASSIVE_LOCATIONS, value).apply();
    }


    @ProfilePreference(name = PreferenceNames.LOG_SATELLITE_LOCATIONS)
    public boolean shouldLogSatelliteLocations(){
        return  prefs.getBoolean(PreferenceNames.LOG_SATELLITE_LOCATIONS, true);
    }

    public void setShouldLogSatelliteLocations(boolean value){
        prefs.edit().putBoolean(PreferenceNames.LOG_SATELLITE_LOCATIONS, value).apply();
    }

    @ProfilePreference(name = PreferenceNames.LOG_NETWORK_LOCATIONS)
    public boolean shouldLogNetworkLocations(){
        return prefs.getBoolean(PreferenceNames.LOG_NETWORK_LOCATIONS, true);
    }

    public void setShouldLogNetworkLocations(boolean value){
        prefs.edit().putBoolean(PreferenceNames.LOG_NETWORK_LOCATIONS, value).apply();
    }




    /**
     * New file creation preference:
     * onceamonth - once a month,
     * onceaday - once a day,
     * customfile - custom file (static),
     * everystart - every time the service starts
     */
    @ProfilePreference(name=PreferenceNames.NEW_FILE_CREATION_MODE)
    public String getNewFileCreationMode() {
        return prefs.getString(PreferenceNames.NEW_FILE_CREATION_MODE, "onceaday");
    }

    public void setNewFileCreationMode(String mode){
        prefs.edit().putString(PreferenceNames.NEW_FILE_CREATION_MODE, mode).apply();
    }


    /**
     * Whether a new file should be created daily
     */
    public boolean shouldCreateNewFileOnceADay() {
        return (getNewFileCreationMode().equals("onceaday"));
    }


    /**
     * Whether a new file should be created monthly
     */
    public boolean shouldCreateNewFileOnceAMonth() {
        return (getNewFileCreationMode().equals("onceamonth"));
    }


    /**
     * Whether only a custom file should be created
     */
    public boolean shouldCreateCustomFile() {
        return getNewFileCreationMode().equals("custom") || getNewFileCreationMode().equals("static");
    }


    /**
     * The custom filename to use if {@link #shouldCreateCustomFile()} returns true
     */
    @ProfilePreference(name= PreferenceNames.CUSTOM_FILE_NAME)
    public String getCustomFileName() {
        return prefs.getString(PreferenceNames.CUSTOM_FILE_NAME, "gpslogger");
    }


    /**
     * Sets custom filename to use if {@link #shouldCreateCustomFile()} returns true
     */
    public void setCustomFileName(String customFileName) {
        prefs.edit().putString(PreferenceNames.CUSTOM_FILE_NAME, customFileName).apply();
    }

    /**
     * Whether to prompt for a custom file name each time logging starts, if {@link #shouldCreateCustomFile()} returns true
     */
    @ProfilePreference(name= PreferenceNames.ASK_CUSTOM_FILE_NAME)
    public boolean shouldAskCustomFileNameEachTime() {
        return prefs.getBoolean(PreferenceNames.ASK_CUSTOM_FILE_NAME, true);
    }

    /**
     * Whether automatic sending to various targets (email,ftp, dropbox, etc) is enabled
     */
    @ProfilePreference(name= PreferenceNames.AUTOSEND_ENABLED)
    public boolean isAutoSendEnabled() {
        return prefs.getBoolean(PreferenceNames.AUTOSEND_ENABLED, false);
    }


    /**
     * The time, in minutes, before files are sent to the auto-send targets
     */
    @ProfilePreference(name= PreferenceNames.AUTOSEND_FREQUENCY)
    public int getAutoSendInterval() {
        return Math.round(Float.parseFloat(prefs.getString(PreferenceNames.AUTOSEND_FREQUENCY, "60")));
    }

    public void setAutoSendInterval(String frequency){
        prefs.edit().putString(PreferenceNames.AUTOSEND_FREQUENCY, frequency).apply();
    }


    /**
     * Whether to auto send to targets when logging is stopped
     */
    @ProfilePreference(name= PreferenceNames.AUTOSEND_ON_STOP)
    public boolean shouldAutoSendOnStopLogging() {
        return prefs.getBoolean(PreferenceNames.AUTOSEND_ON_STOP, true);
    }

    public void setDebugToFile(boolean writeToFile) {
        prefs.edit().putBoolean(PreferenceNames.DEBUG_TO_FILE, writeToFile).apply();
    }

    /**
     * Whether to write log messages to a debuglog.txt file
     */
    public boolean shouldDebugToFile() {
        return prefs.getBoolean(PreferenceNames.DEBUG_TO_FILE, false);
    }


    /**
     * Whether to zip the files up before auto sending to targets
     */
    @ProfilePreference(name= PreferenceNames.AUTOSEND_ZIP)
    public boolean shouldSendZipFile() {
        return prefs.getBoolean(PreferenceNames.AUTOSEND_ZIP, true);
    }

    /**
     * Whether to encrypt zip files with password protection
     */
    @ProfilePreference(name= PreferenceNames.AUTOSEND_ZIP_ENCRYPT_ENABLED)
    public boolean shouldEncryptZipFile() {
        return prefs.getBoolean(PreferenceNames.AUTOSEND_ZIP_ENCRYPT_ENABLED, false);
    }

    public void setZipEncryptionEnabled(boolean enabled) {
        prefs.edit().putBoolean(PreferenceNames.AUTOSEND_ZIP_ENCRYPT_ENABLED, enabled).apply();
    }

    /**
     * Get encrypted SharedPreferences for sensitive data storage
     */
    private SharedPreferences getEncryptedPreferences() throws Exception {
        String masterKeyAlias = MasterKeys.getOrCreate(MasterKeys.AES256_GCM_SPEC);

        return EncryptedSharedPreferences.create(
            "encrypted_prefs",
            masterKeyAlias,
            AppSettings.getInstance().getApplicationContext(),
            EncryptedSharedPreferences.PrefKeyEncryptionScheme.AES256_SIV,
            EncryptedSharedPreferences.PrefValueEncryptionScheme.AES256_GCM
        );
    }

    /**
     * Get the zip encryption password from secure storage
     */
    public String getZipPassword() {
        try {
            SharedPreferences encryptedPrefs = getEncryptedPreferences();
            return encryptedPrefs.getString(PreferenceNames.AUTOSEND_ZIP_PASSWORD, "");
        } catch (Exception e) {
            LOG.error("Failed to retrieve zip password from encrypted storage", e);
            return "";
        }
    }

    /**
     * Store the zip encryption password in secure storage
     */
    public void setZipPassword(String password) {
        try {
            SharedPreferences encryptedPrefs = getEncryptedPreferences();
            encryptedPrefs.edit().putString(PreferenceNames.AUTOSEND_ZIP_PASSWORD,
                                          password != null ? password : "").apply();
            LOG.debug("Zip password stored in encrypted preferences");
        } catch (Exception e) {
            LOG.error("Failed to store zip password in encrypted storage", e);
        }
    }

    // Scheduled sending preferences

    /**
     * Whether scheduled sending is enabled
     */
    @ProfilePreference(name=PreferenceNames.AUTOSEND_SCHEDULED_ENABLED)
    public boolean isScheduledSendEnabled() {
        return prefs.getBoolean(PreferenceNames.AUTOSEND_SCHEDULED_ENABLED, false);
    }

    public void setScheduledSendEnabled(boolean enabled) {
        prefs.edit().putBoolean(PreferenceNames.AUTOSEND_SCHEDULED_ENABLED, enabled).apply();
    }

    /**
     * Get scheduled send times as comma-separated string (e.g., "08:00,14:30,18:00")
     */
    @ProfilePreference(name=PreferenceNames.AUTOSEND_SCHEDULED_TIMES)
    public String getScheduledSendTimes() {
        return prefs.getString(PreferenceNames.AUTOSEND_SCHEDULED_TIMES, "");
    }

    public void setScheduledSendTimes(String times) {
        prefs.edit().putString(PreferenceNames.AUTOSEND_SCHEDULED_TIMES, times).apply();
    }

    /**
     * Get scheduled send times as list of time strings
     */
    public List<String> getScheduledSendTimesList() {
        String times = getScheduledSendTimes();
        if (times == null || times.trim().isEmpty()) {
            return new ArrayList<>();
        }

        List<String> timeList = new ArrayList<>();
        String[] timeArray = times.split(",");
        for (String time : timeArray) {
            String trimmed = time.trim();
            if (!trimmed.isEmpty() && isValidTimeFormat(trimmed)) {
                timeList.add(trimmed);
            }
        }
        return timeList;
    }

    /**
     * Validate time format (HH:mm)
     */
    private boolean isValidTimeFormat(String time) {
        if (time == null || time.length() != 5) {
            return false;
        }

        try {
            String[] parts = time.split(":");
            if (parts.length != 2) {
                return false;
            }

            int hour = Integer.parseInt(parts[0]);
            int minute = Integer.parseInt(parts[1]);

            return hour >= 0 && hour <= 23 && minute >= 0 && minute <= 59;
        } catch (NumberFormatException e) {
            return false;
        }
    }


    /**
     * Whether to auto send to OpenGTS Server
     */
    @ProfilePreference(name= PreferenceNames.AUTOSEND_OPENGTS_ENABLED)
    public boolean isOpenGtsAutoSendEnabled() {
        return prefs.getBoolean(PreferenceNames.AUTOSEND_OPENGTS_ENABLED, false);
    }


    /**
     * OpenGTS Server name
     */
    @ProfilePreference(name= PreferenceNames.OPENGTS_SERVER)
    public String getOpenGTSServer() {
        return prefs.getString(PreferenceNames.OPENGTS_SERVER, "");
    }


    public void setOpenGTSServer(String server){
        prefs.edit().putString(PreferenceNames.OPENGTS_SERVER, server).apply();
    }

    /**
     * OpenGTS Server Port
     */
    @ProfilePreference(name= PreferenceNames.OPENGTS_PORT)
    public String getOpenGTSServerPort() {
        return prefs.getString(PreferenceNames.OPENGTS_PORT, "443");
    }

    public void setOpenGTSServerPort(String port){
        prefs.edit().putString(PreferenceNames.OPENGTS_PORT, port).apply();
    }

    /**
     * Communication method when talking to OpenGTS (either UDP or HTTP)
     */
    @ProfilePreference(name= PreferenceNames.OPENGTS_PROTOCOL)
    public String getOpenGTSServerCommunicationMethod() {
        return prefs.getString(PreferenceNames.OPENGTS_PROTOCOL, "HTTPS");
    }


    /**
     * OpenGTS Server Path
     */
    @ProfilePreference(name= PreferenceNames.OPENGTS_SERVER_PATH)
    public String getOpenGTSServerPath() {
        return prefs.getString(PreferenceNames.OPENGTS_SERVER_PATH, "");
    }

    public void setOpenGTSServerPath(String path){
        prefs.edit().putString(PreferenceNames.OPENGTS_SERVER_PATH, path).apply();
    }

    /**
     * Device ID for OpenGTS communication
     */
    @ProfilePreference(name= PreferenceNames.OPENGTS_DEVICE_ID)
    public String getOpenGTSDeviceId() {
        return prefs.getString(PreferenceNames.OPENGTS_DEVICE_ID, "");
    }

    public void setOpenGTSDeviceId(String deviceId){
        prefs.edit().putString(PreferenceNames.OPENGTS_DEVICE_ID, deviceId).apply();
    }


    /**
     * Account name for OpenGTS communication
     */
    @ProfilePreference(name= PreferenceNames.OPENGTS_ACCOUNT_NAME)
    public String getOpenGTSAccountName() {
        return prefs.getString(PreferenceNames.OPENGTS_ACCOUNT_NAME, "");
    }

    public void setOpenGTSAccountName(String account){
        prefs.edit().putString(PreferenceNames.OPENGTS_ACCOUNT_NAME, account).apply();
    }



    public String getOSMAuthState(){
        return prefs.getString(PreferenceNames.OPENSTREETMAP_AUTH_STATE, "");
    }

    public void setOSMAuthState(String auth_state_json_serialized){
        prefs.edit().putString(PreferenceNames.OPENSTREETMAP_AUTH_STATE, auth_state_json_serialized).apply();
    }


    /**
     * Description of uploaded trace on OpenStreetMap
     */
    @ProfilePreference(name = PreferenceNames.OPENSTREETMAP_DESCRIPTION)
    public String getOSMDescription() {
        return prefs.getString(PreferenceNames.OPENSTREETMAP_DESCRIPTION, "");
    }

    public void setOSMDescription(String description){
        prefs.edit().putString(PreferenceNames.OPENSTREETMAP_DESCRIPTION, description).apply();
    }

    /**
     * Tags associated with uploaded trace on OpenStreetMap
     */
    @ProfilePreference(name= PreferenceNames.OPENSTREETMAP_TAGS)
    public String getOSMTags() {
        return prefs.getString(PreferenceNames.OPENSTREETMAP_TAGS, "");
    }

    public void setOSMTags(String tags){
        prefs.edit().putString(PreferenceNames.OPENSTREETMAP_TAGS, tags).apply();
    }

    /**
     * Visibility of uploaded trace on OpenStreetMap
     */
    @ProfilePreference(name= PreferenceNames.OPENSTREETMAP_VISIBILITY)
    public String getOSMVisibility() {
        return prefs.getString(PreferenceNames.OPENSTREETMAP_VISIBILITY, "private");
    }

    public void setOSMVisibility(String visibility){
        prefs.edit().putString(PreferenceNames.OPENSTREETMAP_VISIBILITY, visibility).apply();
    }


    /**
     * Whether to auto send to OpenStreetMap
     */
    @ProfilePreference(name= PreferenceNames.AUTOSEND_OSM_ENABLED)
    public boolean isOsmAutoSendEnabled() {
        return prefs.getBoolean(PreferenceNames.AUTOSEND_OSM_ENABLED, false);
    }


    /**
     * Whether to prompt user for OSM details before starting logging
     */
    @ProfilePreference(name= PreferenceNames.OPENSTREETMAP_PROMPT_WHEN_LOGGING_STARTS)
    public boolean shouldPromptForOSMDetailsWhenLoggingStarts() {
        return prefs.getBoolean(PreferenceNames.OPENSTREETMAP_PROMPT_WHEN_LOGGING_STARTS, false);
    }



    /**
     * OwnCloud server for auto send
     */
    @ProfilePreference(name= PreferenceNames.OWNCLOUD_BASE_URL)
    public String getOwnCloudBaseUrl() {
        return prefs.getString(PreferenceNames.OWNCLOUD_BASE_URL, "");
    }

    public void setOwnCloudBaseUrl(String baseUrl){
        prefs.edit().putString(PreferenceNames.OWNCLOUD_BASE_URL, baseUrl).apply();
    }

    /**
     * OwnCloud username for auto send
     */
    @ProfilePreference(name= PreferenceNames.OWNCLOUD_USERNAME)
    public String getOwnCloudUsername() {
        return prefs.getString(PreferenceNames.OWNCLOUD_USERNAME, "");
    }

    public void setOwnCloudUsername(String user) {
        prefs.edit().putString(PreferenceNames.OWNCLOUD_USERNAME, user).apply();
    }

    /**
     * OwnCloud password for auto send
     */
    @ProfilePreference(name= PreferenceNames.OWNCLOUD_PASSWORD)
    public String getOwnCloudPassword() {
        return prefs.getString(PreferenceNames.OWNCLOUD_PASSWORD, "");
    }

    public void setOwnCloudPassword(String pass){
        prefs.edit().putString(PreferenceNames.OWNCLOUD_PASSWORD, pass).apply();
    }


    /**
     * OwnCloud target directory for autosend
     */
    @ProfilePreference(name= PreferenceNames.OWNCLOUD_DIRECTORY)
    public String getOwnCloudDirectory() {
        return prefs.getString(PreferenceNames.OWNCLOUD_DIRECTORY, "/gpslogger");
    }

    public void setOwnCloudDirectory(String dir){
        prefs.edit().putString(PreferenceNames.OWNCLOUD_DIRECTORY, dir).apply();
    }

    /**
     * Whether to auto send to OwnCloud
     */
    @ProfilePreference(name= PreferenceNames.AUTOSEND_OWNCLOUD_ENABLED)
    public boolean isOwnCloudAutoSendEnabled() {
        return prefs.getBoolean(PreferenceNames.AUTOSEND_OWNCLOUD_ENABLED, false);
    }




    /**
     * Whether to prefix the phone's serial number to the logging file
     */
    @ProfilePreference(name= PreferenceNames.PREFIX_SERIAL_TO_FILENAME)
    public boolean shouldPrefixSerialToFileName() {
        return prefs.getBoolean(PreferenceNames.PREFIX_SERIAL_TO_FILENAME, false);
    }


    /**
     * Whether to subtract GeoID height from the reported altitude to get Mean Sea Level altitude instead of WGS84
     */
    @ProfilePreference(name= PreferenceNames.ALTITUDE_SHOULD_ADJUST)
    public boolean shouldAdjustAltitudeFromGeoIdHeight() {
        return prefs.getBoolean(PreferenceNames.ALTITUDE_SHOULD_ADJUST, false);
    }


    /**
     * How much to subtract from the altitude reported
     */
    @ProfilePreference(name= PreferenceNames.ALTITUDE_SUBTRACT_OFFSET)
    public int getSubtractAltitudeOffset() {
        return Strings.toInt(prefs.getString(PreferenceNames.ALTITUDE_SUBTRACT_OFFSET, "0"), 0);
    }

    public void setSubtractAltitudeOffset(String offset){
        prefs.edit().putString(PreferenceNames.ALTITUDE_SUBTRACT_OFFSET, offset).apply();
    }


    /**
     * Whether to autosend only if wifi is enabled
     */
    @ProfilePreference(name= PreferenceNames.AUTOSEND_WIFI_ONLY)
    public boolean shouldAutoSendOnWifiOnly() {
        return prefs.getBoolean(PreferenceNames.AUTOSEND_WIFI_ONLY, false);
    }


    @ProfilePreference(name= PreferenceNames.CURRENT_PROFILE_NAME)
    public String getCurrentProfileName() {
        return prefs.getString(PreferenceNames.CURRENT_PROFILE_NAME, AppSettings.getInstance().getString(R.string.profile_default));
    }

    public void setCurrentProfileName(String profileName){
        prefs.edit().putString(PreferenceNames.CURRENT_PROFILE_NAME, profileName).apply();
    }

    /**
     * A preference to keep track of version specific changes.
     */
    @ProfilePreference(name= PreferenceNames.LAST_VERSION_SEEN_BY_USER)
    public int getLastVersionSeen(){
        return Strings.toInt(prefs.getString(PreferenceNames.LAST_VERSION_SEEN_BY_USER, "1"), 1);
    }

    public void setLastVersionSeen(int lastVersionSeen){
        prefs.edit().putString(PreferenceNames.LAST_VERSION_SEEN_BY_USER, String.valueOf(lastVersionSeen)).apply();
    }


    @ProfilePreference(name=PreferenceNames.USER_SPECIFIED_LANGUAGE)
    public String getUserSpecifiedLocale() {
        return prefs.getString(PreferenceNames.USER_SPECIFIED_LANGUAGE, "");
    }

    public void setUserSpecifiedLocale(String userSpecifiedLocale) {
        prefs.edit().putString(PreferenceNames.USER_SPECIFIED_LANGUAGE, userSpecifiedLocale).apply();
    }

    @ProfilePreference(name=PreferenceNames.CUSTOM_FILE_NAME_KEEP_CHANGING)
    public boolean shouldChangeFileNameDynamically() {
        return prefs.getBoolean(PreferenceNames.CUSTOM_FILE_NAME_KEEP_CHANGING, true);
    }

    /**
     * Whether to create date-based folder structure
     * 强制启用日期文件夹创建功能，始终返回true
     */
    @ProfilePreference(name=PreferenceNames.CREATE_DATE_FOLDERS)
    public boolean shouldCreateDateFolders() {
        // 强制启用日期文件夹功能，忽略用户设置
        return true;
    }

    public void setShouldCreateDateFolders(boolean createDateFolders) {
        // 保留方法以保持API兼容性，但不执行任何操作
        // 因为日期文件夹功能已强制启用
    }

    /**
     * Get folder path template for date-based folder structure
     */
    @ProfilePreference(name=PreferenceNames.FOLDER_PATH_TEMPLATE)
    public String getFolderPathTemplate() {
        return prefs.getString(PreferenceNames.FOLDER_PATH_TEMPLATE, "{year}/{month}/{day}");
    }

    public void setFolderPathTemplate(String template) {
        prefs.edit().putString(PreferenceNames.FOLDER_PATH_TEMPLATE, template).apply();
    }

    // External control settings
    @ProfilePreference(name=PreferenceNames.EXTERNAL_CONTROL_ENABLED)
    public boolean isExternalControlEnabled() {
        return prefs.getBoolean(PreferenceNames.EXTERNAL_CONTROL_ENABLED, false);
    }

    public void setExternalControlEnabled(boolean enabled) {
        prefs.edit().putBoolean(PreferenceNames.EXTERNAL_CONTROL_ENABLED, enabled).apply();
    }

    @ProfilePreference(name=PreferenceNames.HARDWARE_BUTTONS_ENABLED)
    public boolean isHardwareButtonsEnabled() {
        return prefs.getBoolean(PreferenceNames.HARDWARE_BUTTONS_ENABLED, false);
    }

    public void setHardwareButtonsEnabled(boolean enabled) {
        prefs.edit().putBoolean(PreferenceNames.HARDWARE_BUTTONS_ENABLED, enabled).apply();
    }

    @ProfilePreference(name=PreferenceNames.PROXIMITY_SENSOR_ENABLED)
    public boolean isProximitySensorEnabled() {
        return prefs.getBoolean(PreferenceNames.PROXIMITY_SENSOR_ENABLED, false);
    }

    public void setProximitySensorEnabled(boolean enabled) {
        prefs.edit().putBoolean(PreferenceNames.PROXIMITY_SENSOR_ENABLED, enabled).apply();
    }

    @ProfilePreference(name=PreferenceNames.HEADSET_BUTTONS_ENABLED)
    public boolean isHeadsetButtonsEnabled() {
        return prefs.getBoolean(PreferenceNames.HEADSET_BUTTONS_ENABLED, false);
    }

    public void setHeadsetButtonsEnabled(boolean enabled) {
        prefs.edit().putBoolean(PreferenceNames.HEADSET_BUTTONS_ENABLED, enabled).apply();
    }

    @ProfilePreference(name=PreferenceNames.EXTERNAL_KEYBOARD_ENABLED)
    public boolean isExternalKeyboardEnabled() {
        return prefs.getBoolean(PreferenceNames.EXTERNAL_KEYBOARD_ENABLED, false);
    }

    public void setExternalKeyboardEnabled(boolean enabled) {
        prefs.edit().putBoolean(PreferenceNames.EXTERNAL_KEYBOARD_ENABLED, enabled).apply();
    }

    @ProfilePreference(name=PreferenceNames.BACKGROUND_KEY_LISTENER_ENABLED)
    public boolean isBackgroundKeyListenerEnabled() {
        return prefs.getBoolean(PreferenceNames.BACKGROUND_KEY_LISTENER_ENABLED, false);
    }

    public void setBackgroundKeyListenerEnabled(boolean enabled) {
        prefs.edit().putBoolean(PreferenceNames.BACKGROUND_KEY_LISTENER_ENABLED, enabled).apply();
    }

    // Generic getter and setter for string preferences
    public String getString(String key, String defaultValue) {
        return prefs.getString(key, defaultValue);
    }

    public void setString(String key, String value) {
        prefs.edit().putString(key, value).apply();
    }

    // Generic getter and setter for boolean preferences
    public boolean getBoolean(String key, boolean defaultValue) {
        return prefs.getBoolean(key, defaultValue);
    }

    public void setBoolean(String key, boolean value) {
        prefs.edit().putBoolean(key, value).apply();
    }

    public void setShouldChangeFileNameDynamically(boolean keepChanging){
        prefs.edit().putBoolean(PreferenceNames.CUSTOM_FILE_NAME_KEEP_CHANGING, keepChanging).apply();
    }

    public boolean isSFTPEnabled(){
        return prefs.getBoolean(PreferenceNames.SFTP_ENABLED, false);
    }

    public String getSFTPHost(){
        return prefs.getString(PreferenceNames.SFTP_HOST, "127.0.0.1");
    }

    public void setSFTPHost(String host){
        prefs.edit().putString(PreferenceNames.SFTP_HOST, host).apply();
    }

    public int getSFTPPort(){
        return Strings.toInt(prefs.getString(PreferenceNames.SFTP_PORT, "22"),22);
    }

    public void setSFTPPort(String port){
        prefs.edit().putString(PreferenceNames.SFTP_PORT, port).apply();
    }

    public String getSFTPUser(){
        return prefs.getString(PreferenceNames.SFTP_USER, "");
    }

    public void setSFTPUser(String user){
        prefs.edit().putString(PreferenceNames.SFTP_USER, user).apply();
    }

    public String getSFTPPassword(){
        return prefs.getString(PreferenceNames.SFTP_PASSWORD, "");
    }

    public void setSFTPPassword(String pass){
        prefs.edit().putString(PreferenceNames.SFTP_PASSWORD, pass).apply();
    }

    public String getSFTPPrivateKeyFilePath(){
        return prefs.getString(PreferenceNames.SFTP_PRIVATE_KEY_PATH, "");
    }

    public void setSFTPPrivateKeyFilePath(String filePath){
        prefs.edit().putString(PreferenceNames.SFTP_PRIVATE_KEY_PATH, filePath).apply();
    }

    public String getSFTPPrivateKeyPassphrase(){
        return prefs.getString(PreferenceNames.SFTP_PRIVATE_KEY_PASSPHRASE, "");
    }

    public void setSFTPPrivateKeyPassphrase(String pass){
        prefs.edit().putString(PreferenceNames.SFTP_PRIVATE_KEY_PASSPHRASE, pass).apply();
    }

    public String getSFTPKnownHostKey(){
        return prefs.getString(PreferenceNames.SFTP_KNOWN_HOST_KEY, "");
    }

    public void setSFTPKnownHostKey(String hostKey){
        prefs.edit().putString(PreferenceNames.SFTP_KNOWN_HOST_KEY, hostKey).apply();
    }

    public String getSFTPRemoteServerPath(){
        return prefs.getString(PreferenceNames.SFTP_REMOTE_SERVER_PATH, "/tmp");
    }

    public void setSFTPRemoteServerPath(String path){
        prefs.edit().putString(PreferenceNames.SFTP_REMOTE_SERVER_PATH, path).apply();
    }

    /**
     * Annotations Button Settings
     */
    @ProfilePreference(name= PreferenceNames.ANNOTATIONS_BUTTON_SETTINGS)
    public String getAnnotationButtonSettings() {
        return prefs.getString(PreferenceNames.ANNOTATIONS_BUTTON_SETTINGS, "");
    }

    public void setAnnotationButtonSettings(String settings){
        prefs.edit().putString(PreferenceNames.ANNOTATIONS_BUTTON_SETTINGS, settings).apply();
    }

    /**
     * Get the number of annotation buttons to display (9-25)
     */
    @ProfilePreference(name= PreferenceNames.ANNOTATIONS_BUTTON_COUNT)
    public int getAnnotationButtonCount() {
        return prefs.getInt(PreferenceNames.ANNOTATIONS_BUTTON_COUNT, 9);
    }

    /**
     * Set the number of annotation buttons to display (9-25)
     */
    public void setAnnotationButtonCount(int count){
        // Ensure count is within valid range
        if (count < 9) count = 9;
        if (count > 25) count = 25;
        prefs.edit().putInt(PreferenceNames.ANNOTATIONS_BUTTON_COUNT, count).apply();
    }

    /**
     * Get the annotation button layout style - always returns rectangular
     */
    public String getAnnotationLayoutStyle() {
        return "rectangular";
    }

    /**
     * Get the annotation button spacing mode - always returns standard
     */
    public String getAnnotationSpacingMode() {
        return "standard";
    }

    /**
     * Get the custom spacing for annotation buttons - always returns 4dp
     */
    public int getAnnotationCustomSpacing() {
        return 4;
    }

    /**
     * Get the annotation view mode (grid or grouped)
     */
    @ProfilePreference(name = PreferenceNames.ANNOTATION_VIEW_MODE)
    public String getAnnotationViewMode() {
        return prefs.getString(PreferenceNames.ANNOTATION_VIEW_MODE, "grouped");
    }

    /**
     * Set the annotation view mode (grid or grouped)
     */
    public void setAnnotationViewMode(String mode) {
        prefs.edit().putString(PreferenceNames.ANNOTATION_VIEW_MODE, mode).apply();
    }

    @ProfilePreference(name= PreferenceNames.ONLY_LOG_IF_SIGNIFICANT_MOTION)
    public boolean shouldLogOnlyIfSignificantMotion() {
        return prefs.getBoolean(PreferenceNames.ONLY_LOG_IF_SIGNIFICANT_MOTION, false);
    }

    public void setShouldLogOnlyIfSignificantMotion(boolean value){
        prefs.edit().putBoolean(PreferenceNames.ONLY_LOG_IF_SIGNIFICANT_MOTION, value).apply();
    }

    @SuppressWarnings("unchecked")
    public void savePropertiesFromPreferences(File f) throws IOException {

        Properties props = new Properties();

        Method[] methods = PreferenceHelper.class.getMethods();
        for(Method m : methods){

            Annotation a = m.getAnnotation(ProfilePreference.class);
            if(a != null){
                try {
                    Object val = m.invoke(this);

                    if(val != null){
                        props.setProperty(((ProfilePreference)a).name(),String.valueOf(val));
                        LOG.debug(((ProfilePreference) a).name() + " : " + String.valueOf(val));
                    }
                    else {
                        LOG.debug("Null value: " + ((ProfilePreference) a).name() + " is null.");
                    }

                } catch (Exception e) {
                    LOG.error("Could not save preferences to profile", e);
                }
            }
        }

        OutputStream outStream = new FileOutputStream(f);
        props.store(outStream,"Warning: This file can contain server names, passwords, email addresses and other sensitive information.");

    }


    /**
     * Sets preferences in a generic manner from a .properties file
     */

    public void setPreferenceFromPropertiesFile(File file) throws IOException {
        Properties props = new Properties();
        InputStreamReader reader = new InputStreamReader(new FileInputStream(file));
        props.load(reader);

        for (Object key : props.keySet()) {

            SharedPreferences.Editor editor = prefs.edit();
            String value = props.getProperty(key.toString());
            LOG.info("Setting preset property: " + key.toString() + " to " + value.toString());

            if (value.equalsIgnoreCase("true") || value.equalsIgnoreCase("false")) {
                editor.putBoolean(key.toString(), Boolean.parseBoolean(value));
            } else {
                editor.putString(key.toString(), value);
            }
            editor.apply();
        }

    }

    // Voice input settings

    /**
     * Whether voice input is enabled for annotations
     */
    @ProfilePreference(name = PreferenceNames.VOICE_INPUT_ENABLED)
    public boolean isVoiceInputEnabled() {
        return prefs.getBoolean(PreferenceNames.VOICE_INPUT_ENABLED, false);
    }

    public void setVoiceInputEnabled(boolean enabled) {
        prefs.edit().putBoolean(PreferenceNames.VOICE_INPUT_ENABLED, enabled).apply();
    }

    /**
     * Get the preferred language for voice input
     */
    @ProfilePreference(name = PreferenceNames.VOICE_INPUT_LANGUAGE)
    public String getVoiceInputLanguage() {
        return prefs.getString(PreferenceNames.VOICE_INPUT_LANGUAGE, "");
    }

    public void setVoiceInputLanguage(String language) {
        prefs.edit().putString(PreferenceNames.VOICE_INPUT_LANGUAGE, language).apply();
    }

    /**
     * Get the timeout for voice input in seconds
     */
    @ProfilePreference(name = PreferenceNames.VOICE_INPUT_TIMEOUT)
    public int getVoiceInputTimeout() {
        return prefs.getInt(PreferenceNames.VOICE_INPUT_TIMEOUT, 10);
    }

    public void setVoiceInputTimeout(int timeout) {
        prefs.edit().putInt(PreferenceNames.VOICE_INPUT_TIMEOUT, timeout).apply();
    }

    // ===== Audio Recording Settings =====

    /**
     * Get the silence timeout for audio recording in seconds
     */
    @ProfilePreference(name = PreferenceNames.AUDIO_RECORDING_SILENCE_TIMEOUT)
    public int getAudioRecordingSilenceTimeout() {
        return prefs.getInt(PreferenceNames.AUDIO_RECORDING_SILENCE_TIMEOUT, 3); // Default 3 seconds (increased from 2)
    }

    public void setAudioRecordingSilenceTimeout(int timeout) {
        prefs.edit().putInt(PreferenceNames.AUDIO_RECORDING_SILENCE_TIMEOUT, timeout).apply();
    }

    /**
     * Get the silence threshold for audio recording
     */
    @ProfilePreference(name = PreferenceNames.AUDIO_RECORDING_SILENCE_THRESHOLD)
    public int getAudioRecordingSilenceThreshold() {
        return prefs.getInt(PreferenceNames.AUDIO_RECORDING_SILENCE_THRESHOLD, 800); // Default 800
    }

    public void setAudioRecordingSilenceThreshold(int threshold) {
        prefs.edit().putInt(PreferenceNames.AUDIO_RECORDING_SILENCE_THRESHOLD, threshold).apply();
    }

    /**
     * Get whether advanced voice detection is enabled
     */
    @ProfilePreference(name = PreferenceNames.AUDIO_RECORDING_ADVANCED_DETECTION)
    public boolean isAudioRecordingAdvancedDetectionEnabled() {
        return prefs.getBoolean(PreferenceNames.AUDIO_RECORDING_ADVANCED_DETECTION, true); // Default enabled
    }

    public void setAudioRecordingAdvancedDetectionEnabled(boolean enabled) {
        prefs.edit().putBoolean(PreferenceNames.AUDIO_RECORDING_ADVANCED_DETECTION, enabled).apply();
    }

    // Annotation Template Settings
    public void setAnnotationTemplate(String template) {
        prefs.edit().putString("annotation_template", template).apply();
    }

    public String getAnnotationTemplate() {
        return prefs.getString("annotation_template", "{voice_text}");
    }

    public void setAnnotationTemplateEnabled(boolean enabled) {
        prefs.edit().putBoolean("annotation_template_enabled", enabled).apply();
    }

    public boolean isAnnotationTemplateEnabled() {
        return prefs.getBoolean("annotation_template_enabled", false);
    }

    public void setAnnotationTemplatePresets(String presets) {
        prefs.edit().putString("annotation_template_presets", presets).apply();
    }

    public String getAnnotationTemplatePresets() {
        return prefs.getString("annotation_template_presets", "");
    }

    // Single Point Push Template Settings
    public void setSinglePointPushTemplate(String template) {
        prefs.edit().putString("single_point_push_template", template).apply();
    }

    public String getSinglePointPushTemplate() {
        return prefs.getString("single_point_push_template",
            "{primary_button_name}: {push_original_info} | 分组: {group_name} | " +
            "统计: {pushed_points_count}/{total_push_points} | " +
            "通过: {pass_count} | 不过: {fail_count}");
    }

    public void setSinglePointPushTemplateEnabled(boolean enabled) {
        prefs.edit().putBoolean("single_point_push_template_enabled", enabled).apply();
    }

    public boolean isSinglePointPushTemplateEnabled() {
        return prefs.getBoolean("single_point_push_template_enabled", true);
    }

    public void setSinglePointPushTemplatePresets(String presets) {
        prefs.edit().putString("single_point_push_template_presets", presets).apply();
    }

    public String getSinglePointPushTemplatePresets() {
        return prefs.getString("single_point_push_template_presets", "");
    }

    // ===== Audio Feedback Settings =====

    /**
     * Voice Input Button Audio Settings
     */
    public void setVoiceInputButtonAudioType(String audioType) {
        prefs.edit().putString("voice_input_button_audio_type", audioType).apply();
    }

    public String getVoiceInputButtonAudioType() {
        return prefs.getString("voice_input_button_audio_type", "none");
    }

    public void setVoiceInputButtonCustomAudioPath(String path) {
        prefs.edit().putString("voice_input_button_custom_audio_path", path).apply();
    }

    public String getVoiceInputButtonCustomAudioPath() {
        return prefs.getString("voice_input_button_custom_audio_path", "");
    }

    /**
     * Text Input Button Audio Settings
     */
    public void setTextInputButtonAudioType(String audioType) {
        prefs.edit().putString("text_input_button_audio_type", audioType).apply();
    }

    public String getTextInputButtonAudioType() {
        return prefs.getString("text_input_button_audio_type", "none");
    }

    public void setTextInputButtonCustomAudioPath(String path) {
        prefs.edit().putString("text_input_button_custom_audio_path", path).apply();
    }

    public String getTextInputButtonCustomAudioPath() {
        return prefs.getString("text_input_button_custom_audio_path", "");
    }

    /**
     * Counter Button Audio Settings
     */
    public void setCounterButtonAudioType(String audioType) {
        prefs.edit().putString("counter_button_audio_type", audioType).apply();
    }

    public String getCounterButtonAudioType() {
        return prefs.getString("counter_button_audio_type", "none");
    }

    public void setCounterButtonCustomAudioPath(String path) {
        prefs.edit().putString("counter_button_custom_audio_path", path).apply();
    }

    public String getCounterButtonCustomAudioPath() {
        return prefs.getString("counter_button_custom_audio_path", "");
    }

    /**
     * Single Click Button Audio Settings
     */
    public void setSingleClickButtonAudioType(String audioType) {
        prefs.edit().putString("single_click_button_audio_type", audioType).apply();
    }

    public String getSingleClickButtonAudioType() {
        return prefs.getString("single_click_button_audio_type", "none");
    }

    public void setSingleClickButtonCustomAudioPath(String path) {
        prefs.edit().putString("single_click_button_custom_audio_path", path).apply();
    }

    public String getSingleClickButtonCustomAudioPath() {
        return prefs.getString("single_click_button_custom_audio_path", "");
    }

    /**
     * Pass Button Audio Settings
     */
    public void setPassButtonAudioType(String audioType) {
        prefs.edit().putString("pass_button_audio_type", audioType).apply();
    }

    public String getPassButtonAudioType() {
        return prefs.getString("pass_button_audio_type", "none");
    }

    public void setPassButtonCustomAudioPath(String path) {
        prefs.edit().putString("pass_button_custom_audio_path", path).apply();
    }

    public String getPassButtonCustomAudioPath() {
        return prefs.getString("pass_button_custom_audio_path", "");
    }

    /**
     * Fail Button Audio Settings
     */
    public void setFailButtonAudioType(String audioType) {
        prefs.edit().putString("fail_button_audio_type", audioType).apply();
    }

    public String getFailButtonAudioType() {
        return prefs.getString("fail_button_audio_type", "none");
    }

    public void setFailButtonCustomAudioPath(String path) {
        prefs.edit().putString("fail_button_custom_audio_path", path).apply();
    }

    public String getFailButtonCustomAudioPath() {
        return prefs.getString("fail_button_custom_audio_path", "");
    }

    /**
     * Push Button Audio Settings
     */
    public void setPushButtonAudioType(String audioType) {
        prefs.edit().putString("push_button_audio_type", audioType).apply();
    }

    public String getPushButtonAudioType() {
        return prefs.getString("push_button_audio_type", "none");
    }

    public void setPushButtonCustomAudioPath(String path) {
        prefs.edit().putString("push_button_custom_audio_path", path).apply();
    }

    public String getPushButtonCustomAudioPath() {
        return prefs.getString("push_button_custom_audio_path", "");
    }

    /**
     * Sequential Push Button Audio Settings
     */
    public void setSequentialPushButtonAudioType(String audioType) {
        prefs.edit().putString("sequential_push_button_audio_type", audioType).apply();
    }

    public String getSequentialPushButtonAudioType() {
        return prefs.getString("sequential_push_button_audio_type", "none");
    }

    public void setSequentialPushButtonCustomAudioPath(String path) {
        prefs.edit().putString("sequential_push_button_custom_audio_path", path).apply();
    }

    public String getSequentialPushButtonCustomAudioPath() {
        return prefs.getString("sequential_push_button_custom_audio_path", "");
    }

    // ===== Single Point Push Settings =====

    @ProfilePreference(name = PreferenceNames.SINGLE_POINT_PUSH_ENABLED)
    public boolean isSinglePointPushEnabled() {
        return prefs.getBoolean(PreferenceNames.SINGLE_POINT_PUSH_ENABLED, false);
    }

    public void setSinglePointPushEnabled(boolean enabled) {
        prefs.edit().putBoolean(PreferenceNames.SINGLE_POINT_PUSH_ENABLED, enabled).apply();
    }

    @ProfilePreference(name = PreferenceNames.SINGLE_POINT_PUSH_AUTO_DETECT_TYPE)
    public String getSinglePointPushAutoDetectType() {
        return prefs.getString(PreferenceNames.SINGLE_POINT_PUSH_AUTO_DETECT_TYPE, "string");
    }

    public void setSinglePointPushAutoDetectType(String type) {
        prefs.edit().putString(PreferenceNames.SINGLE_POINT_PUSH_AUTO_DETECT_TYPE, type).apply();
    }

    // Single point push Intent configuration methods
    @ProfilePreference(name = PreferenceNames.SINGLE_POINT_PUSH_TARGET_TYPE)
    public String getSinglePointPushTargetType() {
        return prefs.getString(PreferenceNames.SINGLE_POINT_PUSH_TARGET_TYPE, "activity");
    }

    public void setSinglePointPushTargetType(String targetType) {
        prefs.edit().putString(PreferenceNames.SINGLE_POINT_PUSH_TARGET_TYPE, targetType).apply();
    }

    @ProfilePreference(name = PreferenceNames.SINGLE_POINT_PUSH_ACTION)
    public String getSinglePointPushAction() {
        return prefs.getString(PreferenceNames.SINGLE_POINT_PUSH_ACTION, "android.intent.action.VIEW");
    }

    public void setSinglePointPushAction(String action) {
        prefs.edit().putString(PreferenceNames.SINGLE_POINT_PUSH_ACTION, action).apply();
    }

    @ProfilePreference(name = PreferenceNames.SINGLE_POINT_PUSH_CLASS)
    public String getSinglePointPushClass() {
        return prefs.getString(PreferenceNames.SINGLE_POINT_PUSH_CLASS, "");
    }

    public void setSinglePointPushClass(String className) {
        prefs.edit().putString(PreferenceNames.SINGLE_POINT_PUSH_CLASS, className).apply();
    }

    @ProfilePreference(name = PreferenceNames.SINGLE_POINT_PUSH_PACKAGE)
    public String getSinglePointPushPackage() {
        return prefs.getString(PreferenceNames.SINGLE_POINT_PUSH_PACKAGE, "");
    }

    public void setSinglePointPushPackage(String packageName) {
        prefs.edit().putString(PreferenceNames.SINGLE_POINT_PUSH_PACKAGE, packageName).apply();
    }

    @ProfilePreference(name = PreferenceNames.SINGLE_POINT_PUSH_DATA)
    public String getSinglePointPushData() {
        return prefs.getString(PreferenceNames.SINGLE_POINT_PUSH_DATA, "");
    }

    public void setSinglePointPushData(String data) {
        prefs.edit().putString(PreferenceNames.SINGLE_POINT_PUSH_DATA, data).apply();
    }

    @ProfilePreference(name = PreferenceNames.SINGLE_POINT_PUSH_MIME_TYPE)
    public String getSinglePointPushMimeType() {
        return prefs.getString(PreferenceNames.SINGLE_POINT_PUSH_MIME_TYPE, "");
    }

    public void setSinglePointPushMimeType(String mimeType) {
        prefs.edit().putString(PreferenceNames.SINGLE_POINT_PUSH_MIME_TYPE, mimeType).apply();
    }

    @ProfilePreference(name = PreferenceNames.SINGLE_POINT_PUSH_FLAGS)
    public String getSinglePointPushFlags() {
        return prefs.getString(PreferenceNames.SINGLE_POINT_PUSH_FLAGS, "0");
    }

    public void setSinglePointPushFlags(String flags) {
        prefs.edit().putString(PreferenceNames.SINGLE_POINT_PUSH_FLAGS, flags).apply();
    }

    // Methods for extra data (6 pairs of key-value)
    public String getSinglePointPushExtraKey(int index) {
        return prefs.getString("single_point_push_extra_key_" + index, "");
    }

    public void setSinglePointPushExtraKey(int index, String key) {
        prefs.edit().putString("single_point_push_extra_key_" + index, key).apply();
    }

    public String getSinglePointPushExtraValue(int index) {
        return prefs.getString("single_point_push_extra_value_" + index, "");
    }

    public void setSinglePointPushExtraValue(int index, String value) {
        prefs.edit().putString("single_point_push_extra_value_" + index, value).apply();
    }

    public String getSinglePointPushAutoDetectType(int index) {
        return prefs.getString("single_point_push_auto_detect_type_" + index, "string");
    }

    public void setSinglePointPushAutoDetectType(int index, String type) {
        prefs.edit().putString("single_point_push_auto_detect_type_" + index, type).apply();
    }

}
