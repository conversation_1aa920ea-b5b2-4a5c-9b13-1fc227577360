/*
 * Copyright (C) 2024 mendhak
 *
 * This file is part of GPSLogger for Android.
 *
 * GPSLogger is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 2 of the License, or
 * (at your option) any later version.
 *
 * GPSLogger is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with GPSLogger.  If not, see <http://www.gnu.org/licenses/>.
 */

package com.mendhak.gpslogger.ui.managers;

import android.content.Context;
import android.content.SharedPreferences;
import android.util.Log;

import com.mendhak.gpslogger.ui.models.ButtonConfig;

import org.json.JSONException;

/**
 * 按钮配置管理器
 * 管理全局按钮配置的保存和加载
 */
public class ButtonConfigManager {
    
    private static final String TAG = "ButtonConfigManager";
    private static final String PREFS_NAME = "button_config_prefs";
    private static final String KEY_PASS_BUTTON_CONFIG = "pass_button_config";
    private static final String KEY_FAIL_BUTTON_CONFIG = "fail_button_config";
    
    private static ButtonConfigManager instance;
    private Context context;
    private SharedPreferences prefs;
    private ButtonConfig passButtonConfig;
    private ButtonConfig failButtonConfig;
    
    private ButtonConfigManager(Context context) {
        this.context = context.getApplicationContext();
        this.prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE);
        loadConfigs();
    }
    
    public static synchronized ButtonConfigManager getInstance(Context context) {
        if (instance == null) {
            instance = new ButtonConfigManager(context);
        }
        return instance;
    }
    
    /**
     * 获取通过按钮配置
     */
    public ButtonConfig getPassButtonConfig() {
        return passButtonConfig;
    }
    
    /**
     * 获取不通过按钮配置
     */
    public ButtonConfig getFailButtonConfig() {
        return failButtonConfig;
    }
    
    /**
     * 设置通过按钮配置
     */
    public void setPassButtonConfig(ButtonConfig config) {
        this.passButtonConfig = config;
        savePassButtonConfig();
    }
    
    /**
     * 设置不通过按钮配置
     */
    public void setFailButtonConfig(ButtonConfig config) {
        this.failButtonConfig = config;
        saveFailButtonConfig();
    }
    
    /**
     * 设置全局按钮配置（同时设置通过和不通过按钮）
     */
    public void setGlobalButtonConfig(ButtonConfig config) {
        // 为通过和不通过按钮创建独立的配置副本
        try {
            ButtonConfig passConfig = ButtonConfig.fromJson(config.toJson());
            ButtonConfig failConfig = ButtonConfig.fromJson(config.toJson());
            
            this.passButtonConfig = passConfig;
            this.failButtonConfig = failConfig;
            
            saveConfigs();
        } catch (JSONException e) {
            Log.e(TAG, "设置全局按钮配置失败", e);
        }
    }
    
    /**
     * 重置为默认配置
     */
    public void resetToDefault() {
        passButtonConfig = ButtonConfig.createDefaultTextConfig();
        failButtonConfig = ButtonConfig.createDefaultTextConfig();
        saveConfigs();
    }
    
    /**
     * 从SharedPreferences加载配置
     */
    private void loadConfigs() {
        try {
            // 加载通过按钮配置
            String passConfigJson = prefs.getString(KEY_PASS_BUTTON_CONFIG, null);
            if (passConfigJson != null) {
                passButtonConfig = ButtonConfig.fromJson(passConfigJson);
            } else {
                passButtonConfig = ButtonConfig.createDefaultTextConfig();
            }
            
            // 加载不通过按钮配置
            String failConfigJson = prefs.getString(KEY_FAIL_BUTTON_CONFIG, null);
            if (failConfigJson != null) {
                failButtonConfig = ButtonConfig.fromJson(failConfigJson);
            } else {
                failButtonConfig = ButtonConfig.createDefaultTextConfig();
            }
            
            Log.d(TAG, "成功加载按钮配置");
            
        } catch (JSONException e) {
            Log.e(TAG, "加载按钮配置失败，使用默认配置", e);
            passButtonConfig = ButtonConfig.createDefaultTextConfig();
            failButtonConfig = ButtonConfig.createDefaultTextConfig();
        }
    }
    
    /**
     * 保存所有配置
     */
    private void saveConfigs() {
        savePassButtonConfig();
        saveFailButtonConfig();
    }
    
    /**
     * 保存通过按钮配置
     */
    private void savePassButtonConfig() {
        try {
            String configJson = passButtonConfig.toJson();
            prefs.edit().putString(KEY_PASS_BUTTON_CONFIG, configJson).apply();
            Log.d(TAG, "成功保存通过按钮配置");
        } catch (JSONException e) {
            Log.e(TAG, "保存通过按钮配置失败", e);
        }
    }
    
    /**
     * 保存不通过按钮配置
     */
    private void saveFailButtonConfig() {
        try {
            String configJson = failButtonConfig.toJson();
            prefs.edit().putString(KEY_FAIL_BUTTON_CONFIG, configJson).apply();
            Log.d(TAG, "成功保存不通过按钮配置");
        } catch (JSONException e) {
            Log.e(TAG, "保存不通过按钮配置失败", e);
        }
    }
    
    /**
     * 检查配置是否为语音模式
     */
    public boolean isVoiceMode() {
        return passButtonConfig.getType() == ButtonConfig.ButtonType.VOICE ||
               failButtonConfig.getType() == ButtonConfig.ButtonType.VOICE;
    }
    
    /**
     * 检查配置是否为计数器模式
     */
    public boolean isCounterMode() {
        return passButtonConfig.getType() == ButtonConfig.ButtonType.COUNTER ||
               failButtonConfig.getType() == ButtonConfig.ButtonType.COUNTER;
    }
    
    /**
     * 检查配置是否为文本模式
     */
    public boolean isTextMode() {
        return passButtonConfig.getType() == ButtonConfig.ButtonType.TEXT &&
               failButtonConfig.getType() == ButtonConfig.ButtonType.TEXT;
    }
    
    /**
     * 获取配置摘要信息
     */
    public String getConfigSummary() {
        StringBuilder summary = new StringBuilder();
        summary.append("通过按钮: ").append(passButtonConfig.getType().name());
        summary.append(", 不通过按钮: ").append(failButtonConfig.getType().name());
        return summary.toString();
    }
}
