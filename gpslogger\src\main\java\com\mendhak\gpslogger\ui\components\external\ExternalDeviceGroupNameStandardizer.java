/*
 * Copyright (C) 2016 mendhak
 *
 * This file is part of GPSLogger for Android.
 *
 * GPSLogger is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 2 of the License, or
 * (at your option) any later version.
 *
 * GPSLogger is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with GPSLogger.  If not, see <http://www.gnu.org/licenses/>.
 */

package com.mendhak.gpslogger.ui.components.external;

import com.mendhak.gpslogger.common.slf4j.Logs;
import org.slf4j.Logger;

import java.util.HashMap;
import java.util.Map;
import java.util.regex.Pattern;

/**
 * Standardizes external device group names for consistent template processing
 * and better file organization.
 */
public class ExternalDeviceGroupNameStandardizer {
    
    private static final Logger LOG = Logs.of(ExternalDeviceGroupNameStandardizer.class);
    
    // Device type constants
    public static final String DEVICE_TYPE_HEADSET = "耳机";
    public static final String DEVICE_TYPE_STEERING_WHEEL = "方向盘";
    public static final String DEVICE_TYPE_KEYBOARD = "键盘";
    public static final String DEVICE_TYPE_HARDWARE_BUTTON = "硬件按键";
    public static final String DEVICE_TYPE_UNKNOWN = "外部设备";
    
    // Action type constants
    public static final String ACTION_SINGLE_CLICK = "单击";
    public static final String ACTION_DOUBLE_CLICK = "双击";
    public static final String ACTION_LONG_PRESS = "长按";
    public static final String ACTION_PRESS = "按下";
    public static final String ACTION_RELEASE = "释放";
    
    // Key mapping for common keys
    private static final Map<String, String> KEY_NAME_MAPPING = new HashMap<>();
    static {
        // Headset keys
        KEY_NAME_MAPPING.put("HEADSETHOOK", "通话键");
        KEY_NAME_MAPPING.put("MEDIA_PLAY_PAUSE", "播放暂停");
        KEY_NAME_MAPPING.put("MEDIA_NEXT", "下一首");
        KEY_NAME_MAPPING.put("MEDIA_PREVIOUS", "上一首");
        KEY_NAME_MAPPING.put("VOLUME_UP", "音量上");
        KEY_NAME_MAPPING.put("VOLUME_DOWN", "音量下");
        
        // Keyboard keys
        KEY_NAME_MAPPING.put("KEYCODE_SPACE", "空格键");
        KEY_NAME_MAPPING.put("KEYCODE_ENTER", "回车键");
        KEY_NAME_MAPPING.put("KEYCODE_TAB", "Tab键");
        KEY_NAME_MAPPING.put("KEYCODE_ESCAPE", "Esc键");
        
        // Hardware buttons
        KEY_NAME_MAPPING.put("KEYCODE_BACK", "返回键");
        KEY_NAME_MAPPING.put("KEYCODE_MENU", "菜单键");
        KEY_NAME_MAPPING.put("KEYCODE_HOME", "主页键");
        KEY_NAME_MAPPING.put("KEYCODE_POWER", "电源键");
    }
    
    // Patterns for parsing different source formats
    private static final Pattern HEADSET_PATTERN = Pattern.compile("后台耳机按键-(.+)");
    private static final Pattern STEERING_WHEEL_PATTERN = Pattern.compile("方向盘-(.+)-(.+)");
    private static final Pattern KEYBOARD_PATTERN = Pattern.compile("后台外接键盘-(.+)");
    private static final Pattern HARDWARE_PATTERN = Pattern.compile("后台硬件按键-(.+)");
    
    /**
     * Standardize external device group name for consistent processing
     * 
     * @param originalSource Original source string from device manager
     * @return Standardized group name
     */
    public static String standardizeGroupName(String originalSource) {
        if (originalSource == null || originalSource.trim().isEmpty()) {
            LOG.warn("Empty or null source provided, using default");
            return DEVICE_TYPE_UNKNOWN;
        }
        
        String source = originalSource.trim();
        LOG.debug("Standardizing group name for source: {}", source);
        
        try {
            // Parse headset button
            java.util.regex.Matcher headsetMatcher = HEADSET_PATTERN.matcher(source);
            if (headsetMatcher.matches()) {
                String keyName = headsetMatcher.group(1);
                String standardizedKey = KEY_NAME_MAPPING.getOrDefault(keyName, keyName);
                String result = DEVICE_TYPE_HEADSET + "-" + standardizedKey;
                LOG.debug("Standardized headset source: {} -> {}", source, result);
                return result;
            }
            
            // Parse steering wheel button
            java.util.regex.Matcher steeringMatcher = STEERING_WHEEL_PATTERN.matcher(source);
            if (steeringMatcher.matches()) {
                String buttonName = steeringMatcher.group(1);
                String actionType = steeringMatcher.group(2);
                String standardizedButton = KEY_NAME_MAPPING.getOrDefault(buttonName, buttonName);
                String standardizedAction = standardizeActionType(actionType);
                String result = DEVICE_TYPE_STEERING_WHEEL + "-" + standardizedButton + "-" + standardizedAction;
                LOG.debug("Standardized steering wheel source: {} -> {}", source, result);
                return result;
            }
            
            // Parse keyboard button
            java.util.regex.Matcher keyboardMatcher = KEYBOARD_PATTERN.matcher(source);
            if (keyboardMatcher.matches()) {
                String keyName = keyboardMatcher.group(1);
                String standardizedKey = KEY_NAME_MAPPING.getOrDefault(keyName, keyName);
                String result = DEVICE_TYPE_KEYBOARD + "-" + standardizedKey;
                LOG.debug("Standardized keyboard source: {} -> {}", source, result);
                return result;
            }
            
            // Parse hardware button
            java.util.regex.Matcher hardwareMatcher = HARDWARE_PATTERN.matcher(source);
            if (hardwareMatcher.matches()) {
                String keyName = hardwareMatcher.group(1);
                String standardizedKey = KEY_NAME_MAPPING.getOrDefault(keyName, keyName);
                String result = DEVICE_TYPE_HARDWARE_BUTTON + "-" + standardizedKey;
                LOG.debug("Standardized hardware source: {} -> {}", source, result);
                return result;
            }
            
            // If no pattern matches, try to extract device type from source
            if (source.contains("耳机") || source.contains("headset")) {
                return DEVICE_TYPE_HEADSET + "-未知按键";
            } else if (source.contains("方向盘") || source.contains("steering")) {
                return DEVICE_TYPE_STEERING_WHEEL + "-未知按键";
            } else if (source.contains("键盘") || source.contains("keyboard")) {
                return DEVICE_TYPE_KEYBOARD + "-未知按键";
            } else {
                LOG.warn("Unknown source format: {}, using generic device type", source);
                return DEVICE_TYPE_UNKNOWN + "-" + source;
            }
            
        } catch (Exception e) {
            LOG.error("Error standardizing group name for source: {}", source, e);
            return DEVICE_TYPE_UNKNOWN + "-" + source;
        }
    }
    
    /**
     * Standardize action type names
     */
    private static String standardizeActionType(String actionType) {
        if (actionType == null) return ACTION_PRESS;
        
        String lower = actionType.toLowerCase();
        if (lower.contains("single") || lower.contains("click") || lower.equals("单击")) {
            return ACTION_SINGLE_CLICK;
        } else if (lower.contains("double") || lower.equals("双击")) {
            return ACTION_DOUBLE_CLICK;
        } else if (lower.contains("long") || lower.contains("press") || lower.equals("长按")) {
            return ACTION_LONG_PRESS;
        } else if (lower.contains("release") || lower.equals("释放")) {
            return ACTION_RELEASE;
        } else {
            return actionType; // Keep original if not recognized
        }
    }
    
    /**
     * Get device type from standardized group name
     */
    public static String getDeviceType(String standardizedGroupName) {
        if (standardizedGroupName == null) return DEVICE_TYPE_UNKNOWN;
        
        String[] parts = standardizedGroupName.split("-");
        return parts.length > 0 ? parts[0] : DEVICE_TYPE_UNKNOWN;
    }
    
    /**
     * Get button name from standardized group name
     */
    public static String getButtonName(String standardizedGroupName) {
        if (standardizedGroupName == null) return "未知";
        
        String[] parts = standardizedGroupName.split("-");
        return parts.length > 1 ? parts[1] : "未知";
    }
    
    /**
     * Get action type from standardized group name
     */
    public static String getActionType(String standardizedGroupName) {
        if (standardizedGroupName == null) return ACTION_PRESS;
        
        String[] parts = standardizedGroupName.split("-");
        return parts.length > 2 ? parts[2] : ACTION_PRESS;
    }
}
