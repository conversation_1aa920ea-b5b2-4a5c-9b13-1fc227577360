<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="16dp"
    android:background="@drawable/dialog_background">

    <!-- 标题栏 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:paddingBottom="16dp">

        <!-- 一级按钮预览 -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="horizontal"
            android:gravity="center_vertical">

            <View
                android:id="@+id/primary_button_color_indicator"
                android:layout_width="12dp"
                android:layout_height="12dp"
                android:layout_marginEnd="8dp"
                android:background="@drawable/circle_background" />

            <TextView
                android:id="@+id/primary_button_name"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="一级按钮"
                android:textSize="16sp"
                android:textStyle="bold"
                android:textColor="?android:attr/textColorPrimary" />

        </LinearLayout>

        <!-- 关闭按钮 -->
        <ImageButton
            android:id="@+id/btn_close"
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:background="?android:attr/selectableItemBackgroundBorderless"
            android:src="@drawable/ic_close"
            android:contentDescription="关闭"
            android:scaleType="centerInside" />

    </LinearLayout>

    <!-- 二级按钮计数显示 -->
    <TextView
        android:id="@+id/secondary_button_count"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="二级按钮 (0)"
        android:textSize="14sp"
        android:textColor="?android:attr/textColorSecondary"
        android:paddingBottom="12dp"
        android:gravity="center" />

    <!-- 二级按钮网格 -->
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/secondary_buttons_grid"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="16dp"
        android:minHeight="120dp"
        android:maxHeight="300dp"
        android:scrollbars="vertical" />

    <!-- 空状态提示 -->
    <LinearLayout
        android:id="@+id/empty_state_layout"
        android:layout_width="match_parent"
        android:layout_height="120dp"
        android:orientation="vertical"
        android:gravity="center"
        android:visibility="gone">

        <ImageView
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:src="@drawable/ic_grid_view"
            android:alpha="0.5"
            android:layout_marginBottom="8dp" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="暂无二级按钮"
            android:textSize="14sp"
            android:textColor="?android:attr/textColorSecondary"
            android:layout_marginBottom="4dp" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="请先在按钮编辑界面添加二级按钮"
            android:textSize="12sp"
            android:textColor="?android:attr/textColorTertiary" />

    </LinearLayout>

</LinearLayout>
