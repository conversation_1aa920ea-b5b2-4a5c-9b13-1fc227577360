/*
 * Copyright (C) 2016 mendhak
 *
 * This file is part of GPSLogger for Android.
 *
 * GPSLogger for Android is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 2 of the License, or
 * (at your option) any later version.
 *
 * GPSLogger for Android is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with GPSLogger for Android.  If not, see <http://www.gnu.org/licenses/>.
 */

package com.mendhak.gpslogger.ui.preferences;

import android.content.Context;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.EditText;
import android.widget.Toast;
import androidx.appcompat.app.AlertDialog;
import androidx.preference.EditTextPreference;
import com.mendhak.gpslogger.R;
import com.mendhak.gpslogger.common.PreferenceHelper;
import com.mendhak.gpslogger.common.slf4j.Logs;
import org.slf4j.Logger;

/**
 * Custom preference for setting zip encryption password with confirmation
 */
public class ZipPasswordPreference extends EditTextPreference {
    private static final Logger LOG = Logs.of(ZipPasswordPreference.class);
    
    public ZipPasswordPreference(Context context, AttributeSet attrs, int defStyleAttr, int defStyleRes) {
        super(context, attrs, defStyleAttr, defStyleRes);
        init();
    }
    
    public ZipPasswordPreference(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init();
    }
    
    public ZipPasswordPreference(Context context, AttributeSet attrs) {
        super(context, attrs);
        init();
    }
    
    public ZipPasswordPreference(Context context) {
        super(context);
        init();
    }
    
    private void init() {
        updateSummary();
    }
    
    @Override
    protected void onClick() {
        showPasswordDialog();
    }
    
    private void showPasswordDialog() {
        AlertDialog.Builder builder = new AlertDialog.Builder(getContext());
        View dialogView = LayoutInflater.from(getContext())
            .inflate(R.layout.dialog_zip_password, null);
        
        EditText passwordEdit = dialogView.findViewById(R.id.password_edit);
        EditText confirmEdit = dialogView.findViewById(R.id.confirm_edit);
        
        // Pre-fill with current password (masked)
        String currentPassword = PreferenceHelper.getInstance().getZipPassword();
        if (!currentPassword.isEmpty()) {
            passwordEdit.setText(currentPassword);
            confirmEdit.setText(currentPassword);
        }
        
        builder.setView(dialogView)
            .setTitle(R.string.autosend_zip_password_title)
            .setPositiveButton(android.R.string.ok, (dialog, which) -> {
                String password = passwordEdit.getText().toString();
                String confirm = confirmEdit.getText().toString();
                
                if (validatePassword(password, confirm)) {
                    PreferenceHelper.getInstance().setZipPassword(password);
                    updateSummary();
                    Toast.makeText(getContext(), R.string.autosend_zip_password_set_success, 
                        Toast.LENGTH_SHORT).show();
                    LOG.info("Zip encryption password updated");
                }
            })
            .setNegativeButton(android.R.string.cancel, null)
            .setNeutralButton("Clear", (dialog, which) -> {
                // Clear password
                PreferenceHelper.getInstance().setZipPassword("");
                updateSummary();
                Toast.makeText(getContext(), "Zip password cleared", Toast.LENGTH_SHORT).show();
                LOG.info("Zip encryption password cleared");
            })
            .show();
    }
    
    private boolean validatePassword(String password, String confirm) {
        if (password.trim().isEmpty()) {
            Toast.makeText(getContext(), R.string.autosend_zip_password_empty_error, 
                Toast.LENGTH_SHORT).show();
            return false;
        }
        
        if (!password.equals(confirm)) {
            Toast.makeText(getContext(), R.string.autosend_zip_password_mismatch_error, 
                Toast.LENGTH_SHORT).show();
            return false;
        }
        
        if (password.length() < 4) {
            Toast.makeText(getContext(), "Password must be at least 4 characters", 
                Toast.LENGTH_SHORT).show();
            return false;
        }
        
        return true;
    }
    
    private void updateSummary() {
        String password = PreferenceHelper.getInstance().getZipPassword();
        if (password.isEmpty()) {
            setSummary("No password set - tap to set password");
        } else {
            setSummary("Password set (" + password.length() + " characters) - tap to change");
        }
    }
}
