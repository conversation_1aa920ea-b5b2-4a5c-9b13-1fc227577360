/*
 * Copyright (C) 2024 mendhak
 *
 * This file is part of GPSLogger for Android.
 *
 * GPSLogger is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 2 of the License, or
 * (at your option) any later version.
 *
 * GPSLogger is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with GPSLogger.  If not, see <http://www.gnu.org/licenses/>.
 */

package com.mendhak.gpslogger.ui.adapters;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.mendhak.gpslogger.R;
import com.mendhak.gpslogger.ui.models.LocationGroup;
import com.mendhak.gpslogger.ui.models.LocationPoint;
import com.mendhak.gpslogger.ui.models.LocationPointState;
import com.mendhak.gpslogger.ui.managers.StateManager;
import com.mendhak.gpslogger.ui.managers.ButtonConfigManager;
import com.mendhak.gpslogger.ui.managers.GroupIconManager;
import com.mendhak.gpslogger.ui.dialogs.GroupSettingsDialog;
import com.mendhak.gpslogger.ui.models.GroupIcon;
import com.mendhak.gpslogger.ui.utils.GroupDragCallback;

import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.Collections;

/**
 * 位置分组列表适配器
 */
public class LocationGroupAdapter extends RecyclerView.Adapter<LocationGroupAdapter.LocationGroupViewHolder>
        implements GroupDragCallback.OnItemMoveListener {

    private List<LocationGroup> locationGroups;
    private OnLocationGroupClickListener listener;
    private Map<String, Integer> groupPushIndexMap = new HashMap<>(); // 组顺序推送索引管理
    private Map<String, Set<Integer>> groupPushedPointsMap = new HashMap<>(); // 组内已推送位置点管理
    private Map<String, Set<Integer>> groupPassedPointsMap = new HashMap<>(); // 组内已通过位置点管理
    private Map<String, Set<Integer>> groupFailedPointsMap = new HashMap<>(); // 组内不通过位置点管理
    private StateManager stateManager; // 状态持久化管理器
    private ButtonConfigManager buttonConfigManager; // 按钮配置管理器
    private androidx.recyclerview.widget.ItemTouchHelper itemTouchHelper; // 拖动排序助手
    
    public interface OnLocationGroupClickListener {
        void onGroupHeaderClick(LocationGroup group);
        void onGroupSettingsClick(LocationGroup group);
        void onGroupSequentialPushClick(LocationGroup group);
        void onGroupQuickResetClick(LocationGroup group);
        void onLocationPointPassClick(LocationGroup group, LocationPoint point);
        void onLocationPointFailClick(LocationGroup group, LocationPoint point);
        void onLocationPointPushClick(LocationGroup group, LocationPoint point);
        void onLocationPointSequenceNumberLongClick(LocationGroup group, LocationPoint point);
        void onLocationPointDescriptionLongClick(LocationGroup group, LocationPoint point);
        void onLocationPointSetStartIndex(LocationGroup group, LocationPoint point, int pointIndex);
    }
    
    public LocationGroupAdapter(List<LocationGroup> locationGroups) {
        this.locationGroups = locationGroups;
    }
    
    public void setOnLocationGroupClickListener(OnLocationGroupClickListener listener) {
        this.listener = listener;
    }

    /**
     * 设置状态管理器
     */
    public void setStateManager(StateManager stateManager) {
        this.stateManager = stateManager;
        // 从持久化存储中恢复状态
        loadStatesFromPersistence();
    }

    /**
     * 设置按钮配置管理器
     */
    public void setButtonConfigManager(ButtonConfigManager buttonConfigManager) {
        this.buttonConfigManager = buttonConfigManager;
        notifyDataSetChanged(); // 刷新UI以应用新的按钮配置
    }

    /**
     * 设置拖动排序助手
     */
    public void setItemTouchHelper(androidx.recyclerview.widget.ItemTouchHelper itemTouchHelper) {
        this.itemTouchHelper = itemTouchHelper;
    }

    /**
     * 从持久化存储中加载状态
     */
    private void loadStatesFromPersistence() {
        if (stateManager == null || locationGroups == null) {
            return;
        }

        for (LocationGroup group : locationGroups) {
            String groupName = group.getGroupName();
            LocationPointState state = stateManager.getGroupState(groupName);

            // 恢复推送状态
            Set<Integer> pushedPoints = new HashSet<>(state.getPushedPointIndices());
            groupPushedPointsMap.put(groupName, pushedPoints);
            android.util.Log.d("LocationGroupAdapter", "Loaded pushed points for " + groupName + ": " + pushedPoints);

            // 恢复通过状态
            Set<Integer> passedPoints = new HashSet<>(state.getPassedPointIndices());
            groupPassedPointsMap.put(groupName, passedPoints);
            android.util.Log.d("LocationGroupAdapter", "Loaded passed points for " + groupName + ": " + passedPoints);

            // 恢复不通过状态
            Set<Integer> failedPoints = new HashSet<>(state.getFailedPointIndices());
            groupFailedPointsMap.put(groupName, failedPoints);
            android.util.Log.d("LocationGroupAdapter", "Loaded failed points for " + groupName + ": " + failedPoints);
        }

        notifyDataSetChanged();
    }
    
    @NonNull
    @Override
    public LocationGroupViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext())
                .inflate(R.layout.item_location_group, parent, false);
        return new LocationGroupViewHolder(view);
    }
    
    @Override
    public void onBindViewHolder(@NonNull LocationGroupViewHolder holder, int position) {
        LocationGroup group = locationGroups.get(position);
        holder.bind(group);
    }
    
    @Override
    public int getItemCount() {
        return locationGroups != null ? locationGroups.size() : 0;
    }
    
    public void updateLocationGroups(List<LocationGroup> newLocationGroups) {
        this.locationGroups = newLocationGroups;
        groupPushIndexMap.clear(); // 重置顺序推送状态
        groupPushedPointsMap.clear(); // 重置单点推送状态
        groupPassedPointsMap.clear(); // 重置通过状态
        groupFailedPointsMap.clear(); // 重置不通过状态

        // 重新加载持久化状态
        loadStatesFromPersistence();

        notifyDataSetChanged();
    }

    /**
     * 设置组的推送索引
     */
    public void setGroupPushIndex(String groupName, int pushIndex) {
        groupPushIndexMap.put(groupName, pushIndex);
        notifyDataSetChanged();
    }

    /**
     * 获取组的推送索引
     */
    public int getGroupPushIndex(String groupName) {
        return groupPushIndexMap.getOrDefault(groupName, 0);
    }

    /**
     * 清除所有推送状态
     */
    public void clearPushStates() {
        groupPushIndexMap.clear();
        groupPushedPointsMap.clear();
        notifyDataSetChanged();
    }

    /**
     * 标记单个位置点为已推送
     */
    public void markPointAsPushed(String groupName, int pointIndex) {
        Set<Integer> pushedPoints = groupPushedPointsMap.computeIfAbsent(groupName, k -> new HashSet<>());
        pushedPoints.add(pointIndex);
        android.util.Log.d("LocationGroupAdapter", "Marked point " + pointIndex + " as pushed for group " + groupName + ". Current pushed points: " + pushedPoints);

        // 保存到持久化存储
        if (stateManager != null) {
            stateManager.updatePushStatus(groupName, pointIndex, true);
            android.util.Log.d("LocationGroupAdapter", "Saved push status to StateManager for group " + groupName + ", point " + pointIndex);
        }

        notifyDataSetChanged();
    }

    /**
     * 获取组内已推送的位置点集合
     */
    public Set<Integer> getGroupPushedPoints(String groupName) {
        return groupPushedPointsMap.getOrDefault(groupName, new HashSet<>());
    }

    /**
     * 获取组内已通过的位置点集合
     */
    public Set<Integer> getGroupPassedPoints(String groupName) {
        return groupPassedPointsMap.getOrDefault(groupName, new HashSet<>());
    }

    /**
     * 获取组内不通过的位置点集合
     */
    public Set<Integer> getGroupFailedPoints(String groupName) {
        return groupFailedPointsMap.getOrDefault(groupName, new HashSet<>());
    }

    /**
     * 获取分组统计信息
     */
    public GroupStatistics getGroupStatistics(String groupName) {
        // 找到对应的分组
        LocationGroup group = null;
        for (LocationGroup g : locationGroups) {
            if (g.getGroupName().equals(groupName)) {
                group = g;
                break;
            }
        }

        if (group == null) {
            return new GroupStatistics(0, 0, 0, 0);
        }

        int totalPoints = group.getLocationPoints().size();
        int pushedCount = getGroupPushedPoints(groupName).size();
        int passedCount = getGroupPassedPoints(groupName).size();
        int failedCount = getGroupFailedPoints(groupName).size();

        return new GroupStatistics(totalPoints, pushedCount, passedCount, failedCount);
    }

    /**
     * 分组统计信息数据类
     */
    public static class GroupStatistics {
        public final int totalPoints;
        public final int pushedCount;
        public final int passedCount;
        public final int failedCount;

        public GroupStatistics(int totalPoints, int pushedCount, int passedCount, int failedCount) {
            this.totalPoints = totalPoints;
            this.pushedCount = pushedCount;
            this.passedCount = passedCount;
            this.failedCount = failedCount;
        }
    }

    /**
     * 为组级顺序推送设置连续的推送状态
     */
    public void setGroupSequentialPushIndex(String groupName, int pushIndex) {
        groupPushIndexMap.put(groupName, pushIndex);

        // 同时更新单点推送状态，标记前pushIndex个位置点为已推送
        Set<Integer> pushedPoints = groupPushedPointsMap.computeIfAbsent(groupName, k -> new HashSet<>());
        for (int i = 0; i < pushIndex; i++) {
            pushedPoints.add(i);
        }
        notifyDataSetChanged();
    }

    /**
     * 获取组内下一个未推送的位置点索引（智能跳过逻辑，考虑起点设置）
     * @param groupName 组名
     * @param totalPoints 组内总位置点数量
     * @return 下一个未推送的位置点索引，如果全部推送完成则返回totalPoints
     */
    public int getNextUnpushedPointIndex(String groupName, int totalPoints) {
        Set<Integer> pushedPoints = groupPushedPointsMap.getOrDefault(groupName, new HashSet<>());

        // 获取起点索引
        int startIndex = 0;
        if (stateManager != null) {
            startIndex = stateManager.getStartIndex(groupName);
        }

        // 从起点索引开始查找第一个未推送的位置点
        for (int i = startIndex; i < totalPoints; i++) {
            if (!pushedPoints.contains(i)) {
                return i; // 返回第一个未推送的位置点索引
            }
        }

        // 从起点开始的所有位置点都已推送
        return totalPoints;
    }

    /**
     * 标记组级顺序推送的单个位置点为已推送
     */
    public void markSequentialPointAsPushed(String groupName, int pointIndex) {
        // 更新单点推送状态
        Set<Integer> pushedPoints = groupPushedPointsMap.computeIfAbsent(groupName, k -> new HashSet<>());
        pushedPoints.add(pointIndex);

        // 保存到持久化存储
        if (stateManager != null) {
            stateManager.updatePushStatus(groupName, pointIndex, true);
        }

        // 更新组级推送索引（设置为已推送的最大连续索引+1）
        int maxContinuousIndex = 0;
        for (int i = 0; i < Integer.MAX_VALUE; i++) {
            if (pushedPoints.contains(i)) {
                maxContinuousIndex = i + 1;
            } else {
                break;
            }
        }
        groupPushIndexMap.put(groupName, maxContinuousIndex);

        notifyDataSetChanged();
    }
    
    class LocationGroupViewHolder extends RecyclerView.ViewHolder {
        private LinearLayout groupHeaderLayout;
        private ImageView groupIcon;
        private TextView groupNameText;
        private Button groupSequentialPushButton;
        private TextView pointCountText;
        private android.widget.ImageButton groupQuickResetButton;
        private ImageView dragHandle;
        private ImageView expandCollapseButton;
        private ImageView groupSettingsButton;
        private RecyclerView locationPointsRecycler;
        
        private LocationPointAdapter locationPointAdapter;
        
        public LocationGroupViewHolder(@NonNull View itemView) {
            super(itemView);
            
            groupHeaderLayout = itemView.findViewById(R.id.group_header_layout);
            groupIcon = itemView.findViewById(R.id.group_icon);
            groupNameText = itemView.findViewById(R.id.group_name_text);
            groupSequentialPushButton = itemView.findViewById(R.id.group_sequential_push_button);
            pointCountText = itemView.findViewById(R.id.point_count_text);
            groupQuickResetButton = itemView.findViewById(R.id.group_quick_reset_button);
            dragHandle = itemView.findViewById(R.id.drag_handle);
            expandCollapseButton = itemView.findViewById(R.id.expand_collapse_button);
            groupSettingsButton = itemView.findViewById(R.id.group_settings_button);
            locationPointsRecycler = itemView.findViewById(R.id.location_points_recycler);
            
            // 设置位置点RecyclerView
            locationPointsRecycler.setLayoutManager(new LinearLayoutManager(itemView.getContext()));
            locationPointAdapter = new LocationPointAdapter(null);
            locationPointAdapter.setButtonConfigManager(buttonConfigManager);
            locationPointsRecycler.setAdapter(locationPointAdapter);
            
            // 设置点击监听器
            groupHeaderLayout.setOnClickListener(v -> {
                if (listener != null) {
                    int position = getAdapterPosition();
                    if (position != RecyclerView.NO_POSITION) {
                        listener.onGroupHeaderClick(locationGroups.get(position));
                    }
                }
            });
            
            expandCollapseButton.setOnClickListener(v -> {
                if (listener != null) {
                    int position = getAdapterPosition();
                    if (position != RecyclerView.NO_POSITION) {
                        LocationGroup group = locationGroups.get(position);
                        group.toggleExpanded();
                        updateExpandCollapseIcon(group.isExpanded());
                        updateLocationPointsVisibility(group.isExpanded());
                        listener.onGroupHeaderClick(group);
                    }
                }
            });
            
            groupSettingsButton.setOnClickListener(v -> {
                if (listener != null) {
                    int position = getAdapterPosition();
                    if (position != RecyclerView.NO_POSITION) {
                        listener.onGroupSettingsClick(locationGroups.get(position));
                    }
                }
            });
            
            groupSequentialPushButton.setOnClickListener(v -> {
                if (listener != null) {
                    int position = getAdapterPosition();
                    if (position != RecyclerView.NO_POSITION) {
                        listener.onGroupSequentialPushClick(locationGroups.get(position));
                    }
                }
            });

            groupQuickResetButton.setOnClickListener(v -> {
                if (listener != null) {
                    int position = getAdapterPosition();
                    if (position != RecyclerView.NO_POSITION) {
                        listener.onGroupQuickResetClick(locationGroups.get(position));
                    }
                }
            });
            
            // 设置位置点适配器监听器
            locationPointAdapter.setOnLocationPointClickListener(new LocationPointAdapter.OnLocationPointClickListener() {
                @Override
                public void onPassButtonClick(LocationPoint point) {
                    if (listener != null) {
                        int position = getAdapterPosition();
                        if (position != RecyclerView.NO_POSITION) {
                            listener.onLocationPointPassClick(locationGroups.get(position), point);
                        }
                    }
                }
                
                @Override
                public void onFailButtonClick(LocationPoint point) {
                    if (listener != null) {
                        int position = getAdapterPosition();
                        if (position != RecyclerView.NO_POSITION) {
                            listener.onLocationPointFailClick(locationGroups.get(position), point);
                        }
                    }
                }
                
                @Override
                public void onPushButtonClick(LocationPoint point) {
                    if (listener != null) {
                        int position = getAdapterPosition();
                        if (position != RecyclerView.NO_POSITION) {
                            listener.onLocationPointPushClick(locationGroups.get(position), point);
                        }
                    }
                }

                @Override
                public void onSequenceNumberLongClick(LocationPoint point) {
                    if (listener != null) {
                        int position = getAdapterPosition();
                        if (position != RecyclerView.NO_POSITION) {
                            listener.onLocationPointSequenceNumberLongClick(locationGroups.get(position), point);
                        }
                    }
                }

                @Override
                public void onDescriptionLongClick(LocationPoint point) {
                    if (listener != null) {
                        int position = getAdapterPosition();
                        if (position != RecyclerView.NO_POSITION) {
                            listener.onLocationPointDescriptionLongClick(locationGroups.get(position), point);
                        }
                    }
                }

                @Override
                public void onPassButtonLongClick(LocationPoint point) {
                    if (listener != null) {
                        int position = getAdapterPosition();
                        if (position != RecyclerView.NO_POSITION) {
                            listener.onLocationPointDescriptionLongClick(locationGroups.get(position), point);
                        }
                    }
                }

                @Override
                public void onFailButtonLongClick(LocationPoint point) {
                    if (listener != null) {
                        int position = getAdapterPosition();
                        if (position != RecyclerView.NO_POSITION) {
                            listener.onLocationPointDescriptionLongClick(locationGroups.get(position), point);
                        }
                    }
                }

                @Override
                public void onSetStartIndexClick(LocationPoint point, int pointIndex) {
                    if (listener != null) {
                        int position = getAdapterPosition();
                        if (position != RecyclerView.NO_POSITION) {
                            listener.onLocationPointSetStartIndex(locationGroups.get(position), point, pointIndex);
                        }
                    }
                }
            });

            // 设置拖动手柄触摸监听器
            dragHandle.setOnTouchListener((v, event) -> {
                if (event.getAction() == android.view.MotionEvent.ACTION_DOWN) {
                    if (itemTouchHelper != null) {
                        itemTouchHelper.startDrag(this);
                    }
                }
                return false;
            });
        }
        
        public void bind(LocationGroup group) {
            groupNameText.setText(group.getGroupName());
            pointCountText.setText("(" + group.getPointCount() + ")");

            // 更新分组图标
            updateGroupIcon(group);

            // 更新展开/折叠图标
            updateExpandCollapseIcon(group.isExpanded());

            // 更新位置点列表
            locationPointAdapter.updateLocationPoints(group.getLocationPoints());

            // 更新位置点的推送状态
            Set<Integer> pushedPoints = groupPushedPointsMap.getOrDefault(group.getGroupName(), new HashSet<>());
            locationPointAdapter.setPushedPointIndices(pushedPoints);

            // 更新位置点的通过/不通过状态
            Set<Integer> passedPoints = groupPassedPointsMap.getOrDefault(group.getGroupName(), new HashSet<>());
            Set<Integer> failedPoints = groupFailedPointsMap.getOrDefault(group.getGroupName(), new HashSet<>());
            locationPointAdapter.setPassedPointIndices(passedPoints);
            locationPointAdapter.setFailedPointIndices(failedPoints);

            // 更新起点索引
            if (stateManager != null) {
                int startIndex = stateManager.getStartIndex(group.getGroupName());
                locationPointAdapter.setStartIndex(startIndex);
            }

            // 更新位置点列表可见性
            updateLocationPointsVisibility(group.isExpanded());
        }
        
        private void updateExpandCollapseIcon(boolean isExpanded) {
            if (isExpanded) {
                expandCollapseButton.setImageResource(R.drawable.ic_expand_less);
            } else {
                expandCollapseButton.setImageResource(R.drawable.ic_expand_more_24dp);
            }
        }
        
        private void updateLocationPointsVisibility(boolean isExpanded) {
            locationPointsRecycler.setVisibility(isExpanded ? View.VISIBLE : View.GONE);
        }

        private void updateGroupIcon(LocationGroup group) {
            // 根据分组的图标ID设置图标
            String iconId = group.getIconId();
            if (iconId == null || iconId.isEmpty()) {
                iconId = GroupIconManager.getDefaultIconId();
            }

            GroupIcon groupIconModel = GroupIconManager.getIconById(iconId);
            if (groupIconModel != null) {
                groupIcon.setImageResource(groupIconModel.getIconResource());
            }
        }
    }

    /**
     * 标记位置点为通过状态
     */
    public void markPointAsPassed(String groupName, int pointIndex) {
        Set<Integer> passedPoints = groupPassedPointsMap.computeIfAbsent(groupName, k -> new HashSet<>());
        Set<Integer> failedPoints = groupFailedPointsMap.computeIfAbsent(groupName, k -> new HashSet<>());

        passedPoints.add(pointIndex);
        failedPoints.remove(pointIndex); // 移除不通过状态

        // 保存到持久化存储
        if (stateManager != null) {
            stateManager.updatePassStatus(groupName, pointIndex);
        }

        // 找到对应的组并刷新
        for (int i = 0; i < locationGroups.size(); i++) {
            LocationGroup group = locationGroups.get(i);
            if (group.getGroupName().equals(groupName)) {
                notifyItemChanged(i);
                break;
            }
        }
    }

    /**
     * 标记位置点为不通过状态
     */
    public void markPointAsFailed(String groupName, int pointIndex) {
        Set<Integer> passedPoints = groupPassedPointsMap.computeIfAbsent(groupName, k -> new HashSet<>());
        Set<Integer> failedPoints = groupFailedPointsMap.computeIfAbsent(groupName, k -> new HashSet<>());

        failedPoints.add(pointIndex);
        passedPoints.remove(pointIndex); // 移除通过状态

        // 保存到持久化存储
        if (stateManager != null) {
            stateManager.updateFailStatus(groupName, pointIndex);
        }

        // 找到对应的组并刷新
        for (int i = 0; i < locationGroups.size(); i++) {
            LocationGroup group = locationGroups.get(i);
            if (group.getGroupName().equals(groupName)) {
                notifyItemChanged(i);
                break;
            }
        }
    }

    /**
     * 清除位置点的通过/不通过状态
     */
    public void clearPointPassFailStatus(String groupName, int pointIndex) {
        Set<Integer> passedPoints = groupPassedPointsMap.get(groupName);
        Set<Integer> failedPoints = groupFailedPointsMap.get(groupName);

        if (passedPoints != null) {
            passedPoints.remove(pointIndex);
        }
        if (failedPoints != null) {
            failedPoints.remove(pointIndex);
        }

        // 保存到持久化存储
        if (stateManager != null) {
            stateManager.clearPassFailStatus(groupName, pointIndex);
        }

        // 找到对应的组并刷新
        for (int i = 0; i < locationGroups.size(); i++) {
            LocationGroup group = locationGroups.get(i);
            if (group.getGroupName().equals(groupName)) {
                notifyItemChanged(i);
                break;
            }
        }
    }

    /**
     * 清除位置点的推送状态
     */
    public void clearPointPushStatus(String groupName, int pointIndex) {
        Set<Integer> pushedPoints = groupPushedPointsMap.get(groupName);
        if (pushedPoints != null) {
            pushedPoints.remove(pointIndex);

            // 保存到持久化存储
            if (stateManager != null) {
                stateManager.updatePushStatus(groupName, pointIndex, false);
            }

            // 找到对应的组并刷新
            for (int i = 0; i < locationGroups.size(); i++) {
                LocationGroup group = locationGroups.get(i);
                if (group.getGroupName().equals(groupName)) {
                    notifyItemChanged(i);
                    break;
                }
            }
        }
    }

    /**
     * 设置分组的顺序推送起点索引
     */
    public void setStartIndex(String groupName, int startIndex) {
        // 保存到持久化存储
        if (stateManager != null) {
            stateManager.setStartIndex(groupName, startIndex);
        }

        // 找到对应的组并刷新整个项目，这样bind方法会重新调用并更新起点索引
        for (int i = 0; i < locationGroups.size(); i++) {
            LocationGroup group = locationGroups.get(i);
            if (group.getGroupName().equals(groupName)) {
                notifyItemChanged(i);
                break;
            }
        }
    }

    /**
     * 清除指定分组的所有状态数据
     */
    public void clearGroupState(String groupName) {
        // 清除内存中的状态
        groupPushedPointsMap.remove(groupName);
        groupPassedPointsMap.remove(groupName);
        groupFailedPointsMap.remove(groupName);

        // 清除持久化存储中的状态
        if (stateManager != null) {
            stateManager.clearGroupState(groupName);
        }

        // 找到对应的组并刷新
        for (int i = 0; i < locationGroups.size(); i++) {
            LocationGroup group = locationGroups.get(i);
            if (group.getGroupName().equals(groupName)) {
                notifyItemChanged(i);
                break;
            }
        }
    }



    // GroupDragCallback.OnItemMoveListener 实现
    @Override
    public boolean onItemMove(int fromPosition, int toPosition) {
        if (fromPosition < toPosition) {
            for (int i = fromPosition; i < toPosition; i++) {
                Collections.swap(locationGroups, i, i + 1);
            }
        } else {
            for (int i = fromPosition; i > toPosition; i--) {
                Collections.swap(locationGroups, i, i - 1);
            }
        }

        notifyItemMoved(fromPosition, toPosition);

        // 更新分组顺序到StateManager
        updateGroupOrderInStateManager();

        return true;
    }

    @Override
    public void onItemDragStart(RecyclerView.ViewHolder viewHolder) {
        // 拖动开始时的视觉反馈
        if (viewHolder.itemView != null) {
            viewHolder.itemView.setAlpha(0.7f);
            viewHolder.itemView.setScaleX(1.05f);
            viewHolder.itemView.setScaleY(1.05f);
        }
    }

    @Override
    public void onItemDragEnd(RecyclerView.ViewHolder viewHolder) {
        // 拖动结束时恢复外观
        if (viewHolder.itemView != null) {
            viewHolder.itemView.setAlpha(1.0f);
            viewHolder.itemView.setScaleX(1.0f);
            viewHolder.itemView.setScaleY(1.0f);
        }
    }

    /**
     * 更新分组顺序到StateManager
     */
    private void updateGroupOrderInStateManager() {
        if (stateManager != null) {
            java.util.List<String> groupNames = new java.util.ArrayList<>();
            for (LocationGroup group : locationGroups) {
                groupNames.add(group.getGroupName());
            }
            stateManager.setGroupOrder(groupNames);
        }
    }

    /**
     * 根据StateManager中的顺序排序分组
     */
    public void sortGroupsByOrder() {
        if (stateManager != null) {
            java.util.List<String> savedOrder = stateManager.getGroupOrder();
            if (!savedOrder.isEmpty()) {
                // 根据保存的顺序重新排列分组
                java.util.List<LocationGroup> sortedGroups = new java.util.ArrayList<>();

                // 首先添加按保存顺序排列的分组
                for (String groupName : savedOrder) {
                    for (LocationGroup group : locationGroups) {
                        if (group.getGroupName().equals(groupName)) {
                            sortedGroups.add(group);
                            break;
                        }
                    }
                }

                // 然后添加不在保存顺序中的新分组
                for (LocationGroup group : locationGroups) {
                    if (!savedOrder.contains(group.getGroupName())) {
                        sortedGroups.add(group);
                        // 同时添加到StateManager的顺序中
                        stateManager.addGroupToOrder(group.getGroupName());
                    }
                }

                locationGroups.clear();
                locationGroups.addAll(sortedGroups);
                notifyDataSetChanged();
            }
        }
    }
}
