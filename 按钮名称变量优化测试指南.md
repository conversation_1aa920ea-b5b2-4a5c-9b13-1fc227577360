# 按钮名称变量优化测试指南

## 🎯 功能概述

优化了annotation变量系统中的按钮名称变量，使用更精确的变量系统：

### 新的按钮名称变量
- **{primary_button_name}** - 一级按钮名称
- **{secondary_button_name}** - 二级按钮名称（一级按钮点击时为空）

### 重要变更
- **完全删除了{button_name}变量** - 请使用新的变量替代

## 🔧 变量行为

### 一级按钮点击时
- `{primary_button_name}` = 一级按钮名称（如："测试按钮"）
- `{secondary_button_name}` = 空字符串

### 二级按钮点击时
- `{primary_button_name}` = 一级按钮名称（如："测试按钮"）
- `{secondary_button_name}` = 二级按钮名称（如："增加"）

## 📱 测试步骤

### 第一步：准备测试环境

1. **安装更新的APK**
   - 构建并安装包含新变量功能的APK
   - 启动GPSLogger应用

2. **配置annotation模板**
   - 进入设置 → 记录细节设置 → 编辑Annotation模板
   - 启用"启用Annotation模板"
   - 设置测试模板：
   ```
   按钮名称变量测试：
   一级按钮名：{primary_button_name}
   二级按钮名：{secondary_button_name}
   组名：{group_name}
   时间：{date} {time}
   ```

3. **启用文件记录**
   - 勾选"记录到纯文本文件"
   - 确保能够查看生成的txt文件

### 第二步：创建测试按钮

#### 2.1 创建一级按钮
1. **进入annotation界面**
2. **创建测试按钮**：
   - 长按分组 → 管理按钮 → 添加按钮 → 创建新按钮
   - 按钮文本：输入"主按钮测试"
   - 触发模式：选择"语音输入"
   - 点击"创建"

#### 2.2 创建二级按钮
1. **为主按钮添加二级按钮**：
   - 长按"主按钮测试" → 管理二级按钮 → 添加二级按钮
   - 按钮文本：输入"子按钮A"
   - 触发模式：选择"语音输入"
   - 点击"创建"

2. **添加第二个二级按钮**：
   - 继续添加二级按钮
   - 按钮文本：输入"子按钮B"
   - 触发模式：选择"计数器"
   - 点击"创建"

### 第三步：测试一级按钮变量

1. **开始记录**：
   - 返回主界面，点击开始记录
   - 确认GPS定位正常

2. **测试一级按钮**：
   - 点击"主按钮测试"按钮
   - 进行语音输入（如："这是一级按钮测试"）
   - 完成语音输入

3. **检查输出文件**：
   ```
   按钮名称变量测试：
   一级按钮名：主按钮测试
   二级按钮名：
   组名：默认
   时间：2024-XX-XX XX:XX:XX
   ```

### 第四步：测试二级按钮变量

1. **测试二级按钮A（语音模式）**：
   - 点击"主按钮测试"，选择"子按钮A"
   - 进行语音输入（如："这是二级按钮A测试"）
   - 完成语音输入

2. **检查输出文件**：
   ```
   按钮名称变量测试：
   一级按钮名：主按钮测试
   二级按钮名：子按钮A
   组名：默认
   时间：2024-XX-XX XX:XX:XX
   ```

3. **测试二级按钮B（计数器模式）**：
   - 点击"主按钮测试"，选择"子按钮B"
   - 按钮会直接触发计数器模式

4. **检查输出文件**：
   ```
   按钮名称变量测试：
   一级按钮名：主按钮测试
   二级按钮名：子按钮B
   组名：默认
   时间：2024-XX-XX XX:XX:XX
   ```

## ✅ 预期结果验证

### 一级按钮测试结果
- ✅ `{primary_button_name}` 显示一级按钮名称
- ✅ `{secondary_button_name}` 为空

### 二级按钮测试结果
- ✅ `{primary_button_name}` 显示一级按钮名称
- ✅ `{secondary_button_name}` 显示二级按钮名称

### 变量系统验证
- ✅ 新变量提供精确的按钮信息
- ✅ 完全替代了原有的button_name变量

## 🔧 技术实现亮点

### 智能按钮名称解析
- **层级识别**：自动识别" > "分隔符
- **向后兼容**：保留原有`{button_name}`变量
- **精确分离**：提供独立的一级和二级按钮名称

### 简洁高效
- **精确变量**：只提供需要的按钮名称信息
- **即时生效**：新变量立即可用
- **性能优化**：高效的字符串解析

## 📝 使用建议

### 推荐的模板格式
```
详细按钮信息：
主按钮：{primary_button_name}
子按钮：{secondary_button_name}
内容：{voice_text}
时间：{datetime}
```

### 条件显示技巧
```
按钮：{primary_button_name}{secondary_button_name:? > {secondary_button_name}}
内容：{voice_text}
```

这个优化为用户提供了更灵活的按钮名称变量系统，特别适合复杂的二级按钮结构和详细的记录需求。
