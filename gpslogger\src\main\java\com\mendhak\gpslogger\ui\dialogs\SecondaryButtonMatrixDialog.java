/*
 * Copyright (C) 2016 mendhak
 *
 * This file is part of GPSLogger for Android.
 *
 * GPSLogger for Android is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 2 of the License, or
 * (at your option) any later version.
 *
 * GPSLogger for Android is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with GPSLogger for Android.  If not, see <http://www.gnu.org/licenses/>.
 */

package com.mendhak.gpslogger.ui.dialogs;

import android.app.Dialog;
import android.content.Context;
import android.graphics.Color;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.Button;
import android.widget.ImageButton;
import android.widget.TextView;
import androidx.appcompat.app.AlertDialog;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.mendhak.gpslogger.R;
import com.mendhak.gpslogger.ui.fragments.display.AnnotationViewFragment.ButtonWrapper;
import com.mendhak.gpslogger.ui.fragments.display.AnnotationViewFragment.SecondaryButton;
import com.mendhak.gpslogger.ui.adapters.SecondaryButtonAdapter;
import com.mendhak.gpslogger.ui.components.SecondaryButtonManager;

import java.util.List;

/**
 * 二级按钮矩阵对话框
 * 显示一级按钮下的所有二级按钮，支持点击触发和管理功能
 */
public class SecondaryButtonMatrixDialog {
    
    public interface OnSecondaryButtonInteractionListener {
        void onSecondaryButtonClick(SecondaryButton secondaryButton, ButtonWrapper primaryButton);
        void onAddSecondaryButton(ButtonWrapper primaryButton);
        void onManageSecondaryButtons(ButtonWrapper primaryButton);
        void onDialogClosed();
    }
    
    private Context context;
    private ButtonWrapper primaryButton;
    private OnSecondaryButtonInteractionListener listener;
    private Dialog dialog;
    
    // UI组件
    private TextView primaryButtonName;
    private View primaryButtonColorIndicator;
    private TextView secondaryButtonCount;
    private RecyclerView secondaryButtonsGrid;
    private View emptyStateLayout;
    private ImageButton btnClose;
    
    private SecondaryButtonAdapter adapter;
    
    public SecondaryButtonMatrixDialog(Context context, ButtonWrapper primaryButton, 
                                     OnSecondaryButtonInteractionListener listener) {
        this.context = context;
        this.primaryButton = primaryButton;
        this.listener = listener;
    }
    
    public void show() {
        if (context == null || primaryButton == null) {
            return;
        }
        
        View dialogView = LayoutInflater.from(context).inflate(R.layout.dialog_secondary_button_matrix, null);
        initializeViews(dialogView);
        setupPrimaryButtonInfo();
        setupSecondaryButtonGrid();
        setupActionButtons();
        
        AlertDialog.Builder builder = new AlertDialog.Builder(context);
        builder.setView(dialogView);
        
        dialog = builder.create();
        
        // 设置对话框样式
        if (dialog.getWindow() != null) {
            dialog.getWindow().setBackgroundDrawableResource(android.R.color.transparent);
        }
        
        dialog.show();
    }
    
    private void initializeViews(View dialogView) {
        primaryButtonName = dialogView.findViewById(R.id.primary_button_name);
        primaryButtonColorIndicator = dialogView.findViewById(R.id.primary_button_color_indicator);
        secondaryButtonCount = dialogView.findViewById(R.id.secondary_button_count);
        secondaryButtonsGrid = dialogView.findViewById(R.id.secondary_buttons_grid);
        emptyStateLayout = dialogView.findViewById(R.id.empty_state_layout);
        btnClose = dialogView.findViewById(R.id.btn_close);
    }
    
    private void setupPrimaryButtonInfo() {
        // 设置一级按钮名称
        primaryButtonName.setText(primaryButton.getText());
        
        // 设置一级按钮颜色指示器
        try {
            int color = Color.parseColor(primaryButton.getColor());
            primaryButtonColorIndicator.setBackgroundColor(color);
        } catch (Exception e) {
            primaryButtonColorIndicator.setBackgroundColor(Color.parseColor("#808080"));
        }
        
        // 更新二级按钮计数
        updateSecondaryButtonCount();
    }
    
    private void setupSecondaryButtonGrid() {
        // 设置网格布局管理器（3列）
        GridLayoutManager layoutManager = new GridLayoutManager(context, 3);
        secondaryButtonsGrid.setLayoutManager(layoutManager);
        
        // 创建适配器
        List<SecondaryButton> secondaryButtons = SecondaryButtonManager.getSortedSecondaryButtons(primaryButton);
        adapter = new SecondaryButtonAdapter(secondaryButtons, new SecondaryButtonAdapter.OnSecondaryButtonClickListener() {
            @Override
            public void onSecondaryButtonClick(SecondaryButton secondaryButton) {
                if (listener != null) {
                    listener.onSecondaryButtonClick(secondaryButton, primaryButton);
                }
                dismiss();
            }
            
            @Override
            public void onSecondaryButtonLongClick(SecondaryButton secondaryButton) {
                // 长按可以触发编辑功能
                if (listener != null) {
                    listener.onManageSecondaryButtons(primaryButton);
                }
            }
        });
        
        secondaryButtonsGrid.setAdapter(adapter);
        
        // 更新空状态显示
        updateEmptyState();
    }
    
    private void setupActionButtons() {
        // 关闭按钮
        btnClose.setOnClickListener(v -> dismiss());
    }
    
    private void updateSecondaryButtonCount() {
        int count = primaryButton.getSecondaryButtonCount();
        secondaryButtonCount.setText("二级按钮 (" + count + ")");
    }
    
    private void updateEmptyState() {
        boolean isEmpty = !primaryButton.hasSecondaryButtons();
        emptyStateLayout.setVisibility(isEmpty ? View.VISIBLE : View.GONE);
        secondaryButtonsGrid.setVisibility(isEmpty ? View.GONE : View.VISIBLE);
    }
    
    public void dismiss() {
        if (dialog != null && dialog.isShowing()) {
            dialog.dismiss();
        }
        if (listener != null) {
            listener.onDialogClosed();
        }
    }
    
    public boolean isShowing() {
        return dialog != null && dialog.isShowing();
    }
    
    /**
     * 刷新对话框内容
     */
    public void refresh() {
        if (adapter != null) {
            List<SecondaryButton> updatedButtons = SecondaryButtonManager.getSortedSecondaryButtons(primaryButton);
            adapter.updateButtons(updatedButtons);
        }
        updateSecondaryButtonCount();
        updateEmptyState();
    }
}
