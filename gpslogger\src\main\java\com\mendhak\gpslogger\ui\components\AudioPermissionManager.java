/*
 * Copyright (C) 2016 mendhak
 *
 * This file is part of GPSLogger for Android.
 *
 * GPSLogger is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 2 of the License, or
 * (at your option) any later version.
 *
 * GPSLogger is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with GPSLogger.  If not, see <http://www.gnu.org/licenses/>.
 */

package com.mendhak.gpslogger.ui.components;

import android.Manifest;
import android.content.Context;
import android.content.pm.PackageManager;
import androidx.core.content.ContextCompat;
import com.mendhak.gpslogger.common.slf4j.Logs;
import org.slf4j.Logger;

/**
 * Unified audio permission manager for consistent permission checking
 * across different audio recording components.
 */
public class AudioPermissionManager {
    
    private static final Logger LOG = Logs.of(AudioPermissionManager.class);
    
    // Permission constants
    public static final String RECORD_AUDIO_PERMISSION = Manifest.permission.RECORD_AUDIO;
    
    // Error message constants
    public static final String PERMISSION_DENIED_MESSAGE = "需要录音权限才能使用录音功能";
    public static final String PERMISSION_CHECK_FAILED_MESSAGE = "权限检查失败";
    
    /**
     * Permission check result
     */
    public static class PermissionResult {
        public final boolean granted;
        public final String errorMessage;
        public final String source;
        
        public PermissionResult(boolean granted, String errorMessage, String source) {
            this.granted = granted;
            this.errorMessage = errorMessage;
            this.source = source;
        }
        
        public static PermissionResult granted(String source) {
            return new PermissionResult(true, null, source);
        }
        
        public static PermissionResult denied(String errorMessage, String source) {
            return new PermissionResult(false, errorMessage, source);
        }
    }
    
    /**
     * Permission callback interface
     */
    public interface PermissionCallback {
        void onPermissionResult(PermissionResult result);
    }
    
    /**
     * Check if RECORD_AUDIO permission is granted
     * 
     * @param context Application context
     * @param source Source component requesting permission check (for logging)
     * @return PermissionResult with check result and details
     */
    public static PermissionResult checkRecordAudioPermission(Context context, String source) {
        if (context == null) {
            LOG.error("Context is null for permission check from source: {}", source);
            return PermissionResult.denied(PERMISSION_CHECK_FAILED_MESSAGE, source);
        }
        
        try {
            boolean hasPermission = ContextCompat.checkSelfPermission(context, RECORD_AUDIO_PERMISSION) 
                                  == PackageManager.PERMISSION_GRANTED;
            
            if (hasPermission) {
                LOG.debug("RECORD_AUDIO permission granted for source: {}", source);
                return PermissionResult.granted(source);
            } else {
                LOG.warn("RECORD_AUDIO permission not granted for source: {}", source);
                return PermissionResult.denied(PERMISSION_DENIED_MESSAGE, source);
            }
            
        } catch (Exception e) {
            LOG.error("Error checking RECORD_AUDIO permission for source: {}", source, e);
            return PermissionResult.denied(PERMISSION_CHECK_FAILED_MESSAGE + ": " + e.getMessage(), source);
        }
    }
    
    /**
     * Check permission and execute callback
     * 
     * @param context Application context
     * @param source Source component requesting permission check
     * @param callback Callback to execute with result
     */
    public static void checkRecordAudioPermission(Context context, String source, PermissionCallback callback) {
        if (callback == null) {
            LOG.warn("Permission callback is null for source: {}", source);
            return;
        }
        
        PermissionResult result = checkRecordAudioPermission(context, source);
        callback.onPermissionResult(result);
    }
    
    /**
     * Simple boolean check for RECORD_AUDIO permission
     * 
     * @param context Application context
     * @return true if permission is granted, false otherwise
     */
    public static boolean hasRecordAudioPermission(Context context) {
        return checkRecordAudioPermission(context, "SimpleCheck").granted;
    }
    
    /**
     * Log permission check result for debugging
     * 
     * @param result Permission check result
     */
    public static void logPermissionResult(PermissionResult result) {
        if (result.granted) {
            LOG.debug("Permission check successful for source: {}", result.source);
        } else {
            LOG.warn("Permission check failed for source: {} - {}", result.source, result.errorMessage);
        }
    }
    
    /**
     * Get user-friendly error message based on permission result
     * 
     * @param result Permission check result
     * @return User-friendly error message
     */
    public static String getUserFriendlyErrorMessage(PermissionResult result) {
        if (result.granted) {
            return null;
        }
        
        // Customize error message based on source if needed
        if (result.source != null) {
            if (result.source.contains("后台") || result.source.contains("Background")) {
                return "后台录音需要录音权限，请在设置中授予权限";
            } else if (result.source.contains("通知栏") || result.source.contains("Notification")) {
                return "通知栏语音输入需要录音权限";
            }
        }
        
        return result.errorMessage != null ? result.errorMessage : PERMISSION_DENIED_MESSAGE;
    }
    
    /**
     * Check if permission error is recoverable (user can grant permission)
     * 
     * @param result Permission check result
     * @return true if error is recoverable, false otherwise
     */
    public static boolean isRecoverableError(PermissionResult result) {
        if (result.granted) {
            return false;
        }
        
        // Permission denied is recoverable (user can grant it)
        // System errors might not be recoverable
        return result.errorMessage != null && 
               result.errorMessage.contains(PERMISSION_DENIED_MESSAGE);
    }
}
