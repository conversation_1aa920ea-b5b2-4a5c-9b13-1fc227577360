/*
 * Copyright (C) 2024 mendhak
 *
 * This file is part of GPSLogger for Android.
 *
 * GPSLogger is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 2 of the License, or
 * (at your option) any later version.
 *
 * GPSLogger is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with GPSLogger.  If not, see <http://www.gnu.org/licenses/>.
 */

package com.mendhak.gpslogger.ui.components.external;

import android.content.Context;
import android.graphics.PixelFormat;
import android.text.InputType;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.WindowManager;
import android.view.inputmethod.InputMethodManager;
import android.widget.Button;
import android.widget.EditText;
import android.widget.TextView;
import android.widget.Toast;
import com.mendhak.gpslogger.R;
import com.mendhak.gpslogger.common.slf4j.Logs;
import com.mendhak.gpslogger.common.events.CommandEvents;
import com.mendhak.gpslogger.ui.components.template.providers.BasicVariableProvider;
import com.mendhak.gpslogger.common.Session;
import de.greenrobot.event.EventBus;
import org.slf4j.Logger;

/**
 * Manager for displaying overlay text input dialog
 * Allows text input from background without switching to foreground
 */
public class OverlayTextInputManager {
    
    private static final Logger LOG = Logs.of(OverlayTextInputManager.class);
    
    private final Context context;
    private WindowManager windowManager;
    private View overlayView;
    private boolean isShowing = false;
    
    /**
     * Interface for text input result callback
     */
    public interface OnTextInputListener {
        /**
         * Called when user confirms text input
         * @param inputText The text entered by user
         * @param source The source that triggered the input
         */
        void onTextInputResult(String inputText, String source);
        
        /**
         * Called when user cancels text input
         * @param source The source that triggered the input
         */
        void onTextInputCancelled(String source);
    }
    
    /**
     * Constructor
     * @param context Android context
     */
    public OverlayTextInputManager(Context context) {
        this.context = context.getApplicationContext();
        this.windowManager = (WindowManager) context.getSystemService(Context.WINDOW_SERVICE);
    }
    
    /**
     * Show overlay text input dialog
     * @param source The source that triggered the input
     * @param listener Result callback listener
     */
    public void showTextInputOverlay(String source, OnTextInputListener listener) {
        LOG.debug("Showing overlay text input for source: {}", source);
        
        // Check overlay permission
        if (!OverlayPermissionManager.hasOverlayPermission(context)) {
            LOG.warn("Overlay permission not granted, cannot show overlay text input");
            Toast.makeText(context, "需要悬浮窗权限才能在后台弹出文本输入框", Toast.LENGTH_LONG).show();
            OverlayPermissionManager.requestOverlayPermission(context);
            if (listener != null) {
                listener.onTextInputCancelled(source);
            }
            return;
        }
        
        // Prevent multiple overlays
        if (isShowing) {
            LOG.warn("Overlay text input already showing");
            return;
        }
        
        try {
            createOverlayView(source, listener);
            addOverlayToWindow();
            isShowing = true;
            LOG.info("Overlay text input shown successfully for source: {}", source);
        } catch (Exception e) {
            LOG.error("Failed to show overlay text input", e);
            if (listener != null) {
                listener.onTextInputCancelled(source);
            }
        }
    }
    
    /**
     * Hide overlay text input dialog
     */
    public void hideTextInputOverlay() {
        if (!isShowing || overlayView == null) {
            return;
        }
        
        try {
            // Hide keyboard first
            hideKeyboard();
            
            // Remove overlay from window
            windowManager.removeView(overlayView);
            overlayView = null;
            isShowing = false;
            LOG.debug("Overlay text input hidden");
        } catch (Exception e) {
            LOG.error("Failed to hide overlay text input", e);
        }
    }
    
    /**
     * Check if overlay is currently showing
     * @return true if showing, false otherwise
     */
    public boolean isShowing() {
        return isShowing;
    }
    
    /**
     * Create overlay view with text input interface
     */
    private void createOverlayView(String source, OnTextInputListener listener) {
        // Inflate overlay layout
        LayoutInflater inflater = LayoutInflater.from(context);
        overlayView = inflater.inflate(R.layout.overlay_text_input, null);
        
        // Get UI components
        TextView titleText = overlayView.findViewById(R.id.overlay_title);
        EditText inputEditText = overlayView.findViewById(R.id.overlay_input);
        Button confirmButton = overlayView.findViewById(R.id.overlay_confirm);
        Button cancelButton = overlayView.findViewById(R.id.overlay_cancel);

        // Set title
        titleText.setText("文本输入");
        
        // Configure input field
        inputEditText.setInputType(InputType.TYPE_CLASS_TEXT |
                                 InputType.TYPE_TEXT_FLAG_MULTI_LINE |
                                 InputType.TYPE_TEXT_FLAG_CAP_SENTENCES);
        inputEditText.setMaxLines(3);
        inputEditText.setHint("请输入文本内容...");

        // 添加点击监听器，确保手动点击时也能弹出键盘
        inputEditText.setOnClickListener(v -> {
            LOG.debug("EditText clicked, attempting to show keyboard");
            showKeyboardWithRetry(inputEditText, 0);
        });

        // 添加焦点监听器
        inputEditText.setOnFocusChangeListener((v, hasFocus) -> {
            if (hasFocus) {
                LOG.debug("EditText gained focus, attempting to show keyboard");
                v.post(() -> showKeyboardWithRetry(inputEditText, 0));
            }
        });

        // Set button listeners
        confirmButton.setOnClickListener(v -> {
            String inputText = inputEditText.getText().toString().trim();
            LOG.info("Overlay text input confirmed: '{}' from source: {}", inputText, source);
            
            hideTextInputOverlay();
            
            if (listener != null) {
                listener.onTextInputResult(inputText, source);
            } else {
                // Default handling: post annotation event
                handleTextInputResult(inputText, source);
            }
        });
        
        cancelButton.setOnClickListener(v -> {
            LOG.debug("Overlay text input cancelled from source: {}", source);
            hideTextInputOverlay();
            
            if (listener != null) {
                listener.onTextInputCancelled(source);
            }
        });

        // 注意：键盘显示逻辑已移至addOverlayToWindow方法中，确保悬浮窗完全显示后再弹出
    }
    
    /**
     * Add overlay view to window manager
     */
    private void addOverlayToWindow() {
        WindowManager.LayoutParams params = new WindowManager.LayoutParams(
            WindowManager.LayoutParams.MATCH_PARENT,
            WindowManager.LayoutParams.WRAP_CONTENT,
            OverlayPermissionManager.getOverlayWindowType(),
            WindowManager.LayoutParams.FLAG_NOT_TOUCH_MODAL |
            WindowManager.LayoutParams.FLAG_WATCH_OUTSIDE_TOUCH |
            WindowManager.LayoutParams.FLAG_SHOW_WHEN_LOCKED |
            WindowManager.LayoutParams.FLAG_TURN_SCREEN_ON,
            // 移除FLAG_ALT_FOCUSABLE_IM，这个标志会阻止输入法弹出
            PixelFormat.TRANSLUCENT
        );

        params.gravity = Gravity.CENTER;
        params.x = 0;
        params.y = 0;
        params.softInputMode = WindowManager.LayoutParams.SOFT_INPUT_STATE_VISIBLE |
                              WindowManager.LayoutParams.SOFT_INPUT_ADJUST_RESIZE;

        windowManager.addView(overlayView, params);

        // 延迟显示键盘，确保悬浮窗完全显示后再弹出输入法
        overlayView.post(() -> {
            EditText inputEditText = overlayView.findViewById(R.id.overlay_input);
            if (inputEditText != null) {
                inputEditText.requestFocus();
                // 多重尝试显示键盘，提高成功率
                showKeyboardWithRetry(inputEditText, 0);
            }
        });
    }
    
    /**
     * Show soft keyboard for input field
     */
    private void showKeyboard(EditText editText) {
        try {
            InputMethodManager imm = (InputMethodManager) context.getSystemService(Context.INPUT_METHOD_SERVICE);
            if (imm != null) {
                imm.showSoftInput(editText, InputMethodManager.SHOW_IMPLICIT);
                LOG.debug("Soft keyboard shown for overlay text input");
            }
        } catch (Exception e) {
            LOG.warn("Failed to show soft keyboard: {}", e.getMessage());
        }
    }

    /**
     * Show soft keyboard with retry mechanism for better success rate
     */
    private void showKeyboardWithRetry(EditText editText, int retryCount) {
        if (retryCount >= 5) {
            LOG.warn("Failed to show keyboard after 5 retries");
            return;
        }

        try {
            InputMethodManager imm = (InputMethodManager) context.getSystemService(Context.INPUT_METHOD_SERVICE);
            if (imm != null) {
                // 确保EditText可以获得焦点
                editText.setFocusable(true);
                editText.setFocusableInTouchMode(true);
                editText.requestFocus();
                editText.requestFocusFromTouch();

                // 尝试多种方式显示键盘
                boolean success = false;

                if (retryCount == 0) {
                    // 第一次尝试：使用SHOW_IMPLICIT
                    success = imm.showSoftInput(editText, InputMethodManager.SHOW_IMPLICIT);
                } else if (retryCount == 1) {
                    // 第二次尝试：使用SHOW_FORCED
                    success = imm.showSoftInput(editText, InputMethodManager.SHOW_FORCED);
                } else if (retryCount == 2) {
                    // 第三次尝试：使用toggleSoftInput
                    imm.toggleSoftInput(InputMethodManager.SHOW_FORCED, InputMethodManager.HIDE_IMPLICIT_ONLY);
                    success = true; // toggleSoftInput没有返回值，假设成功
                } else {
                    // 后续尝试：组合方式
                    imm.restartInput(editText);
                    success = imm.showSoftInput(editText, InputMethodManager.SHOW_FORCED);
                }

                LOG.debug("Soft keyboard show attempt {} for overlay text input, method: {}, success: {}",
                         retryCount + 1, getMethodName(retryCount), success);

                // 如果失败，延迟重试
                if (!success && retryCount < 4) {
                    editText.postDelayed(() -> showKeyboardWithRetry(editText, retryCount + 1), 300);
                }
            }
        } catch (Exception e) {
            LOG.warn("Failed to show soft keyboard on attempt {}: {}", retryCount + 1, e.getMessage());
            // 异常时也尝试重试
            if (retryCount < 4) {
                editText.postDelayed(() -> showKeyboardWithRetry(editText, retryCount + 1), 300);
            }
        }
    }

    private String getMethodName(int retryCount) {
        switch (retryCount) {
            case 0: return "SHOW_IMPLICIT";
            case 1: return "SHOW_FORCED";
            case 2: return "toggleSoftInput";
            default: return "restartInput+SHOW_FORCED";
        }
    }
    
    /**
     * Hide soft keyboard
     */
    private void hideKeyboard() {
        if (overlayView == null) return;
        
        try {
            EditText inputEditText = overlayView.findViewById(R.id.overlay_input);
            if (inputEditText != null) {
                InputMethodManager imm = (InputMethodManager) context.getSystemService(Context.INPUT_METHOD_SERVICE);
                if (imm != null) {
                    imm.hideSoftInputFromWindow(inputEditText.getWindowToken(), 0);
                    LOG.debug("Soft keyboard hidden for overlay text input");
                }
            }
        } catch (Exception e) {
            LOG.warn("Failed to hide soft keyboard: {}", e.getMessage());
        }
    }
    
    /**
     * Default handling for text input result
     * Posts annotation event with the input text
     */
    private void handleTextInputResult(String inputText, String source) {
        if (inputText == null || inputText.trim().isEmpty()) {
            LOG.warn("Text input result is empty");
            return;
        }
        
        try {
            // Store input text for template variable and clear voice text for mutual exclusion
            BasicVariableProvider.setInputText(context, inputText);

            // Clear voice text to ensure mutual exclusion between input_text and voice_text variables
            Session.getInstance().clearTemplateVoiceText();

            // Set template context for text input
            String buttonName = "后台文本输入";
            int buttonIndex = 0;
            String groupName = source;

            // Use a handler to post the event after SharedPreferences write completes
            new android.os.Handler(android.os.Looper.getMainLooper()).post(() -> {
                // Post annotation event with input text
                EventBus.getDefault().post(new CommandEvents.Annotate(inputText, null,
                                                                      buttonName, buttonIndex, groupName));
                LOG.info("Posted CommandEvents.Annotate for overlay text input: '{}', source: '{}'",
                        inputText, source);

                // Show success feedback
                Toast.makeText(context, "文本注释已添加: " + inputText, Toast.LENGTH_SHORT).show();
            });
            
        } catch (Exception e) {
            LOG.error("Error processing overlay text input result", e);
            Toast.makeText(context, "处理文本输入结果时出错", Toast.LENGTH_SHORT).show();
        }
    }
}
