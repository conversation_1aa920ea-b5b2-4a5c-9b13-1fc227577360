/*
 * Copyright (C) 2024 mendhak
 *
 * This file is part of GPSLogger for Android.
 *
 * GPSLogger is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 2 of the License, or
 * (at your option) any later version.
 *
 * GPSLogger is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with GPSLogger.  If not, see <http://www.gnu.org/licenses/>.
 */

package com.mendhak.gpslogger.ui.utils;

import android.content.Context;
import android.content.Intent;
import android.location.Location;
import android.os.Handler;
import android.os.Looper;

import com.mendhak.gpslogger.common.PreferenceHelper;
import com.mendhak.gpslogger.common.slf4j.Logs;
import com.mendhak.gpslogger.ui.components.template.AnnotationTemplateEngine;

import org.slf4j.Logger;

/**
 * 单点推送Intent发送工具类
 */
public class SinglePointPushHelper {
    
    private static final Logger LOG = Logs.of(SinglePointPushHelper.class);
    
    /**
     * 发送单点推送Intent
     * @param context 上下文
     * @param location 位置信息
     */
    public static void sendSinglePointPushIntent(Context context, Location location) {
        try {
            PreferenceHelper preferenceHelper = PreferenceHelper.getInstance();
            
            // Check if single point push is enabled
            if (!preferenceHelper.isSinglePointPushEnabled()) {
                LOG.debug("Single point push is disabled, skipping");
                return;
            }

            LOG.info("=== SENDING SINGLE POINT PUSH INTENT ===");

            // Get configuration values
            String targetType = preferenceHelper.getSinglePointPushTargetType();
            String action = preferenceHelper.getSinglePointPushAction();
            String className = preferenceHelper.getSinglePointPushClass();
            String packageName = preferenceHelper.getSinglePointPushPackage();
            String data = preferenceHelper.getSinglePointPushData();
            String mimeType = preferenceHelper.getSinglePointPushMimeType();
            String flagsStr = preferenceHelper.getSinglePointPushFlags();

            // Process template variables in data field
            AnnotationTemplateEngine templateEngine = AnnotationTemplateEngine.getInstance();
            if (!data.trim().isEmpty()) {
                data = templateEngine.processTemplateString(data, context, location, null, null, 0, null);
                LOG.debug("Processed data field: {}", data);
            }

            // Create intent
            Intent pushIntent = new Intent(action);

            if (!packageName.trim().isEmpty()) {
                pushIntent.setPackage(packageName);
            }

            if (!className.trim().isEmpty()) {
                pushIntent.setClassName(packageName, className);
            }

            if (!data.trim().isEmpty()) {
                pushIntent.setData(android.net.Uri.parse(data));
            }

            if (!mimeType.trim().isEmpty()) {
                pushIntent.setType(mimeType);
            }

            // Parse and set flags
            if (!flagsStr.trim().isEmpty()) {
                try {
                    int flags = Integer.parseInt(flagsStr);
                    pushIntent.setFlags(flags);
                    LOG.debug("Set intent flags: {}", flags);
                } catch (NumberFormatException e) {
                    LOG.warn("Invalid flags format: {}", flagsStr);
                }
            }

            // Add location data as extras
            pushIntent.putExtra("latitude", location.getLatitude());
            pushIntent.putExtra("longitude", location.getLongitude());
            pushIntent.putExtra("altitude", location.getAltitude());
            pushIntent.putExtra("accuracy", location.getAccuracy());
            pushIntent.putExtra("bearing", location.getBearing());
            pushIntent.putExtra("speed", location.getSpeed());
            pushIntent.putExtra("time", location.getTime());

            if (location.getProvider() != null) {
                pushIntent.putExtra("provider", location.getProvider());
            }

            // Add extra data fields with template processing
            for (int i = 1; i <= 6; i++) {
                String extraKey = preferenceHelper.getSinglePointPushExtraKey(i);
                String extraValue = preferenceHelper.getSinglePointPushExtraValue(i);

                if (!extraKey.trim().isEmpty() && !extraValue.trim().isEmpty()) {
                    // Process template variables in extra value
                    extraValue = templateEngine.processTemplateString(extraValue, context, location, null, null, 0, null);
                    pushIntent.putExtra(extraKey, extraValue);
                    LOG.debug("Added extra data: {} = {}", extraKey, extraValue);
                }
            }

            // Send intent based on target type with delay to avoid immediate app switching
            final String finalTargetType = targetType;
            final Intent finalPushIntent = pushIntent;

            // 延迟500ms发送Intent，避免首次推送时立即切换应用
            new Handler(Looper.getMainLooper()).postDelayed(new Runnable() {
                @Override
                public void run() {
                    try {
                        switch (finalTargetType) {
                            case "activity":
                                // 使用更温和的flags，避免强制切换应用
                                finalPushIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TOP);
                                context.startActivity(finalPushIntent);
                                LOG.info("Single point push intent sent as activity (delayed)");
                                break;
                            case "service":
                                context.startService(finalPushIntent);
                                LOG.info("Single point push intent sent as service (delayed)");
                                break;
                            case "broadcast":
                            default:
                                context.sendBroadcast(finalPushIntent);
                                LOG.info("Single point push intent sent as broadcast (delayed)");
                                break;
                        }
                    } catch (Exception e) {
                        LOG.error("Error sending delayed intent", e);
                    }
                }
            }, 500); // 延迟500毫秒

            LOG.info("Single point push intent sent successfully: type={}, action={}, package={}, class={}",
                targetType, action, packageName, className);

        } catch (Exception e) {
            LOG.error("Error sending single point push intent", e);
            throw e; // Re-throw to allow caller to handle
        }
    }
    
    /**
     * 创建模拟位置对象
     * @param latitude 纬度
     * @param longitude 经度
     * @return Location对象
     */
    public static Location createLocationFromCoordinates(double latitude, double longitude) {
        Location location = new Location("SinglePointPushView");
        location.setLatitude(latitude);
        location.setLongitude(longitude);
        location.setTime(System.currentTimeMillis());
        location.setAccuracy(1.0f); // 设置一个默认精度
        return location;
    }
}
