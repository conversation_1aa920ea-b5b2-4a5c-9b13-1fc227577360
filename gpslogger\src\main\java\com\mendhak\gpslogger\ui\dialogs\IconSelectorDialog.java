/*
 * Copyright (C) 2024 mendhak
 *
 * This file is part of GPSLogger for Android.
 *
 * GPSLogger is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 2 of the License, or
 * (at your option) any later version.
 *
 * GPSLogger is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with GPSLogger.  If not, see <http://www.gnu.org/licenses/>.
 */

package com.mendhak.gpslogger.ui.dialogs;

import android.app.Dialog;
import android.content.Context;
import android.os.Bundle;
import android.widget.Button;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.mendhak.gpslogger.R;
import com.mendhak.gpslogger.ui.adapters.IconSelectorAdapter;
import com.mendhak.gpslogger.ui.managers.GroupIconManager;
import com.mendhak.gpslogger.ui.models.GroupIcon;

import java.util.List;

/**
 * 图标选择器对话框
 */
public class IconSelectorDialog extends Dialog {
    
    public interface OnIconSelectedListener {
        void onIconSelected(GroupIcon selectedIcon);
    }
    
    private RecyclerView iconsRecycler;
    private Button cancelButton;
    private Button confirmButton;
    
    private IconSelectorAdapter adapter;
    private OnIconSelectedListener listener;
    private GroupIcon currentSelectedIcon;
    
    public IconSelectorDialog(@NonNull Context context) {
        super(context);
    }
    
    public IconSelectorDialog(@NonNull Context context, int themeResId) {
        super(context, themeResId);
    }
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.dialog_icon_selector);
        
        initViews();
        setupRecyclerView();
        setupButtons();
    }
    
    private void initViews() {
        iconsRecycler = findViewById(R.id.icons_recycler);
        cancelButton = findViewById(R.id.cancel_button);
        confirmButton = findViewById(R.id.confirm_button);
    }
    
    private void setupRecyclerView() {
        // 设置网格布局，每行5个图标
        GridLayoutManager layoutManager = new GridLayoutManager(getContext(), 5);
        iconsRecycler.setLayoutManager(layoutManager);
        
        // 获取可用图标
        List<GroupIcon> icons = GroupIconManager.getAvailableIcons();
        
        // 创建适配器
        adapter = new IconSelectorAdapter(icons);
        adapter.setOnIconSelectedListener((icon, position) -> {
            currentSelectedIcon = icon;
            // 可以在这里添加选中反馈
        });
        
        iconsRecycler.setAdapter(adapter);
        
        // 设置当前选中的图标
        if (currentSelectedIcon != null) {
            adapter.setSelectedIcon(currentSelectedIcon);
        }
    }
    
    private void setupButtons() {
        cancelButton.setOnClickListener(v -> dismiss());
        
        confirmButton.setOnClickListener(v -> {
            GroupIcon selectedIcon = adapter.getSelectedIcon();
            if (selectedIcon != null && listener != null) {
                listener.onIconSelected(selectedIcon);
            }
            dismiss();
        });
    }
    
    /**
     * 设置图标选择监听器
     */
    public void setOnIconSelectedListener(OnIconSelectedListener listener) {
        this.listener = listener;
    }
    
    /**
     * 设置当前选中的图标
     */
    public void setSelectedIcon(GroupIcon icon) {
        this.currentSelectedIcon = icon;
        if (adapter != null) {
            adapter.setSelectedIcon(icon);
        }
    }
    
    /**
     * 设置当前选中的图标（通过ID）
     */
    public void setSelectedIconById(String iconId) {
        GroupIcon icon = GroupIconManager.getIconById(iconId);
        setSelectedIcon(icon);
    }
}
