/*
 * Copyright (C) 2024 mendhak
 *
 * This file is part of GPSLogger for Android.
 *
 * GPSLogger is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 2 of the License, or
 * (at your option) any later version.
 *
 * GPSLogger is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with GPSLogger.  If not, see <http://www.gnu.org/licenses/>.
 */

package com.mendhak.gpslogger.ui.adapters;

import android.graphics.Color;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.RecyclerView;

import com.mendhak.gpslogger.R;
import com.mendhak.gpslogger.ui.models.LocationPoint;
import com.mendhak.gpslogger.ui.models.ButtonConfig;
import com.mendhak.gpslogger.ui.managers.ButtonConfigManager;
import com.mendhak.gpslogger.ui.factories.DynamicButtonFactory;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * 位置点列表适配器
 */
public class LocationPointAdapter extends RecyclerView.Adapter<LocationPointAdapter.LocationPointViewHolder> {

    private List<LocationPoint> locationPoints;
    private OnLocationPointClickListener listener;
    private Set<Integer> pushedPointIndices = new HashSet<>(); // 已推送的位置点索引集合
    private Set<Integer> passedPointIndices = new HashSet<>(); // 已通过的位置点索引集合
    private Set<Integer> failedPointIndices = new HashSet<>(); // 不通过的位置点索引集合
    private int startIndex = 0; // 顺序推送起点索引
    private ButtonConfigManager buttonConfigManager; // 按钮配置管理器
    
    public interface OnLocationPointClickListener {
        void onPassButtonClick(LocationPoint point);
        void onFailButtonClick(LocationPoint point);
        void onPushButtonClick(LocationPoint point);
        void onSequenceNumberLongClick(LocationPoint point); // 长按序号取消推送状态
        void onDescriptionLongClick(LocationPoint point); // 长按描述取消通过/不通过状态
        void onPassButtonLongClick(LocationPoint point); // 长按通过按钮
        void onFailButtonLongClick(LocationPoint point); // 长按不通过按钮
        void onSetStartIndexClick(LocationPoint point, int pointIndex); // 设置起点索引
    }
    
    public LocationPointAdapter(List<LocationPoint> locationPoints) {
        this.locationPoints = locationPoints;
    }
    
    public void setOnLocationPointClickListener(OnLocationPointClickListener listener) {
        this.listener = listener;
    }

    /**
     * 设置按钮配置管理器
     */
    public void setButtonConfigManager(ButtonConfigManager buttonConfigManager) {
        this.buttonConfigManager = buttonConfigManager;
        notifyDataSetChanged(); // 刷新UI以应用新的按钮配置
    }
    
    @NonNull
    @Override
    public LocationPointViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext())
                .inflate(R.layout.item_location_point, parent, false);
        return new LocationPointViewHolder(view);
    }
    
    @Override
    public void onBindViewHolder(@NonNull LocationPointViewHolder holder, int position) {
        LocationPoint point = locationPoints.get(position);
        holder.bind(point);
    }
    
    @Override
    public int getItemCount() {
        return locationPoints != null ? locationPoints.size() : 0;
    }
    
    public void updateLocationPoints(List<LocationPoint> newLocationPoints) {
        this.locationPoints = newLocationPoints;
        this.pushedPointIndices.clear(); // 重置推送状态
        this.passedPointIndices.clear(); // 重置通过状态
        this.failedPointIndices.clear(); // 重置不通过状态
        notifyDataSetChanged();
    }

    /**
     * 设置顺序推送起点索引
     */
    public void setStartIndex(int startIndex) {
        this.startIndex = Math.max(0, startIndex);
        notifyDataSetChanged(); // 刷新UI以显示起点标识
    }

    /**
     * 获取当前起点索引
     */
    public int getStartIndex() {
        return startIndex;
    }

    /**
     * 标记指定位置点为已推送
     */
    public void markPointAsPushed(int pointIndex) {
        pushedPointIndices.add(pointIndex);
        notifyDataSetChanged();
    }

    /**
     * 检查指定位置点是否已推送
     */
    public boolean isPointPushed(int pointIndex) {
        return pushedPointIndices.contains(pointIndex);
    }

    /**
     * 设置已推送的位置点索引集合（用于组级顺序推送）
     */
    public void setPushedPointIndices(Set<Integer> pushedIndices) {
        this.pushedPointIndices.clear();
        this.pushedPointIndices.addAll(pushedIndices);
        notifyDataSetChanged();
    }

    /**
     * 获取已推送的位置点数量
     */
    public int getPushedCount() {
        return pushedPointIndices.size();
    }

    /**
     * 标记指定位置点为通过状态
     */
    public void markPointAsPassed(int pointIndex) {
        passedPointIndices.add(pointIndex);
        failedPointIndices.remove(pointIndex); // 移除不通过状态
        notifyItemChanged(pointIndex);
    }

    /**
     * 标记指定位置点为不通过状态
     */
    public void markPointAsFailed(int pointIndex) {
        failedPointIndices.add(pointIndex);
        passedPointIndices.remove(pointIndex); // 移除通过状态
        notifyItemChanged(pointIndex);
    }

    /**
     * 清除指定位置点的推送状态
     */
    public void clearPushStatus(int pointIndex) {
        pushedPointIndices.remove(pointIndex);
        notifyItemChanged(pointIndex);
    }

    /**
     * 清除指定位置点的通过/不通过状态
     */
    public void clearPassFailStatus(int pointIndex) {
        passedPointIndices.remove(pointIndex);
        failedPointIndices.remove(pointIndex);
        notifyItemChanged(pointIndex);
    }

    /**
     * 检查指定位置点是否为通过状态
     */
    public boolean isPointPassed(int pointIndex) {
        return passedPointIndices.contains(pointIndex);
    }

    /**
     * 检查指定位置点是否为不通过状态
     */
    public boolean isPointFailed(int pointIndex) {
        return failedPointIndices.contains(pointIndex);
    }

    /**
     * 设置已通过的位置点索引集合
     */
    public void setPassedPointIndices(Set<Integer> passedIndices) {
        this.passedPointIndices.clear();
        this.passedPointIndices.addAll(passedIndices);
        notifyDataSetChanged();
    }

    /**
     * 设置不通过的位置点索引集合
     */
    public void setFailedPointIndices(Set<Integer> failedIndices) {
        this.failedPointIndices.clear();
        this.failedPointIndices.addAll(failedIndices);
        notifyDataSetChanged();
    }
    
    class LocationPointViewHolder extends RecyclerView.ViewHolder {
        private TextView sequenceNumberText;
        private TextView coordinatesText;
        private TextView descriptionText;
        private View passButton;
        private View failButton;
        private Button pushButton;
        private LinearLayout buttonContainer;
        
        public LocationPointViewHolder(@NonNull View itemView) {
            super(itemView);

            sequenceNumberText = itemView.findViewById(R.id.sequence_number_text);
            coordinatesText = itemView.findViewById(R.id.coordinates_text);
            descriptionText = itemView.findViewById(R.id.description_text);
            pushButton = itemView.findViewById(R.id.push_button);
            buttonContainer = itemView.findViewById(R.id.button_container);

            // 初始化动态按钮
            initializeDynamicButtons();
            
            // 设置推送按钮点击监听器
            pushButton.setOnClickListener(v -> {
                if (listener != null) {
                    int position = getAdapterPosition();
                    if (position != RecyclerView.NO_POSITION) {
                        listener.onPushButtonClick(locationPoints.get(position));
                    }
                }
            });

            // 设置长按监听器
            sequenceNumberText.setOnLongClickListener(v -> {
                if (listener != null) {
                    int position = getAdapterPosition();
                    if (position != RecyclerView.NO_POSITION) {
                        listener.onSequenceNumberLongClick(locationPoints.get(position));
                        return true;
                    }
                }
                return false;
            });

            // 设置双击监听器来设置起点
            sequenceNumberText.setOnClickListener(new View.OnClickListener() {
                private long lastClickTime = 0;
                private static final long DOUBLE_CLICK_TIME_DELTA = 300; // 双击间隔时间

                @Override
                public void onClick(View v) {
                    long clickTime = System.currentTimeMillis();
                    if (clickTime - lastClickTime < DOUBLE_CLICK_TIME_DELTA) {
                        // 双击事件 - 设置起点
                        if (listener != null) {
                            int position = getAdapterPosition();
                            if (position != RecyclerView.NO_POSITION) {
                                listener.onSetStartIndexClick(locationPoints.get(position), position);
                            }
                        }
                    }
                    lastClickTime = clickTime;
                }
            });

            descriptionText.setOnLongClickListener(v -> {
                if (listener != null) {
                    int position = getAdapterPosition();
                    if (position != RecyclerView.NO_POSITION) {
                        listener.onDescriptionLongClick(locationPoints.get(position));
                        return true;
                    }
                }
                return false;
            });
        }

        /**
         * 初始化动态按钮
         */
        private void initializeDynamicButtons() {
            if (buttonContainer == null || buttonConfigManager == null) {
                // 如果没有按钮容器或配置管理器，使用默认按钮
                createDefaultButtons();
                return;
            }

            // 清除现有按钮
            buttonContainer.removeAllViews();

            // 获取按钮配置
            ButtonConfig passConfig = buttonConfigManager.getPassButtonConfig();
            ButtonConfig failConfig = buttonConfigManager.getFailButtonConfig();

            // 创建动态按钮
            DynamicButtonFactory.OnButtonClickListener buttonListener = new DynamicButtonFactory.OnButtonClickListener() {
                @Override
                public void onPassClick() {
                    if (listener != null) {
                        int position = getAdapterPosition();
                        if (position != RecyclerView.NO_POSITION) {
                            listener.onPassButtonClick(locationPoints.get(position));
                        }
                    }
                }

                @Override
                public void onFailClick() {
                    if (listener != null) {
                        int position = getAdapterPosition();
                        if (position != RecyclerView.NO_POSITION) {
                            listener.onFailButtonClick(locationPoints.get(position));
                        }
                    }
                }

                @Override
                public void onCounterClick(int count) {
                    // 计数器模式的处理逻辑
                    if (listener != null) {
                        int position = getAdapterPosition();
                        if (position != RecyclerView.NO_POSITION) {
                            if (count > 0) {
                                listener.onPassButtonClick(locationPoints.get(position));
                            } else {
                                listener.onFailButtonClick(locationPoints.get(position));
                            }
                        }
                    }
                }
            };

            // 创建通过按钮
            passButton = DynamicButtonFactory.createPassButton(itemView.getContext(), passConfig, buttonListener);

            // 创建不通过按钮
            failButton = DynamicButtonFactory.createFailButton(itemView.getContext(), failConfig, buttonListener);

            // 添加到容器 - 使用固定大小而不是权重，确保与推送按钮大小一致
            buttonContainer.addView(passButton);
            buttonContainer.addView(failButton);

            // 设置长按监听器
            setupButtonLongClickListeners();
        }

        /**
         * 创建默认按钮（当没有配置管理器时）
         */
        private void createDefaultButtons() {
            // 使用原有的静态按钮作为默认
            passButton = itemView.findViewById(R.id.pass_button);
            failButton = itemView.findViewById(R.id.fail_button);

            if (passButton != null && failButton != null) {
                setupButtonClickListeners();
                setupButtonLongClickListeners();
            }
        }

        /**
         * 设置按钮点击监听器
         */
        private void setupButtonClickListeners() {
            if (passButton != null) {
                passButton.setOnClickListener(v -> {
                    if (listener != null) {
                        int position = getAdapterPosition();
                        if (position != RecyclerView.NO_POSITION) {
                            listener.onPassButtonClick(locationPoints.get(position));
                        }
                    }
                });
            }

            if (failButton != null) {
                failButton.setOnClickListener(v -> {
                    if (listener != null) {
                        int position = getAdapterPosition();
                        if (position != RecyclerView.NO_POSITION) {
                            listener.onFailButtonClick(locationPoints.get(position));
                        }
                    }
                });
            }
        }

        /**
         * 设置按钮长按监听器
         */
        private void setupButtonLongClickListeners() {
            if (passButton != null) {
                passButton.setOnLongClickListener(v -> {
                    if (listener != null) {
                        int position = getAdapterPosition();
                        if (position != RecyclerView.NO_POSITION) {
                            listener.onPassButtonLongClick(locationPoints.get(position));
                            return true;
                        }
                    }
                    return false;
                });
            }

            if (failButton != null) {
                failButton.setOnLongClickListener(v -> {
                    if (listener != null) {
                        int position = getAdapterPosition();
                        if (position != RecyclerView.NO_POSITION) {
                            listener.onFailButtonLongClick(locationPoints.get(position));
                            return true;
                        }
                    }
                    return false;
                });
            }
        }
        
        public void bind(LocationPoint point) {
            // 重新初始化动态按钮（确保配置更新后能正确显示）
            initializeDynamicButtons();

            sequenceNumberText.setText(String.valueOf(point.getSequenceNumber()));

            // 根据推送状态和起点状态设置序号颜色
            int position = getAdapterPosition();
            if (position != RecyclerView.NO_POSITION) {
                if (position == startIndex) {
                    // 起点：橙色背景，白色文字
                    sequenceNumberText.setTextColor(Color.parseColor("#FFFFFF"));
                    sequenceNumberText.setBackgroundColor(Color.parseColor("#FF9800"));
                } else if (pushedPointIndices.contains(position)) {
                    // 已推送：蓝色文字，透明背景
                    sequenceNumberText.setTextColor(Color.parseColor("#2196F3"));
                    sequenceNumberText.setBackgroundColor(Color.TRANSPARENT);
                } else {
                    // 未推送：默认黑色文字，透明背景
                    sequenceNumberText.setTextColor(Color.parseColor("#000000"));
                    sequenceNumberText.setBackgroundColor(Color.TRANSPARENT);
                }
            }

            // 格式化坐标显示，在逗号后换行
            String coordinates = point.getCoordinatesString();
            if (coordinates != null && coordinates.contains(",")) {
                coordinates = coordinates.replace(",", ",\n");
            }
            coordinatesText.setText(coordinates);

            // 确保描述文本正确显示
            String description = point.getDescription();
            if (description == null || description.trim().isEmpty()) {
                description = "无描述";
            }
            descriptionText.setText(description);

            // 根据通过/不通过状态设置描述文本颜色
            if (position != RecyclerView.NO_POSITION) {
                if (passedPointIndices.contains(position)) {
                    // 通过状态：绿色（与通过按钮颜色一致）
                    descriptionText.setTextColor(Color.parseColor("#4CAF50"));
                } else if (failedPointIndices.contains(position)) {
                    // 不通过状态：红色（与不通过按钮颜色一致）
                    descriptionText.setTextColor(Color.parseColor("#F44336"));
                } else {
                    // 默认状态：黑色
                    descriptionText.setTextColor(Color.parseColor("#000000"));
                }
            }

            // 调试日志
            android.util.Log.d("LocationPointAdapter", "Binding point: " + point.getSequenceNumber() +
                    ", coords: " + coordinates +
                    ", description: '" + description + "'" +
                    ", pushed: " + pushedPointIndices.contains(position));
        }
    }
}
