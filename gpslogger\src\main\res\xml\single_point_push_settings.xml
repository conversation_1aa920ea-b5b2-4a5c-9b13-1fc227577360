<?xml version="1.0" encoding="utf-8"?>
<PreferenceScreen xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:title="@string/single_point_push_settings_title">

    <!-- Main single point push switch -->
    <SwitchPreferenceCompat
        android:key="single_point_push_enabled"
        android:title="@string/single_point_push_enabled_title"
        android:summary="@string/single_point_push_enabled_summary"
        android:defaultValue="false"
        app:iconSpaceReserved="false" />

    <!-- Intent configuration category -->
    <PreferenceCategory
        android:title="@string/single_point_push_intent_title"
        android:key="single_point_push_intent_category">

        <!-- Target type selection -->
        <ListPreference
            android:key="single_point_push_target_type"
            android:title="@string/single_point_push_target_title"
            android:summary="@string/single_point_push_target_summary"
            android:defaultValue="activity"
            android:dependency="single_point_push_enabled"
            android:entries="@array/target_type_entries"
            android:entryValues="@array/target_type_values"
            app:iconSpaceReserved="false" />

        <!-- Action -->
        <EditTextPreference
            android:key="single_point_push_action"
            android:title="@string/single_point_push_action_title"
            android:summary="@string/single_point_push_action_summary"
            android:defaultValue="android.intent.action.VIEW"
            android:dependency="single_point_push_enabled"
            app:iconSpaceReserved="false" />

        <!-- Class -->
        <EditTextPreference
            android:key="single_point_push_class"
            android:title="@string/single_point_push_class_title"
            android:summary="@string/single_point_push_class_summary"
            android:defaultValue="com.autonaiv.minimap.map.activity.SplashActivity"
            android:dependency="single_point_push_enabled"
            app:iconSpaceReserved="false" />

        <!-- Package -->
        <EditTextPreference
            android:key="single_point_push_package"
            android:title="@string/single_point_push_package_title"
            android:summary="@string/single_point_push_package_summary"
            android:defaultValue=""
            android:dependency="single_point_push_enabled"
            app:iconSpaceReserved="false" />

        <!-- Data -->
        <EditTextPreference
            android:key="single_point_push_data"
            android:title="@string/single_point_push_data_title"
            android:summary="@string/single_point_push_data_summary"
            android:defaultValue=""
            android:dependency="single_point_push_enabled"
            app:iconSpaceReserved="false" />

        <!-- MIME Type -->
        <EditTextPreference
            android:key="single_point_push_mime_type"
            android:title="@string/single_point_push_mime_type_title"
            android:summary="@string/single_point_push_mime_type_summary"
            android:defaultValue=""
            android:dependency="single_point_push_enabled"
            app:iconSpaceReserved="false" />

        <!-- Flags -->
        <EditTextPreference
            android:key="single_point_push_flags"
            android:title="@string/single_point_push_flags_title"
            android:summary="@string/single_point_push_flags_summary"
            android:defaultValue="0"
            android:dependency="single_point_push_enabled"
            app:iconSpaceReserved="false" />

        <!-- Extra data 1 -->
        <EditTextPreference
            android:key="single_point_push_extra_key_1"
            android:title="附加数据1"
            android:summary="输入参数名"
            android:defaultValue=""
            android:dependency="single_point_push_enabled"
            app:iconSpaceReserved="false" />

        <EditTextPreference
            android:key="single_point_push_extra_value_1"
            android:title="输入数值"
            android:summary="输入参数值"
            android:defaultValue=""
            android:dependency="single_point_push_enabled"
            app:iconSpaceReserved="false" />

        <ListPreference
            android:key="single_point_push_auto_detect_type_1"
            android:title="@string/single_point_push_auto_detect_title"
            android:summary="@string/single_point_push_auto_detect_summary"
            android:defaultValue="string"
            android:dependency="single_point_push_enabled"
            android:entries="@array/auto_detect_type_entries"
            android:entryValues="@array/auto_detect_type_values"
            app:iconSpaceReserved="false" />

        <!-- Extra data 2 -->
        <EditTextPreference
            android:key="single_point_push_extra_key_2"
            android:title="附加数据2"
            android:summary="输入参数名"
            android:defaultValue=""
            android:dependency="single_point_push_enabled"
            app:iconSpaceReserved="false" />

        <EditTextPreference
            android:key="single_point_push_extra_value_2"
            android:title="输入数值"
            android:summary="输入参数值"
            android:defaultValue=""
            android:dependency="single_point_push_enabled"
            app:iconSpaceReserved="false" />

        <ListPreference
            android:key="single_point_push_auto_detect_type_2"
            android:title="@string/single_point_push_auto_detect_title"
            android:summary="@string/single_point_push_auto_detect_summary"
            android:defaultValue="string"
            android:dependency="single_point_push_enabled"
            android:entries="@array/auto_detect_type_entries"
            android:entryValues="@array/auto_detect_type_values"
            app:iconSpaceReserved="false" />

        <!-- Extra data 3 -->
        <EditTextPreference
            android:key="single_point_push_extra_key_3"
            android:title="附加数据3"
            android:summary="输入参数名"
            android:defaultValue=""
            android:dependency="single_point_push_enabled"
            app:iconSpaceReserved="false" />

        <EditTextPreference
            android:key="single_point_push_extra_value_3"
            android:title="输入数值"
            android:summary="输入参数值"
            android:defaultValue=""
            android:dependency="single_point_push_enabled"
            app:iconSpaceReserved="false" />

        <ListPreference
            android:key="single_point_push_auto_detect_type_3"
            android:title="@string/single_point_push_auto_detect_title"
            android:summary="@string/single_point_push_auto_detect_summary"
            android:defaultValue="string"
            android:dependency="single_point_push_enabled"
            android:entries="@array/auto_detect_type_entries"
            android:entryValues="@array/auto_detect_type_values"
            app:iconSpaceReserved="false" />

        <!-- Extra data 4 -->
        <EditTextPreference
            android:key="single_point_push_extra_key_4"
            android:title="附加数据4"
            android:summary="输入参数名"
            android:defaultValue=""
            android:dependency="single_point_push_enabled"
            app:iconSpaceReserved="false" />

        <EditTextPreference
            android:key="single_point_push_extra_value_4"
            android:title="输入数值"
            android:summary="输入参数值"
            android:defaultValue=""
            android:dependency="single_point_push_enabled"
            app:iconSpaceReserved="false" />

        <ListPreference
            android:key="single_point_push_auto_detect_type_4"
            android:title="@string/single_point_push_auto_detect_title"
            android:summary="@string/single_point_push_auto_detect_summary"
            android:defaultValue="string"
            android:dependency="single_point_push_enabled"
            android:entries="@array/auto_detect_type_entries"
            android:entryValues="@array/auto_detect_type_values"
            app:iconSpaceReserved="false" />

        <!-- Extra data 5 -->
        <EditTextPreference
            android:key="single_point_push_extra_key_5"
            android:title="附加数据5"
            android:summary="输入参数名"
            android:defaultValue=""
            android:dependency="single_point_push_enabled"
            app:iconSpaceReserved="false" />

        <EditTextPreference
            android:key="single_point_push_extra_value_5"
            android:title="输入数值"
            android:summary="输入参数值"
            android:defaultValue=""
            android:dependency="single_point_push_enabled"
            app:iconSpaceReserved="false" />

        <ListPreference
            android:key="single_point_push_auto_detect_type_5"
            android:title="@string/single_point_push_auto_detect_title"
            android:summary="@string/single_point_push_auto_detect_summary"
            android:defaultValue="string"
            android:dependency="single_point_push_enabled"
            android:entries="@array/auto_detect_type_entries"
            android:entryValues="@array/auto_detect_type_values"
            app:iconSpaceReserved="false" />

        <!-- Extra data 6 -->
        <EditTextPreference
            android:key="single_point_push_extra_key_6"
            android:title="附加数据6"
            android:summary="输入参数名"
            android:defaultValue=""
            android:dependency="single_point_push_enabled"
            app:iconSpaceReserved="false" />

        <EditTextPreference
            android:key="single_point_push_extra_value_6"
            android:title="输入数值"
            android:summary="输入参数值"
            android:defaultValue=""
            android:dependency="single_point_push_enabled"
            app:iconSpaceReserved="false" />

        <ListPreference
            android:key="single_point_push_auto_detect_type_6"
            android:title="@string/single_point_push_auto_detect_title"
            android:summary="@string/single_point_push_auto_detect_summary"
            android:defaultValue="string"
            android:dependency="single_point_push_enabled"
            android:entries="@array/auto_detect_type_entries"
            android:entryValues="@array/auto_detect_type_values"
            app:iconSpaceReserved="false" />

    </PreferenceCategory>

    <!-- Global variables information -->
    <PreferenceCategory
        android:title="全局变量支持"
        android:key="single_point_push_variables_category">

        <Preference
            android:key="single_point_push_variables_info"
            android:title="可用的全局变量"
            android:summary="数据字段和附加数据值字段支持以下全局变量：\n{latitude} - 纬度\n{longitude} - 经度\n{altitude} - 海拔\n{accuracy} - 精度\n{speed} - 速度\n{bearing} - 方向\n{coordinates} - 坐标对\n{timestamp} - 时间戳\n\n用户自定义变量：\n{Push_location} - 可在用户变量设置中自定义"
            android:selectable="false"
            app:iconSpaceReserved="false" />

    </PreferenceCategory>

    <!-- Test and help section -->
    <PreferenceCategory
        android:title="测试和帮助"
        android:key="single_point_push_help_category">

        <Preference
            android:key="single_point_push_test"
            android:title="测试"
            android:summary="发送测试Intent验证配置"
            android:dependency="single_point_push_enabled"
            app:iconSpaceReserved="false" />

        <Preference
            android:key="single_point_push_help"
            android:title="帮助"
            android:summary="查看单点推送设置说明"
            app:iconSpaceReserved="false" />

    </PreferenceCategory>

</PreferenceScreen>
