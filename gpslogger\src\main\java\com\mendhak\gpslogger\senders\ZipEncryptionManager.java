/*
 * Copyright (C) 2016 mendhak
 *
 * This file is part of GPSLogger for Android.
 *
 * GPSLogger for Android is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 2 of the License, or
 * (at your option) any later version.
 *
 * GPSLogger for Android is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with GPSLogger for Android.  If not, see <http://www.gnu.org/licenses/>.
 */

package com.mendhak.gpslogger.senders;

import com.mendhak.gpslogger.common.slf4j.Logs;
import net.lingala.zip4j.ZipFile;
import net.lingala.zip4j.exception.ZipException;
import net.lingala.zip4j.model.ZipParameters;
import net.lingala.zip4j.model.enums.AesKeyStrength;
import net.lingala.zip4j.model.enums.EncryptionMethod;
import org.slf4j.Logger;

import java.io.BufferedInputStream;
import java.io.BufferedOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * Manager class for creating encrypted and unencrypted zip files
 */
public class ZipEncryptionManager {
    private static final Logger LOG = Logs.of(ZipEncryptionManager.class);
    private static final int BUFFER = 2048;
    
    /**
     * Create a zip file with optional encryption
     * 
     * @param files List of files to include in the zip
     * @param zipPath Path where the zip file should be created
     * @param config Encryption configuration
     * @return The created zip file
     * @throws Exception if zip creation fails
     */
    public static File createZipFile(List<File> files, String zipPath, ZipEncryptionConfig config) 
            throws Exception {
        
        if (files == null || files.isEmpty()) {
            throw new IllegalArgumentException("No files provided for zip creation");
        }
        
        // Validate configuration
        if (!config.isValid()) {
            throw new IllegalArgumentException("Invalid zip encryption configuration");
        }
        
        LOG.info("Creating zip file: {} (encrypted: {}, files: {})", 
                zipPath, config.isEncryptionEnabled(), files.size());
        
        try {
            if (config.isEncryptionEnabled()) {
                return createEncryptedZip(files, zipPath, config.getPassword());
            } else {
                return createStandardZip(files, zipPath);
            }
        } catch (Exception e) {
            LOG.error("Failed to create zip file: {}", zipPath, e);
            // Clean up partial file if it exists
            File zipFile = new File(zipPath);
            if (zipFile.exists()) {
                boolean deleted = zipFile.delete();
                LOG.debug("Cleaned up partial zip file: {}", deleted);
            }
            throw e;
        }
    }
    
    /**
     * Create an encrypted zip file using Zip4j
     */
    private static File createEncryptedZip(List<File> files, String zipPath, String password) 
            throws ZipException {
        
        LOG.debug("Creating encrypted zip with AES-256 encryption");
        
        ZipFile zipFile = new ZipFile(zipPath, password.toCharArray());
        
        ZipParameters params = new ZipParameters();
        params.setEncryptFiles(true);
        params.setEncryptionMethod(EncryptionMethod.AES);
        params.setAesKeyStrength(AesKeyStrength.KEY_STRENGTH_256);
        
        // Filter out non-existent files
        List<File> validFiles = new ArrayList<>();
        for (File file : files) {
            if (file.exists() && file.length() > 0) {
                validFiles.add(file);
                LOG.debug("Adding file to encrypted zip: {} ({}bytes)", 
                         file.getName(), file.length());
            } else {
                LOG.warn("Skipping non-existent or empty file: {}", file.getAbsolutePath());
            }
        }
        
        if (validFiles.isEmpty()) {
            throw new ZipException("No valid files found for zip creation");
        }
        
        // Add files to encrypted zip
        zipFile.addFiles(validFiles, params);
        
        File result = new File(zipPath);
        LOG.info("Encrypted zip created successfully: {} ({}bytes)", 
                result.getName(), result.length());
        
        return result;
    }
    
    /**
     * Create a standard unencrypted zip file (fallback to original logic)
     */
    private static File createStandardZip(List<File> files, String zipPath) throws IOException {
        
        LOG.debug("Creating standard unencrypted zip");
        
        try (FileOutputStream dest = new FileOutputStream(zipPath);
             ZipOutputStream out = new ZipOutputStream(new BufferedOutputStream(dest))) {
            
            byte[] data = new byte[BUFFER];
            
            for (File file : files) {
                if (!file.exists() || file.length() == 0) {
                    LOG.warn("Skipping non-existent or empty file: {}", file.getAbsolutePath());
                    continue;
                }
                
                LOG.debug("Adding file to standard zip: {} ({}bytes)", 
                         file.getName(), file.length());
                
                try (FileInputStream fi = new FileInputStream(file);
                     BufferedInputStream origin = new BufferedInputStream(fi, BUFFER)) {
                    
                    ZipEntry entry = new ZipEntry(file.getName());
                    out.putNextEntry(entry);
                    
                    int count;
                    while ((count = origin.read(data, 0, BUFFER)) != -1) {
                        out.write(data, 0, count);
                    }
                    
                    out.closeEntry();
                }
            }
        }
        
        File result = new File(zipPath);
        LOG.info("Standard zip created successfully: {} ({}bytes)", 
                result.getName(), result.length());
        
        return result;
    }
}
