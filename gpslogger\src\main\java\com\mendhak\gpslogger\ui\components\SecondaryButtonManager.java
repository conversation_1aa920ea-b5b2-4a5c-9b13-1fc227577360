/*
 * Copyright (C) 2016 mendhak
 *
 * This file is part of GPSLogger for Android.
 *
 * GPSLogger for Android is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 2 of the License, or
 * (at your option) any later version.
 *
 * GPSLogger for Android is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with GPSLogger for Android.  If not, see <http://www.gnu.org/licenses/>.
 */

package com.mendhak.gpslogger.ui.components;

import com.mendhak.gpslogger.ui.fragments.display.AnnotationViewFragment.ButtonWrapper;
import com.mendhak.gpslogger.ui.fragments.display.AnnotationViewFragment.SecondaryButton;
import com.mendhak.gpslogger.ui.components.LayoutEnums.TriggerMode;

import java.util.*;

/**
 * 二级按钮管理器
 * 负责管理一级按钮下的二级按钮列表
 */
public class SecondaryButtonManager {
    
    /**
     * 为一级按钮添加二级按钮
     */
    public static void addSecondaryButton(ButtonWrapper primaryButton, String text, String color, TriggerMode triggerMode) {
        if (primaryButton == null) return;
        
        String buttonId = "secondary_" + System.currentTimeMillis() + "_" + UUID.randomUUID().toString().substring(0, 8);
        SecondaryButton secondaryButton = new SecondaryButton(buttonId, text);
        secondaryButton.setColor(color);
        secondaryButton.setTriggerMode(triggerMode);
        secondaryButton.setOrder(primaryButton.getSecondaryButtonCount());
        
        primaryButton.addSecondaryButton(secondaryButton);
        primaryButton.setPrimaryButton(true); // 标记为一级按钮
    }
    
    /**
     * 删除二级按钮
     */
    public static void removeSecondaryButton(ButtonWrapper primaryButton, SecondaryButton secondaryButton) {
        if (primaryButton == null || secondaryButton == null) return;
        
        primaryButton.removeSecondaryButton(secondaryButton);
        
        // 如果没有二级按钮了，取消一级按钮标记
        if (!primaryButton.hasSecondaryButtons()) {
            primaryButton.setPrimaryButton(false);
        }
        
        // 重新排序剩余的二级按钮
        reorderSecondaryButtons(primaryButton);
    }
    
    /**
     * 重新排序二级按钮
     */
    public static void reorderSecondaryButtons(ButtonWrapper primaryButton) {
        if (primaryButton == null || !primaryButton.hasSecondaryButtons()) return;
        
        List<SecondaryButton> secondaryButtons = primaryButton.getSecondaryButtons();
        for (int i = 0; i < secondaryButtons.size(); i++) {
            secondaryButtons.get(i).setOrder(i);
        }
    }
    
    /**
     * 移动二级按钮位置
     */
    public static void moveSecondaryButton(ButtonWrapper primaryButton, int fromPosition, int toPosition) {
        if (primaryButton == null || !primaryButton.hasSecondaryButtons()) return;
        
        List<SecondaryButton> secondaryButtons = primaryButton.getSecondaryButtons();
        if (fromPosition < 0 || fromPosition >= secondaryButtons.size() || 
            toPosition < 0 || toPosition >= secondaryButtons.size()) {
            return;
        }
        
        SecondaryButton button = secondaryButtons.remove(fromPosition);
        secondaryButtons.add(toPosition, button);
        
        // 重新排序
        reorderSecondaryButtons(primaryButton);
    }
    
    /**
     * 获取排序后的二级按钮列表
     */
    public static List<SecondaryButton> getSortedSecondaryButtons(ButtonWrapper primaryButton) {
        if (primaryButton == null || !primaryButton.hasSecondaryButtons()) {
            return new ArrayList<>();
        }
        
        List<SecondaryButton> buttons = new ArrayList<>(primaryButton.getSecondaryButtons());
        buttons.sort(Comparator.comparingInt(SecondaryButton::getOrder));
        return buttons;
    }
    
    /**
     * 根据ID查找二级按钮
     */
    public static SecondaryButton findSecondaryButtonById(ButtonWrapper primaryButton, String buttonId) {
        if (primaryButton == null || buttonId == null || !primaryButton.hasSecondaryButtons()) {
            return null;
        }
        
        for (SecondaryButton button : primaryButton.getSecondaryButtons()) {
            if (buttonId.equals(button.getId())) {
                return button;
            }
        }
        return null;
    }
    
    /**
     * 更新二级按钮
     */
    public static void updateSecondaryButton(ButtonWrapper primaryButton, String buttonId, 
                                           String newText, String newColor, TriggerMode newTriggerMode, 
                                           String newTemplateVariable) {
        SecondaryButton button = findSecondaryButtonById(primaryButton, buttonId);
        if (button != null) {
            button.setText(newText);
            button.setColor(newColor);
            button.setTriggerMode(newTriggerMode);
            button.setTemplateVariable(newTemplateVariable);
        }
    }
    
    /**
     * 创建默认的二级按钮集合
     */
    public static void createDefaultSecondaryButtons(ButtonWrapper primaryButton, String buttonType) {
        if (primaryButton == null) return;
        
        switch (buttonType.toLowerCase()) {
            case "voice":
                addSecondaryButton(primaryButton, "语音记录", "#4CAF50", TriggerMode.VOICE_INPUT);
                addSecondaryButton(primaryButton, "快速语音", "#2196F3", TriggerMode.VOICE_INPUT);
                break;
                
            case "text":
                addSecondaryButton(primaryButton, "文本输入", "#FF9800", TriggerMode.TEXT_INPUT);
                addSecondaryButton(primaryButton, "快速文本", "#9C27B0", TriggerMode.TEXT_INPUT);
                break;
                
            case "counter":
                addSecondaryButton(primaryButton, "计数+1", "#F44336", TriggerMode.COUNTER_ONLY);
                addSecondaryButton(primaryButton, "计数-1", "#607D8B", TriggerMode.COUNTER_ONLY);
                addSecondaryButton(primaryButton, "重置计数", "#795548", TriggerMode.COUNTER_ONLY);
                break;
                
            default:
                // 混合类型
                addSecondaryButton(primaryButton, "语音", "#4CAF50", TriggerMode.VOICE_INPUT);
                addSecondaryButton(primaryButton, "文本", "#FF9800", TriggerMode.TEXT_INPUT);
                addSecondaryButton(primaryButton, "计数", "#F44336", TriggerMode.COUNTER_ONLY);
                break;
        }
    }
    
    /**
     * 验证二级按钮数据
     */
    public static boolean validateSecondaryButton(SecondaryButton button) {
        return button != null && 
               button.getId() != null && !button.getId().trim().isEmpty() &&
               button.getText() != null && !button.getText().trim().isEmpty();
    }
    
    /**
     * 获取二级按钮的显示文本
     */
    public static String getSecondaryButtonDisplayText(SecondaryButton button) {
        if (button == null) return "";
        
        String text = button.getText();
        if (button.getTriggerMode() != null) {
            switch (button.getTriggerMode()) {
                case VOICE_INPUT:
                    text += " 🎤";
                    break;
                case TEXT_INPUT:
                    text += " ✏️";
                    break;
                case COUNTER_ONLY:
                    text += " 🔢";
                    break;
            }
        }
        return text;
    }
    
    /**
     * 复制二级按钮
     */
    public static SecondaryButton cloneSecondaryButton(SecondaryButton original) {
        if (original == null) return null;
        
        String newId = "secondary_" + System.currentTimeMillis() + "_" + UUID.randomUUID().toString().substring(0, 8);
        SecondaryButton clone = new SecondaryButton(newId, original.getText() + " (副本)");
        clone.setColor(original.getColor());
        clone.setTriggerMode(original.getTriggerMode());
        clone.setTemplateVariable(original.getTemplateVariable());
        clone.setEnabled(original.isEnabled());
        return clone;
    }
}
