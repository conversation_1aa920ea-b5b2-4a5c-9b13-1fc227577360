<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:layout_marginBottom="8dp">

    <!-- Group Header Card -->
    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="4dp"
        android:layout_marginBottom="2dp"
        app:cardCornerRadius="8dp"
        app:cardElevation="2dp">

        <LinearLayout
            android:id="@+id/group_header_layout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="?attr/selectableItemBackground"
            android:clickable="true"
            android:focusable="true"
            android:orientation="horizontal"
            android:padding="12dp">

            <!-- Group Icon -->
            <ImageView
                android:id="@+id/group_icon"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:layout_gravity="center_vertical"
                android:layout_marginEnd="12dp"
                android:src="@drawable/ic_folder"
                android:tint="@android:color/black" />

            <!-- Group Name -->
            <TextView
                android:id="@+id/group_name_text"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:layout_weight="1"
                android:text="test1"
                android:textColor="@android:color/black"
                android:textSize="16sp"
                android:textStyle="bold" />

            <!-- Sequential Push Button for Group -->
            <Button
                android:id="@+id/group_sequential_push_button"
                android:layout_width="wrap_content"
                android:layout_height="28dp"
                android:layout_marginEnd="6dp"
                android:background="@drawable/button_background_orange"
                android:minWidth="0dp"
                android:minHeight="0dp"
                android:paddingHorizontal="8dp"
                android:text="顺序推送"
                android:textColor="@android:color/white"
                android:textSize="10sp" />

            <!-- Point Count -->
            <TextView
                android:id="@+id/point_count_text"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:layout_marginEnd="4dp"
                android:text="(9)"
                android:textColor="@android:color/darker_gray"
                android:textSize="14sp" />

            <!-- Quick Reset Button -->
            <ImageButton
                android:id="@+id/group_quick_reset_button"
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:layout_gravity="center_vertical"
                android:layout_marginEnd="4dp"
                android:src="@drawable/ic_refresh_24dp"
                android:background="?android:attr/selectableItemBackgroundBorderless"
                android:scaleType="centerInside"
                android:tint="@android:color/black"
                android:contentDescription="快速重置分组数据" />

            <!-- Drag Handle -->
            <ImageView
                android:id="@+id/drag_handle"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:layout_gravity="center_vertical"
                android:layout_marginEnd="8dp"
                android:src="@drawable/ic_drag_handle"
                android:tint="@android:color/black"
                android:alpha="0.6" />

            <!-- Expand/Collapse Button -->
            <ImageView
                android:id="@+id/expand_collapse_button"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:layout_gravity="center_vertical"
                android:layout_marginEnd="8dp"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:clickable="true"
                android:focusable="true"
                android:src="@drawable/ic_expand_less"
                android:tint="@android:color/black" />

            <!-- Settings Button -->
            <ImageView
                android:id="@+id/group_settings_button"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:layout_gravity="center_vertical"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:clickable="true"
                android:focusable="true"
                android:src="@drawable/ic_settings"
                android:tint="@android:color/black" />

        </LinearLayout>

    </androidx.cardview.widget.CardView>

    <!-- Location Points RecyclerView -->
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/location_points_recycler"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="8dp"
        android:nestedScrollingEnabled="false" />

</LinearLayout>
