package com.mendhak.gpslogger.ui.fragments.display;

import android.content.Intent;
import android.content.SharedPreferences;
import android.location.Location;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.TextView;
import android.widget.Toast;

import androidx.preference.PreferenceManager;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.mendhak.gpslogger.R;
import com.mendhak.gpslogger.common.EventBusHook;
import com.mendhak.gpslogger.common.PreferenceHelper;
import com.mendhak.gpslogger.common.events.ServiceEvents;
import com.mendhak.gpslogger.common.slf4j.Logs;
import com.mendhak.gpslogger.ui.adapters.LocationGroupAdapter;
import com.mendhak.gpslogger.ui.models.LocationGroup;
import com.mendhak.gpslogger.ui.models.LocationPoint;
import com.mendhak.gpslogger.ui.utils.PushLocationFileParser;
import com.mendhak.gpslogger.ui.utils.SinglePointPushHelper;
import com.mendhak.gpslogger.ui.managers.StateManager;
import com.mendhak.gpslogger.ui.managers.ButtonConfigManager;
import com.mendhak.gpslogger.ui.dialogs.GroupSettingsDialog;
import com.mendhak.gpslogger.ui.components.HapticFeedbackManager;
import com.mendhak.gpslogger.ui.utils.PushLocationResultManager;
import com.mendhak.gpslogger.ui.components.template.providers.SinglePointPushVariableProvider;
import com.mendhak.gpslogger.ui.models.LocationPointState;
import com.mendhak.gpslogger.ui.utils.GroupDragCallback;

import androidx.recyclerview.widget.ItemTouchHelper;
import com.mendhak.gpslogger.common.events.CommandEvents;

import android.app.Activity;
import android.speech.RecognizerIntent;
import java.util.ArrayList;

import org.slf4j.Logger;

import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * 单点推送视图Fragment
 * 用于实现单点位置推送功能
 */
public class SinglePointPushFragment extends GenericViewFragment implements LocationGroupAdapter.OnLocationGroupClickListener {

    private static final Logger LOG = Logs.of(SinglePointPushFragment.class);

    private View rootView;

    // UI组件
    private Button refreshButton;
    private RecyclerView locationGroupsRecycler;
    private LinearLayout emptyStateLayout;
    private LinearLayout loadingLayout;
    private Button createSampleFileButton;

    // 统计信息UI组件
    private TextView pushProgressText;
    private TextView passCountText;
    private TextView failCountText;
    private TextView groupCountText;

    // 数据和适配器
    private LocationGroupAdapter locationGroupAdapter;
    private List<LocationGroup> locationGroups;
    private ExecutorService executorService;

    // 触觉反馈管理器
    private HapticFeedbackManager hapticFeedbackManager;

    // 推送结果管理器
    private PushLocationResultManager resultManager;

    // 组级推送索引管理
    private Map<String, Integer> groupPushIndexMap = new HashMap<>();

    // 全局顺序推送状态管理
    private int globalSequentialPushGroupIndex = 0; // 当前推送的分组索引

    // Toast管理器
    private Toast currentToast;
    private Handler mainHandler = new Handler(Looper.getMainLooper());

    // 语音识别上下文
    private VoiceContext currentVoiceContext;

    /**
     * 语音识别上下文类
     */
    private static class VoiceContext {
        final LocationGroup group;
        final LocationPoint point;
        final boolean isPass;

        VoiceContext(LocationGroup group, LocationPoint point, boolean isPass) {
            this.group = group;
            this.point = point;
            this.isPass = isPass;
        }
    }

    // 状态管理器
    private StateManager stateManager;

    // 按钮配置管理器
    private ButtonConfigManager buttonConfigManager;



    public static SinglePointPushFragment newInstance() {
        return new SinglePointPushFragment();
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        rootView = inflater.inflate(R.layout.fragment_single_point_push, container, false);

        initializeViews();
        setupRecyclerView();
        setupClickListeners();

        executorService = Executors.newSingleThreadExecutor();

        // 初始化触觉反馈管理器
        hapticFeedbackManager = new HapticFeedbackManager(getContext());

        // 初始化推送结果管理器
        resultManager = PushLocationResultManager.getInstance();
        resultManager.initializeResultFile(getContext());

        // 加载数据
        loadLocationData();

        return rootView;
    }



    private void initializeViews() {
        refreshButton = rootView.findViewById(R.id.refresh_button);
        locationGroupsRecycler = rootView.findViewById(R.id.location_groups_recycler);
        emptyStateLayout = rootView.findViewById(R.id.empty_state_layout);
        loadingLayout = rootView.findViewById(R.id.loading_layout);
        createSampleFileButton = rootView.findViewById(R.id.create_sample_file_button);

        // 统计信息UI组件
        pushProgressText = rootView.findViewById(R.id.push_progress_text);
        passCountText = rootView.findViewById(R.id.pass_count_text);
        failCountText = rootView.findViewById(R.id.fail_count_text);
        groupCountText = rootView.findViewById(R.id.group_count_text);
    }

    private void setupRecyclerView() {
        locationGroups = new ArrayList<>();

        // 初始化状态管理器
        stateManager = StateManager.getInstance(getContext());

        // 初始化按钮配置管理器
        buttonConfigManager = ButtonConfigManager.getInstance(getContext());

        locationGroupAdapter = new LocationGroupAdapter(locationGroups);
        locationGroupAdapter.setOnLocationGroupClickListener(this);
        locationGroupAdapter.setStateManager(stateManager);
        locationGroupAdapter.setButtonConfigManager(buttonConfigManager);

        locationGroupsRecycler.setLayoutManager(new LinearLayoutManager(getContext()));
        locationGroupsRecycler.setAdapter(locationGroupAdapter);

        // 设置拖动排序功能
        GroupDragCallback dragCallback = new GroupDragCallback(locationGroupAdapter);
        ItemTouchHelper itemTouchHelper = new ItemTouchHelper(dragCallback);
        itemTouchHelper.attachToRecyclerView(locationGroupsRecycler);
        locationGroupAdapter.setItemTouchHelper(itemTouchHelper);
    }

    private void setupClickListeners() {
        refreshButton.setOnClickListener(v -> loadLocationData());

        createSampleFileButton.setOnClickListener(v -> createSampleFile());
    }

    private void loadLocationData() {
        showLoadingState();

        executorService.execute(() -> {
            try {
                File logDirectory = new File(PreferenceHelper.getInstance().getGpsLoggerFolder());
                List<LocationGroup> groups = PushLocationFileParser.parseFile(logDirectory);

                if (getActivity() != null) {
                    getActivity().runOnUiThread(() -> {
                        hideLoadingState();

                        if (groups.isEmpty()) {
                            showEmptyState();
                        } else {
                            showDataState();
                            updateLocationGroups(groups);
                        }
                    });
                }

            } catch (Exception e) {
                LOG.error("Error loading location data", e);
                if (getActivity() != null) {
                    getActivity().runOnUiThread(() -> {
                        hideLoadingState();
                        showEmptyState();
                        Toast.makeText(getContext(), "加载位置数据失败: " + e.getMessage(), Toast.LENGTH_LONG).show();
                    });
                }
            }
        });
    }

    private void updateLocationGroups(List<LocationGroup> groups) {
        this.locationGroups.clear();
        this.locationGroups.addAll(groups);
        locationGroupAdapter.updateLocationGroups(this.locationGroups);

        // 应用保存的分组顺序
        locationGroupAdapter.sortGroupsByOrder();

        // 重置Fragment级别的推送索引映射
        // 注意：不再调用clearPushStates()，因为LocationGroupAdapter.updateLocationGroups()
        // 已经处理了状态清除和从持久化存储中的状态恢复
        groupPushIndexMap.clear();

        // 重置全局顺序推送状态
        globalSequentialPushGroupIndex = 0;

        // 更新统计信息
        updateStatistics();
    }



    /**
     * 更新统计信息显示
     */
    private void updateStatistics() {
        if (locationGroups == null || locationGroupAdapter == null) {
            // 重置统计信息
            if (pushProgressText != null) pushProgressText.setText("推送进度：0/0");
            if (passCountText != null) passCountText.setText("通过：0");
            if (failCountText != null) failCountText.setText("不通过：0");
            if (groupCountText != null) groupCountText.setText("分组：0");
            return;
        }

        int totalPoints = 0;
        int pushedPoints = 0;
        int passedPoints = 0;
        int failedPoints = 0;
        int groupCount = locationGroups.size();

        // 统计所有分组的数据
        for (LocationGroup group : locationGroups) {
            List<LocationPoint> points = group.getLocationPoints();
            totalPoints += points.size();

            // 获取分组的状态集合
            java.util.Set<Integer> pushedPointsSet = locationGroupAdapter.getGroupPushedPoints(group.getGroupName());
            java.util.Set<Integer> passedPointsSet = locationGroupAdapter.getGroupPassedPoints(group.getGroupName());
            java.util.Set<Integer> failedPointsSet = locationGroupAdapter.getGroupFailedPoints(group.getGroupName());

            // 统计状态数量
            pushedPoints += pushedPointsSet.size();
            passedPoints += passedPointsSet.size();
            failedPoints += failedPointsSet.size();

            // 更新分组统计信息到变量提供者
            SinglePointPushVariableProvider.updateGroupStatistics(getContext(),
                group.getGroupName(), points.size(),
                pushedPointsSet.size(), passedPointsSet.size(), failedPointsSet.size());
        }

        // 更新全局统计信息到变量提供者
        SinglePointPushVariableProvider.updateGlobalStatistics(getContext(),
            totalPoints, pushedPoints, passedPoints, failedPoints);

        // 更新UI显示
        if (pushProgressText != null) {
            pushProgressText.setText("推送进度：" + pushedPoints + "/" + totalPoints);
        }
        if (passCountText != null) {
            passCountText.setText("通过：" + passedPoints);
        }
        if (failCountText != null) {
            failCountText.setText("不通过：" + failedPoints);
        }
        if (groupCountText != null) {
            groupCountText.setText("分组：" + groupCount);
        }
    }

    private void showLoadingState() {
        loadingLayout.setVisibility(View.VISIBLE);
        locationGroupsRecycler.setVisibility(View.GONE);
        emptyStateLayout.setVisibility(View.GONE);
    }

    private void hideLoadingState() {
        loadingLayout.setVisibility(View.GONE);
    }

    private void showEmptyState() {
        emptyStateLayout.setVisibility(View.VISIBLE);
        locationGroupsRecycler.setVisibility(View.GONE);

        // 更新空状态描述，显示当天日期文件夹路径
        File logDirectory = new File(PreferenceHelper.getInstance().getGpsLoggerFolder());
        String filePath = PushLocationFileParser.getFilePath(logDirectory);
        TextView emptyStateDescription = rootView.findViewById(R.id.empty_state_description);
        emptyStateDescription.setText("请在当天日期文件夹中创建Push_location.txt文件：\n" + filePath);
    }

    private void showDataState() {
        locationGroupsRecycler.setVisibility(View.VISIBLE);
        emptyStateLayout.setVisibility(View.GONE);
    }

    private void createSampleFile() {
        executorService.execute(() -> {
            File logDirectory = new File(PreferenceHelper.getInstance().getGpsLoggerFolder());
            boolean success = PushLocationFileParser.createSampleFile(logDirectory);

            if (getActivity() != null) {
                getActivity().runOnUiThread(() -> {
                    if (success) {
                        Toast.makeText(getContext(), "示例文件创建成功", Toast.LENGTH_SHORT).show();
                        loadLocationData(); // 重新加载数据
                    } else {
                        Toast.makeText(getContext(), "示例文件创建失败", Toast.LENGTH_SHORT).show();
                    }
                });
            }
        });
    }



    // LocationGroupAdapter.OnLocationGroupClickListener 实现
    @Override
    public void onGroupHeaderClick(LocationGroup group) {
        // 分组头部点击，已在适配器中处理展开/折叠
    }

    @Override
    public void onGroupSettingsClick(LocationGroup group) {
        showGroupSettingsDialog(group);
    }

    @Override
    public void onGroupSequentialPushClick(LocationGroup group) {
        // 播放顺序推送按钮音频反馈
        playButtonAudioFeedback(com.mendhak.gpslogger.ui.components.AudioFeedbackManager.BUTTON_TYPE_SEQUENTIAL_PUSH);

        // 提供触觉反馈
        if (hapticFeedbackManager != null && hapticFeedbackManager.isHapticFeedbackEnabled()) {
            hapticFeedbackManager.performButtonClickFeedback();
        }

        // 检查单点推送是否启用
        if (!isLocationPushEnabled()) {
            showToast("请先在设置中启用单点推送功能", Toast.LENGTH_LONG);
            return;
        }

        if (group.getLocationPoints().isEmpty()) {
            showToast("该分组没有位置点", Toast.LENGTH_SHORT);
            return;
        }

        // 执行手动逐个推送
        performGroupSequentialPush(group);
    }

    @Override
    public void onGroupQuickResetClick(LocationGroup group) {
        // 显示快速重置确认对话框
        new android.app.AlertDialog.Builder(getContext())
                .setTitle("快速重置")
                .setMessage("确定要重置分组 \"" + group.getGroupName() + "\" 的持久化数据吗？\n\n" +
                           "此操作将清除所有推送状态和通过/不通过状态。")
                .setPositiveButton("重置", (dialog, which) -> {
                    // 清除分组状态
                    if (locationGroupAdapter != null) {
                        locationGroupAdapter.clearGroupState(group.getGroupName());
                    }
                    showToast("分组数据已重置: " + group.getGroupName(), Toast.LENGTH_SHORT);
                    updateStatistics(); // 更新统计信息
                })
                .setNegativeButton("取消", null)
                .show();
    }

    @Override
    public void onLocationPointPassClick(LocationGroup group, LocationPoint point) {
        // 获取通过按钮配置
        com.mendhak.gpslogger.ui.managers.ButtonConfigManager configManager =
            com.mendhak.gpslogger.ui.managers.ButtonConfigManager.getInstance(getContext());
        com.mendhak.gpslogger.ui.models.ButtonConfig passConfig = configManager.getPassButtonConfig();

        // 播放音频反馈
        playButtonAudioFeedback(com.mendhak.gpslogger.ui.components.AudioFeedbackManager.BUTTON_TYPE_PASS);

        // 提供触觉反馈
        if (hapticFeedbackManager != null && hapticFeedbackManager.isHapticFeedbackEnabled()) {
            hapticFeedbackManager.performButtonClickFeedback();
        }

        // 根据按钮模式执行不同的操作
        switch (passConfig.getType()) {
            case TEXT:
                showTextInputDialog("通过", group, point, true);
                break;
            case VOICE:
                startVoiceRecognition("通过", group, point, true);
                break;
            case COUNTER:
                handleCounterMode(group, point, true);
                break;
            default:
                // 默认行为：直接标记为通过，清空输入变量
                clearInputVariables();
                markPointAsPassed(group, point);
                break;
        }
    }

    @Override
    public void onLocationPointFailClick(LocationGroup group, LocationPoint point) {
        // 获取不过按钮配置
        com.mendhak.gpslogger.ui.managers.ButtonConfigManager configManager =
            com.mendhak.gpslogger.ui.managers.ButtonConfigManager.getInstance(getContext());
        com.mendhak.gpslogger.ui.models.ButtonConfig failConfig = configManager.getFailButtonConfig();

        // 播放音频反馈
        playButtonAudioFeedback(com.mendhak.gpslogger.ui.components.AudioFeedbackManager.BUTTON_TYPE_FAIL);

        // 提供触觉反馈
        if (hapticFeedbackManager != null && hapticFeedbackManager.isHapticFeedbackEnabled()) {
            hapticFeedbackManager.performButtonClickFeedback();
        }

        // 根据按钮模式执行不同的操作
        switch (failConfig.getType()) {
            case TEXT:
                showTextInputDialog("不过", group, point, false);
                break;
            case VOICE:
                startVoiceRecognition("不过", group, point, false);
                break;
            case COUNTER:
                handleCounterMode(group, point, false);
                break;
            default:
                // 默认行为：直接标记为不通过，清空输入变量
                clearInputVariables();
                markPointAsFailed(group, point);
                break;
        }
    }

    @Override
    public void onLocationPointPushClick(LocationGroup group, LocationPoint point) {
        // 播放音频反馈
        playButtonAudioFeedback(com.mendhak.gpslogger.ui.components.AudioFeedbackManager.BUTTON_TYPE_PUSH);

        // 提供触觉反馈
        if (hapticFeedbackManager != null && hapticFeedbackManager.isHapticFeedbackEnabled()) {
            hapticFeedbackManager.performButtonClickFeedback();
        }

        performSinglePointPush(group, point);
    }

    @Override
    public void onLocationPointSequenceNumberLongClick(LocationGroup group, LocationPoint point) {
        // 长按序号取消推送状态
        int pointIndex = point.getSequenceNumber() - 1; // 序号从1开始，索引从0开始

        // 清除推送状态
        locationGroupAdapter.clearPointPushStatus(group.getGroupName(), pointIndex);

        showToast("已取消推送状态: " + point.getDescription(), Toast.LENGTH_SHORT);
    }

    @Override
    public void onLocationPointDescriptionLongClick(LocationGroup group, LocationPoint point) {
        // 长按描述取消通过/不通过状态
        int pointIndex = point.getSequenceNumber() - 1; // 序号从1开始，索引从0开始

        // 这里需要通过适配器清除通过/不通过状态
        // 由于我们需要直接访问LocationPointAdapter，我们需要一个不同的方法
        clearPointPassFailStatus(group.getGroupName(), pointIndex);

        showToast("已取消通过/不通过状态: " + point.getDescription(), Toast.LENGTH_SHORT);
    }

    @Override
    public void onLocationPointSetStartIndex(LocationGroup group, LocationPoint point, int pointIndex) {
        // 设置顺序推送起点
        String groupName = group.getGroupName();

        // 更新适配器中的起点索引
        locationGroupAdapter.setStartIndex(groupName, pointIndex);

        // 显示提示信息
        showToast("已设置起点: 第" + (pointIndex + 1) + "个位置点 - " + point.getDescription(), Toast.LENGTH_SHORT);

        LOG.info("Set start index for group {}: {}", groupName, pointIndex);
    }

    /**
     * 清除位置点的通过/不通过状态
     */
    private void clearPointPassFailStatus(String groupName, int pointIndex) {
        // 使用适配器的清除方法
        locationGroupAdapter.clearPointPassFailStatus(groupName, pointIndex);
    }

    /**
     * 播放按钮音频反馈
     */
    private void playButtonAudioFeedback(String buttonType) {
        try {
            com.mendhak.gpslogger.ui.components.AudioFeedbackManager audioManager =
                com.mendhak.gpslogger.ui.components.AudioFeedbackManager.getInstance(getContext());

            // 直接使用传入的按钮类型
            audioManager.playButtonFeedback(buttonType);
        } catch (Exception e) {
            LOG.error("播放音频反馈失败", e);
        }
    }

    /**
     * 显示文本输入对话框
     */
    private void showTextInputDialog(String buttonName, LocationGroup group, LocationPoint point, boolean isPass) {
        com.mendhak.gpslogger.ui.dialogs.TextInputDialog.show(
            getContext(),
            buttonName,
            new com.mendhak.gpslogger.ui.dialogs.TextInputDialog.OnTextInputListener() {
                @Override
                public void onTextInputResult(String inputText) {
                    // 存储输入文本到模板变量
                    com.mendhak.gpslogger.ui.components.template.providers.BasicVariableProvider.setInputText(getContext(), inputText);

                    // 标记位置点状态
                    if (isPass) {
                        markPointAsPassed(group, point);
                        showToast("通过: " + inputText + " - " + point.getDescription(), Toast.LENGTH_SHORT);
                    } else {
                        markPointAsFailed(group, point);
                        showToast("不过: " + inputText + " - " + point.getDescription(), Toast.LENGTH_SHORT);
                    }
                }

                @Override
                public void onTextInputCancelled() {
                    // 用户取消了文本输入
                    LOG.debug("Text input cancelled for {} button", buttonName);
                }
            }
        );
    }

    /**
     * 启动语音识别
     */
    private void startVoiceRecognition(String buttonName, LocationGroup group, LocationPoint point, boolean isPass) {
        try {
            com.mendhak.gpslogger.ui.components.VoiceInputManager voiceManager =
                new com.mendhak.gpslogger.ui.components.VoiceInputManager(this, new com.mendhak.gpslogger.ui.components.VoiceInputManager.VoiceInputListener() {
                    @Override
                    public void onVoiceInputStarted() {
                        showToast("语音识别中...", Toast.LENGTH_SHORT);
                    }

                    @Override
                    public void onVoiceInputResult(String text) {
                        handleVoiceInputResult(text);
                    }

                    @Override
                    public void onVoiceInputError(String error) {
                        showToast("语音识别失败: " + error, Toast.LENGTH_SHORT);
                        currentVoiceContext = null;
                    }

                    @Override
                    public void onVoiceInputCancelled() {
                        showToast("语音识别已取消", Toast.LENGTH_SHORT);
                        currentVoiceContext = null;
                    }

                    @Override
                    public void onAudioRecordingStarted() {
                        showToast("录音中...", Toast.LENGTH_SHORT);
                    }

                    @Override
                    public void onAudioRecordingFinished(String audioFilePath) {
                        showToast("录音完成", Toast.LENGTH_SHORT);
                        currentVoiceContext = null;
                    }

                    @Override
                    public void onAudioRecordingError(String error) {
                        showToast("录音失败: " + error, Toast.LENGTH_SHORT);
                        currentVoiceContext = null;
                    }

                    @Override
                    public void onAudioRecordingCancelled() {
                        showToast("录音已取消", Toast.LENGTH_SHORT);
                        currentVoiceContext = null;
                    }
                });

            if (voiceManager.startVoiceInput()) {
                // 保存当前上下文，用于语音识别结果处理
                currentVoiceContext = new VoiceContext(group, point, isPass);
            } else {
                showToast("语音识别不可用", Toast.LENGTH_SHORT);
                // 回退到默认行为
                if (isPass) {
                    markPointAsPassed(group, point);
                } else {
                    markPointAsFailed(group, point);
                }
            }
        } catch (Exception e) {
            LOG.error("启动语音识别失败", e);
            showToast("语音识别启动失败", Toast.LENGTH_SHORT);
            // 回退到默认行为
            if (isPass) {
                markPointAsPassed(group, point);
            } else {
                markPointAsFailed(group, point);
            }
        }
    }

    /**
     * 处理语音识别结果
     */
    private void handleVoiceInputResult(String voiceText) {
        if (currentVoiceContext != null && voiceText != null && !voiceText.trim().isEmpty()) {
            // 存储语音识别结果到模板变量
            com.mendhak.gpslogger.common.Session.getInstance().setTemplateContext(voiceText,
                currentVoiceContext.isPass ? "通过" : "不过", 0, currentVoiceContext.group.getGroupName());

            // 标记位置点状态
            if (currentVoiceContext.isPass) {
                markPointAsPassed(currentVoiceContext.group, currentVoiceContext.point);
                showToast("通过: " + voiceText + " - " + currentVoiceContext.point.getDescription(), Toast.LENGTH_SHORT);
            } else {
                markPointAsFailed(currentVoiceContext.group, currentVoiceContext.point);
                showToast("不过: " + voiceText + " - " + currentVoiceContext.point.getDescription(), Toast.LENGTH_SHORT);
            }

            // 清除语音识别上下文
            currentVoiceContext = null;
        }
    }

    /**
     * 处理计数器模式
     */
    private void handleCounterMode(LocationGroup group, LocationPoint point, boolean isPass) {
        // 计数器模式：清空输入变量
        clearInputVariables();

        // 获取下一个计数值
        int nextCounter = getNextCounter();

        // 标记位置点状态
        if (isPass) {
            markPointAsPassed(group, point);
            showToast("通过 #" + nextCounter + " - " + point.getDescription(), Toast.LENGTH_SHORT);
        } else {
            markPointAsFailed(group, point);
            showToast("不过 #" + nextCounter + " - " + point.getDescription(), Toast.LENGTH_SHORT);
        }
    }

    /**
     * 清空输入变量
     */
    private void clearInputVariables() {
        try {
            com.mendhak.gpslogger.ui.components.template.providers.BasicVariableProvider.clearInputText(getContext());
            com.mendhak.gpslogger.common.Session.getInstance().clearTemplateVoiceText();
        } catch (Exception e) {
            LOG.error("Error clearing input variables", e);
        }
    }

    /**
     * 标记位置点为通过状态
     */
    private void markPointAsPassed(LocationGroup group, LocationPoint point) {
        int pointIndex = point.getSequenceNumber() - 1; // 序号从1开始，索引从0开始
        locationGroupAdapter.markPointAsPassed(group.getGroupName(), pointIndex);

        // 记录通过操作到结果文件
        PreferenceHelper prefHelper = PreferenceHelper.getInstance();
        if (resultManager != null && prefHelper.isSinglePointPushTemplateEnabled()) {
            resultManager.recordPassOperation(getContext(), group, point, "通过");
        }

        updateStatistics(); // 更新统计信息
    }

    /**
     * 标记位置点为不通过状态
     */
    private void markPointAsFailed(LocationGroup group, LocationPoint point) {
        int pointIndex = point.getSequenceNumber() - 1; // 序号从1开始，索引从0开始
        locationGroupAdapter.markPointAsFailed(group.getGroupName(), pointIndex);

        // 记录不过操作到结果文件
        PreferenceHelper prefHelper = PreferenceHelper.getInstance();
        if (resultManager != null && prefHelper.isSinglePointPushTemplateEnabled()) {
            resultManager.recordFailOperation(getContext(), group, point, "不过");
        }

        updateStatistics(); // 更新统计信息
    }

    /**
     * 获取下一个计数值
     */
    private int getNextCounter() {
        SharedPreferences prefs = PreferenceManager.getDefaultSharedPreferences(getContext());
        int counter = prefs.getInt("button_counter", 0) + 1;
        prefs.edit().putInt("button_counter", counter).apply();
        return counter;
    }

    private void performSinglePointPush(LocationGroup group, LocationPoint point) {
        // 检查单点推送是否启用
        if (!isLocationPushEnabled()) {
            showToast("请先在设置中启用单点推送功能", Toast.LENGTH_LONG);
            return;
        }

        // 推送操作：清空输入变量
        clearInputVariables();

        try {
            // 设置{Push_location}用户变量
            SharedPreferences prefs = PreferenceManager.getDefaultSharedPreferences(getContext());
            SharedPreferences.Editor editor = prefs.edit();
            editor.putString("user_var_Push_location", point.getCoordinatesString());
            editor.apply();

            // 创建位置对象并触发单点推送Intent
            Location location = SinglePointPushHelper.createLocationFromCoordinates(
                    point.getLatitude(), point.getLongitude());
            SinglePointPushHelper.sendSinglePointPushIntent(getContext(), location);

            // 标记该位置点为已推送（更新颜色状态）
            int pointIndex = point.getSequenceNumber() - 1; // 序号从1开始，索引从0开始
            locationGroupAdapter.markPointAsPushed(group.getGroupName(), pointIndex);

            // 记录推送操作到结果文件
            PreferenceHelper prefHelper = PreferenceHelper.getInstance();
            if (resultManager != null && prefHelper.isSinglePointPushTemplateEnabled()) {
                resultManager.recordPushWithLocation(getContext(), group, point, location);
            }

            showToast("推送: " + point.getDescription(), Toast.LENGTH_SHORT);

            updateStatistics(); // 更新统计信息

        } catch (Exception e) {
            LOG.error("Error performing single point push", e);
            showToast("推送失败: " + e.getMessage(), Toast.LENGTH_LONG);
        }
    }

    // 重载方法，用于组级顺序推送
    private void performSinglePointPush(LocationPoint point) {
        // 顺序推送操作：清空输入变量
        clearInputVariables();

        try {
            // 设置{Push_location}用户变量
            SharedPreferences prefs = PreferenceManager.getDefaultSharedPreferences(getContext());
            SharedPreferences.Editor editor = prefs.edit();
            editor.putString("user_var_Push_location", point.getCoordinatesString());
            editor.apply();

            // 创建位置对象并触发单点推送Intent
            Location location = SinglePointPushHelper.createLocationFromCoordinates(
                    point.getLatitude(), point.getLongitude());
            SinglePointPushHelper.sendSinglePointPushIntent(getContext(), location);

        } catch (Exception e) {
            LOG.error("Error performing single point push", e);
            throw e; // 重新抛出异常，让调用者处理
        }
    }

    /**
     * 处理语音识别结果
     */
    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);

        if (requestCode == com.mendhak.gpslogger.ui.components.VoiceInputManager.VOICE_INPUT_REQUEST_CODE) {
            if (resultCode == Activity.RESULT_OK && data != null && currentVoiceContext != null) {
                ArrayList<String> results = data.getStringArrayListExtra(RecognizerIntent.EXTRA_RESULTS);
                if (results != null && !results.isEmpty()) {
                    String voiceText = results.get(0);

                    // 存储语音识别结果到模板变量
                    com.mendhak.gpslogger.common.Session.getInstance().setTemplateContext(voiceText,
                        currentVoiceContext.isPass ? "通过" : "不过", 0, currentVoiceContext.group.getGroupName());

                    // 标记位置点状态
                    if (currentVoiceContext.isPass) {
                        markPointAsPassed(currentVoiceContext.group, currentVoiceContext.point);
                        showToast("通过: " + voiceText + " - " + currentVoiceContext.point.getDescription(), Toast.LENGTH_SHORT);
                    } else {
                        markPointAsFailed(currentVoiceContext.group, currentVoiceContext.point);
                        showToast("不过: " + voiceText + " - " + currentVoiceContext.point.getDescription(), Toast.LENGTH_SHORT);
                    }
                } else {
                    showToast("语音识别失败", Toast.LENGTH_SHORT);
                    // 回退到默认行为
                    if (currentVoiceContext.isPass) {
                        markPointAsPassed(currentVoiceContext.group, currentVoiceContext.point);
                    } else {
                        markPointAsFailed(currentVoiceContext.group, currentVoiceContext.point);
                    }
                }
            } else {
                showToast("语音识别取消", Toast.LENGTH_SHORT);
                // 回退到默认行为
                if (currentVoiceContext != null) {
                    if (currentVoiceContext.isPass) {
                        markPointAsPassed(currentVoiceContext.group, currentVoiceContext.point);
                    } else {
                        markPointAsFailed(currentVoiceContext.group, currentVoiceContext.point);
                    }
                }
            }

            // 清除语音识别上下文
            currentVoiceContext = null;
        }
    }

    @EventBusHook
    public void onEventMainThread(ServiceEvents.LocationUpdate locationUpdate) {
        // TODO: 处理位置更新事件
        // 在这里添加单点推送的具体逻辑
    }

    @EventBusHook
    public void onEventMainThread(ServiceEvents.LoggingStatus loggingStatus) {
        // TODO: 处理日志状态变化
        // 在这里添加状态变化的处理逻辑
    }

    @EventBusHook
    public void onEventMainThread(ServiceEvents.WaitingForLocation waitingForLocation) {
        // TODO: 处理等待位置状态
        // 在这里添加等待位置的处理逻辑
    }

    @EventBusHook
    public void onEventMainThread(ServiceEvents.SatellitesVisible satellitesVisible) {
        // TODO: 处理卫星可见状态
        // 在这里添加卫星状态的处理逻辑
    }

    /**
     * 处理外接设备触发的顺序推送请求
     */
    @EventBusHook
    public void onEventMainThread(CommandEvents.RequestSequentialPush requestSequentialPush) {
        LOG.info("External device sequential push request from: {}", requestSequentialPush.source);

        if (getActivity() == null || !isAdded()) {
            LOG.warn("Fragment not attached, ignoring sequential push request");
            return;
        }

        // 触发顺序推送功能
        triggerSequentialPushFromExternal(requestSequentialPush.source);
    }

    /**
     * 检查单点推送功能是否启用
     */
    private boolean isLocationPushEnabled() {
        try {
            com.mendhak.gpslogger.common.PreferenceHelper preferenceHelper =
                com.mendhak.gpslogger.common.PreferenceHelper.getInstance();
            return preferenceHelper.isSinglePointPushEnabled();
        } catch (Exception e) {
            LOG.error("Error checking single point push enabled status", e);
            return false;
        }
    }

    /**
     * 安全显示Toast消息
     */
    private void showToast(String message, int duration) {
        // 确保在主线程中执行
        if (Looper.myLooper() == Looper.getMainLooper()) {
            // 已经在主线程中
            showToastInternal(message, duration);
        } else {
            // 切换到主线程
            mainHandler.post(() -> showToastInternal(message, duration));
        }
    }

    private void showToastInternal(String message, int duration) {
        try {
            // 检查Fragment状态
            if (getContext() == null || !isAdded() || isDetached()) {
                LOG.debug("Fragment not in valid state for showing toast");
                return;
            }

            // 取消之前的Toast
            if (currentToast != null) {
                currentToast.cancel();
                currentToast = null;
            }

            // 限制消息长度，避免显示问题
            if (message.length() > 100) {
                message = message.substring(0, 97) + "...";
            }

            // 创建新的Toast
            currentToast = Toast.makeText(getContext(), message, duration);
            currentToast.show();

            LOG.debug("Toast displayed: {}", message);

        } catch (Exception e) {
            LOG.error("Error showing toast: " + message, e);
        }
    }



    /**
     * 执行分组手动逐个推送（智能跳过已推送的位置点，支持起点设置）
     */
    private void performGroupSequentialPush(LocationGroup group) {
        String groupKey = group.getGroupName();
        List<LocationPoint> points = group.getLocationPoints();

        // 使用智能跳过逻辑获取下一个未推送的位置点索引（已考虑起点设置）
        int nextUnpushedIndex = locationGroupAdapter.getNextUnpushedPointIndex(groupKey, points.size());

        // 检查是否已推送完所有点
        if (nextUnpushedIndex >= points.size()) {
            showToast("从起点开始的所有位置点已推送完成", Toast.LENGTH_SHORT);
            return;
        }

        // 获取下一个要推送的位置点
        LocationPoint nextPoint = points.get(nextUnpushedIndex);

        try {
            // 推送当前位置点
            performSinglePointPush(nextPoint);

            // 标记该位置点为已推送（使用智能状态管理）
            locationGroupAdapter.markSequentialPointAsPushed(groupKey, nextUnpushedIndex);

            // 记录顺序推送操作到结果文件
            PreferenceHelper prefHelper = PreferenceHelper.getInstance();
            if (resultManager != null && prefHelper.isSinglePointPushTemplateEnabled()) {
                // 找到对应的分组对象
                LocationGroup currentGroup = null;
                for (LocationGroup g : locationGroups) {
                    if (g.getGroupName().equals(groupKey)) {
                        currentGroup = g;
                        break;
                    }
                }
                if (currentGroup != null) {
                    resultManager.recordSequentialPushOperation(getContext(), currentGroup, nextPoint, nextUnpushedIndex);
                }
            }

            // 计算已推送的位置点数量
            int pushedCount = locationGroupAdapter.getGroupPushedPoints(groupKey).size();

            // 显示推送进度提示
            showToast("推送第 " + (nextUnpushedIndex + 1) + "/" + points.size() + " 个位置点：" + nextPoint.getDescription(),
                Toast.LENGTH_SHORT);

        } catch (Exception e) {
            LOG.error("Error in group sequential push", e);
            showToast("推送失败: " + e.getMessage(), Toast.LENGTH_LONG);
        }
    }

    /**
     * 显示分组设置对话框
     */
    private void showGroupSettingsDialog(LocationGroup group) {
        if (getContext() == null) return;

        GroupSettingsDialog dialog = new GroupSettingsDialog(getContext(), group, locationGroupAdapter);

        // 设置监听器
        dialog.setOnGroupSettingsListener(new GroupSettingsDialog.OnGroupSettingsListener() {
            @Override
            public void onGroupRenamed(LocationGroup group, String newName) {
                // 更新分组名称
                group.setGroupName(newName);

                // 更新状态管理器中的分组名称
                if (stateManager != null) {
                    stateManager.renameGroup(group.getGroupName(), newName);
                }

                // 刷新适配器
                if (locationGroupAdapter != null) {
                    locationGroupAdapter.notifyDataSetChanged();
                }

                showToast("分组名称已更新", Toast.LENGTH_SHORT);
            }

            @Override
            public void onGroupIconChanged(LocationGroup group, String newIconId) {
                // 更新分组图标
                group.setIconId(newIconId);

                // 刷新适配器
                if (locationGroupAdapter != null) {
                    locationGroupAdapter.notifyDataSetChanged();
                }

                showToast("分组图标已更新", Toast.LENGTH_SHORT);
            }

            @Override
            public void onGroupDeleted(LocationGroup group) {
                // 删除分组
                if (locationGroups != null) {
                    locationGroups.remove(group);

                    // 从状态管理器中删除分组状态
                    if (stateManager != null) {
                        stateManager.deleteGroup(group.getGroupName());
                    }

                    // 刷新适配器
                    if (locationGroupAdapter != null) {
                        locationGroupAdapter.updateLocationGroups(locationGroups);
                    }

                    showToast("分组已删除", Toast.LENGTH_SHORT);
                }
            }
        });

        dialog.show();
    }

    /**
     * 外接设备触发的顺序推送功能（支持跨分组推送）
     */
    private void triggerSequentialPushFromExternal(String source) {
        LOG.info("Triggering sequential push from external device: {}", source);

        // 播放顺序推送按钮音频反馈
        playButtonAudioFeedback(com.mendhak.gpslogger.ui.components.AudioFeedbackManager.BUTTON_TYPE_SEQUENTIAL_PUSH);

        // 检查是否有分组数据
        if (locationGroups == null || locationGroups.isEmpty()) {
            showToast("没有可推送的位置分组", Toast.LENGTH_SHORT);
            LOG.warn("No location groups available for sequential push");
            return;
        }

        // 执行全局顺序推送（支持跨分组）
        performGlobalSequentialPush(source);
    }

    /**
     * 执行全局顺序推送（支持跨分组推送）
     */
    private void performGlobalSequentialPush(String source) {
        LOG.info("Starting global sequential push from: {}", source);

        // 从当前分组索引开始查找下一个可推送的位置点
        for (int groupIndex = globalSequentialPushGroupIndex; groupIndex < locationGroups.size(); groupIndex++) {
            LocationGroup currentGroup = locationGroups.get(groupIndex);
            String groupKey = currentGroup.getGroupName();
            List<LocationPoint> points = currentGroup.getLocationPoints();

            if (points.isEmpty()) {
                LOG.info("Group {} is empty, skipping to next group", groupKey);
                continue;
            }

            // 获取当前分组的下一个未推送位置点
            int nextUnpushedIndex = locationGroupAdapter.getNextUnpushedPointIndex(groupKey, points.size());

            // 如果当前分组还有未推送的位置点
            if (nextUnpushedIndex < points.size()) {
                // 更新全局分组索引
                globalSequentialPushGroupIndex = groupIndex;

                // 获取要推送的位置点
                LocationPoint nextPoint = points.get(nextUnpushedIndex);

                try {
                    // 推送当前位置点
                    performSinglePointPush(nextPoint);

                    // 标记该位置点为已推送
                    locationGroupAdapter.markSequentialPointAsPushed(groupKey, nextUnpushedIndex);

                    // 显示推送进度提示
                    showToast("外接设备推送 [" + currentGroup.getGroupName() + "] 第 " +
                            (nextUnpushedIndex + 1) + "/" + points.size() + " 个位置点：" + nextPoint.getDescription(),
                            Toast.LENGTH_SHORT);

                    LOG.info("Successfully pushed point {} from group {} (index {})",
                            nextUnpushedIndex, groupKey, groupIndex);
                    return; // 成功推送一个点后返回

                } catch (Exception e) {
                    LOG.error("Error in global sequential push for group {}, point {}", groupKey, nextUnpushedIndex, e);
                    showToast("推送失败: " + e.getMessage(), Toast.LENGTH_LONG);
                    return;
                }
            } else {
                LOG.info("Group {} completed, checking next group", groupKey);
            }
        }

        // 所有分组都推送完成
        LOG.info("All groups completed, resetting global sequential push state");
        globalSequentialPushGroupIndex = 0; // 重置到第一个分组
        showToast("所有分组的位置点已推送完成，重置到第一个分组", Toast.LENGTH_SHORT);
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        if (executorService != null && !executorService.isShutdown()) {
            executorService.shutdown();
        }
    }
}
