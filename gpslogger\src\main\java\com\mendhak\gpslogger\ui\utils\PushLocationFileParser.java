/*
 * Copyright (C) 2024 mendhak
 *
 * This file is part of GPSLogger for Android.
 *
 * GPSLogger is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 2 of the License, or
 * (at your option) any later version.
 *
 * GPSLogger is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with GPSLogger.  If not, see <http://www.gnu.org/licenses/>.
 */

package com.mendhak.gpslogger.ui.utils;

import android.content.Context;
import com.mendhak.gpslogger.common.slf4j.Logs;
import com.mendhak.gpslogger.common.FilePathManager;
import com.mendhak.gpslogger.common.AppSettings;
import com.mendhak.gpslogger.ui.models.LocationGroup;
import com.mendhak.gpslogger.ui.models.LocationPoint;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

import org.slf4j.Logger;

/**
 * Push_location.txt文件解析器
 * 
 * 文件格式：
 * Group:test1
 * 29.7575536,106.6363669_第1个点
 * 29.7575536,106.6363669_第2个点
 * Group:test2
 * 29.7575536,106.6363669_第1个点
 */
public class PushLocationFileParser {
    
    private static final Logger LOG = Logs.of(PushLocationFileParser.class);
    private static final String FILE_NAME = "Push_location.txt";
    private static final String GROUP_PREFIX = "Group:";
    
    /**
     * 解析Push_location.txt文件
     * 现在从当天的日期文件夹中读取文件
     *
     * @param logDirectory 日志文件夹路径（已废弃，保留以兼容现有调用）
     * @return 解析后的位置分组列表
     */
    public static List<LocationGroup> parseFile(File logDirectory) {
        List<LocationGroup> groups = new ArrayList<>();

        try {
            // 使用FilePathManager获取当天日期文件夹中的Push_location.txt文件路径
            Context context = AppSettings.getInstance().getApplicationContext();
            File pushLocationFile = FilePathManager.getLogFilePath(context, "Push_location", ".txt");

            if (!pushLocationFile.exists()) {
                LOG.info("Push_location.txt file not found in date folder: {}", pushLocationFile.getAbsolutePath());
                return groups;
            }

            LOG.info("Loading Push_location.txt from date folder: {}", pushLocationFile.getAbsolutePath());
            return parseFileContent(pushLocationFile);

        } catch (Exception e) {
            LOG.error("Error parsing Push_location.txt file from date folder", e);
            return groups;
        }
    }
    
    /**
     * 解析文件内容
     */
    private static List<LocationGroup> parseFileContent(File file) throws IOException {
        List<LocationGroup> groups = new ArrayList<>();
        LocationGroup currentGroup = null;
        
        try (BufferedReader reader = new BufferedReader(new FileReader(file))) {
            String line;
            int lineNumber = 0;
            
            while ((line = reader.readLine()) != null) {
                lineNumber++;
                line = line.trim();
                
                // 跳过空行
                if (line.isEmpty()) {
                    continue;
                }
                
                // 处理分组行
                if (line.startsWith(GROUP_PREFIX)) {
                    String groupName = line.substring(GROUP_PREFIX.length()).trim();
                    if (!groupName.isEmpty()) {
                        currentGroup = new LocationGroup(groupName);
                        groups.add(currentGroup);
                        LOG.debug("Found group: {}", groupName);
                    } else {
                        LOG.warn("Empty group name at line {}", lineNumber);
                    }
                    continue;
                }
                
                // 处理位置点行
                if (currentGroup != null) {
                    LocationPoint point = parseLocationPoint(line, lineNumber);
                    if (point != null) {
                        currentGroup.addLocationPoint(point);
                        LOG.debug("Added point to group {}: {}", currentGroup.getGroupName(), point.getFullLocationString());
                    }
                } else {
                    LOG.warn("Location point found without group at line {}: {}", lineNumber, line);
                }
            }
        }
        
        LOG.info("Parsed {} groups with total {} points", groups.size(), 
                groups.stream().mapToInt(LocationGroup::getPointCount).sum());
        
        return groups;
    }
    
    /**
     * 解析单个位置点
     * 格式：纬度,经度_描述文本
     */
    private static LocationPoint parseLocationPoint(String line, int lineNumber) {
        try {
            // 查找下划线分隔符
            int underscoreIndex = line.indexOf('_');
            if (underscoreIndex == -1) {
                LOG.warn("Invalid location point format at line {} (missing underscore): {}", lineNumber, line);
                return null;
            }
            
            String coordinatesPart = line.substring(0, underscoreIndex).trim();
            String descriptionPart = line.substring(underscoreIndex + 1).trim();
            
            // 解析坐标
            String[] coordinates = coordinatesPart.split(",");
            if (coordinates.length != 2) {
                LOG.warn("Invalid coordinates format at line {} (expected lat,lng): {}", lineNumber, coordinatesPart);
                return null;
            }
            
            double latitude = Double.parseDouble(coordinates[0].trim());
            double longitude = Double.parseDouble(coordinates[1].trim());
            
            // 验证坐标范围
            if (latitude < -90 || latitude > 90) {
                LOG.warn("Invalid latitude at line {}: {}", lineNumber, latitude);
                return null;
            }
            
            if (longitude < -180 || longitude > 180) {
                LOG.warn("Invalid longitude at line {}: {}", lineNumber, longitude);
                return null;
            }
            
            return new LocationPoint(latitude, longitude, descriptionPart);
            
        } catch (NumberFormatException e) {
            LOG.warn("Invalid number format at line {}: {}", lineNumber, line, e);
            return null;
        } catch (Exception e) {
            LOG.warn("Error parsing location point at line {}: {}", lineNumber, line, e);
            return null;
        }
    }
    
    /**
     * 检查文件是否存在
     * 现在检查当天日期文件夹中的文件
     */
    public static boolean fileExists(File logDirectory) {
        try {
            Context context = AppSettings.getInstance().getApplicationContext();
            File pushLocationFile = FilePathManager.getLogFilePath(context, "Push_location", ".txt");
            return pushLocationFile.exists() && pushLocationFile.isFile();
        } catch (Exception e) {
            LOG.error("Error checking if Push_location.txt exists in date folder", e);
            return false;
        }
    }
    
    /**
     * 获取文件路径
     * 现在返回当天日期文件夹中的文件路径
     */
    public static String getFilePath(File logDirectory) {
        try {
            Context context = AppSettings.getInstance().getApplicationContext();
            File pushLocationFile = FilePathManager.getLogFilePath(context, "Push_location", ".txt");
            return pushLocationFile.getAbsolutePath();
        } catch (Exception e) {
            LOG.error("Error getting Push_location.txt file path from date folder", e);
            return null;
        }
    }

    /**
     * 创建示例文件
     * 现在在当天日期文件夹中创建文件
     */
    public static boolean createSampleFile(File logDirectory) {
        try {
            Context context = AppSettings.getInstance().getApplicationContext();
            File sampleFile = FilePathManager.getLogFilePath(context, "Push_location", ".txt");

            // 确保父目录存在
            File parentDir = sampleFile.getParentFile();
            if (parentDir != null && !parentDir.exists()) {
                boolean created = parentDir.mkdirs();
                if (!created) {
                    LOG.error("Failed to create parent directory: {}", parentDir.getAbsolutePath());
                    return false;
                }
            }

            try (java.io.FileWriter writer = new java.io.FileWriter(sampleFile)) {
                writer.write("Group:测试分组1\n");
                writer.write("39.9042,116.4074_北京天安门广场\n");
                writer.write("31.2304,121.4737_上海外滩\n");
                writer.write("22.3193,114.1694_香港维多利亚港\n");
                writer.write("Group:测试分组2\n");
                writer.write("30.2741,120.1551_杭州西湖\n");
                writer.write("36.0611,120.3785_青岛栈桥\n");
                writer.write("29.5647,106.5507_重庆解放碑\n");
                writer.write("Group:长描述测试\n");
                writer.write("25.0330,121.5654_台北101大楼观景台风景优美值得一游\n");
                writer.write("23.1291,113.2644_广州塔小蛮腰夜景非常漂亮推荐晚上去\n");
                LOG.info("Sample Push_location.txt file created in date folder: {}", sampleFile.getAbsolutePath());
                return true;
            }
        } catch (Exception e) {
            LOG.error("Error creating sample file in date folder", e);
            return false;
        }
    }
}
