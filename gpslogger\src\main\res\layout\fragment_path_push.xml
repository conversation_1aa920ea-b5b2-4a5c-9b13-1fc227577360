<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="16dp"
    android:gravity="center">

    <TextView
        android:id="@+id/path_push_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="路径推送"
        android:textSize="24sp"
        android:textStyle="bold"
        android:layout_marginBottom="16dp"
        android:textColor="?android:attr/textColorPrimary" />

    <TextView
        android:id="@+id/path_push_description"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="此功能正在开发中，敬请期待..."
        android:textSize="16sp"
        android:textColor="?android:attr/textColorSecondary"
        android:gravity="center" />

    <!-- 预留空间用于后续功能开发 -->
    <LinearLayout
        android:id="@+id/path_push_content"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:orientation="vertical"
        android:layout_marginTop="32dp">
        
        <!-- 这里将添加路径推送的具体功能界面 -->
        
    </LinearLayout>

</LinearLayout>
