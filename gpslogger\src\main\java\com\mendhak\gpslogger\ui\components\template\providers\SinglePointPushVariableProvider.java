/*
 * Copyright (C) 2016 mendhak
 *
 * This file is part of GPSLogger for Android.
 *
 * GPSLogger for Android is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 2 of the License, or
 * (at your option) any later version.
 *
 * GPSLogger for Android is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with GPSLogger for Android.  If not, see <http://www.gnu.org/licenses/>.
 */

package com.mendhak.gpslogger.ui.components.template.providers;

import android.content.Context;
import android.content.SharedPreferences;
import android.location.Location;
import android.preference.PreferenceManager;
import com.mendhak.gpslogger.common.slf4j.Logs;
import com.mendhak.gpslogger.ui.components.template.VariableProvider;
import com.mendhak.gpslogger.ui.fragments.display.SinglePointPushFragment;
import org.slf4j.Logger;
import java.util.LinkedHashMap;
import java.util.Map;

/**
 * Variable provider for single point push related variables
 */
public class SinglePointPushVariableProvider implements VariableProvider {
    
    private static final Logger LOG = Logs.of(SinglePointPushVariableProvider.class);
    
    @Override
    public String getCategory() {
        return "Single Point Push";
    }
    
    @Override
    public Map<String, String> getAvailableVariables() {
        Map<String, String> variables = new LinkedHashMap<>();
        variables.put("total_push_points", "Total number of push points across all groups (global statistics)");
        variables.put("pushed_points_count", "Number of points that have been pushed across all groups (global statistics)");
        variables.put("pass_count", "Number of points marked as passed across all groups (global statistics)");
        variables.put("fail_count", "Number of points marked as failed across all groups (global statistics)");
        variables.put("push_original_info", "Original push information (coordinates_description)");
        return variables;
    }
    
    @Override
    public String resolveVariable(String variableName, Context context, Location location,
                                String voiceText, String buttonName, int buttonIndex, String groupName) {
        
        if (context == null) {
            return getDefaultValue(variableName);
        }
        
        try {
            switch (variableName) {
                case "total_push_points":
                    return getTotalPushPoints(context, groupName);
                    
                case "pushed_points_count":
                    return getPushedPointsCount(context, groupName);
                    
                case "pass_count":
                    return getPassCount(context, groupName);
                    
                case "fail_count":
                    return getFailCount(context, groupName);
                    
                case "push_original_info":
                    return getPushOriginalInfo(context);
                    
                default:
                    return null;
            }
        } catch (Exception e) {
            LOG.error("Error resolving single point push variable: {}", variableName, e);
            return getDefaultValue(variableName);
        }
    }
    
    @Override
    public boolean supportsVariable(String variableName) {
        return getAvailableVariables().containsKey(variableName);
    }
    
    @Override
    public int getPriority() {
        return 85; // High priority for single point push variables
    }
    
    /**
     * Get total number of push points (always return global statistics)
     */
    private String getTotalPushPoints(Context context, String groupName) {
        SharedPreferences prefs = PreferenceManager.getDefaultSharedPreferences(context);
        // Always return global total points regardless of groupName
        int globalTotal = prefs.getInt("spp_global_total_points", 0);
        return String.valueOf(globalTotal);
    }

    /**
     * Get number of pushed points (always return global statistics)
     */
    private String getPushedPointsCount(Context context, String groupName) {
        SharedPreferences prefs = PreferenceManager.getDefaultSharedPreferences(context);
        // Always return global pushed points regardless of groupName
        int globalPushed = prefs.getInt("spp_global_pushed_points", 0);
        return String.valueOf(globalPushed);
    }

    /**
     * Get number of passed points (always return global statistics)
     */
    private String getPassCount(Context context, String groupName) {
        SharedPreferences prefs = PreferenceManager.getDefaultSharedPreferences(context);
        // Always return global pass count regardless of groupName
        int globalPass = prefs.getInt("spp_global_pass_count", 0);
        return String.valueOf(globalPass);
    }

    /**
     * Get number of failed points (always return global statistics)
     */
    private String getFailCount(Context context, String groupName) {
        SharedPreferences prefs = PreferenceManager.getDefaultSharedPreferences(context);
        // Always return global fail count regardless of groupName
        int globalFail = prefs.getInt("spp_global_fail_count", 0);
        return String.valueOf(globalFail);
    }
    
    /**
     * Get push original information
     */
    private String getPushOriginalInfo(Context context) {
        SharedPreferences prefs = PreferenceManager.getDefaultSharedPreferences(context);
        String originalInfo = prefs.getString("spp_current_push_original_info", "");
        return originalInfo.isEmpty() ? "N/A" : originalInfo;
    }
    
    /**
     * Get default value for unknown variables
     */
    private String getDefaultValue(String variableName) {
        switch (variableName) {
            case "total_push_points":
            case "pushed_points_count":
            case "pass_count":
            case "fail_count":
                return "0";
            case "push_original_info":
                return "N/A";
            default:
                return "";
        }
    }
    
    /**
     * Update statistics for a specific group
     */
    public static void updateGroupStatistics(Context context, String groupName, 
                                           int totalPoints, int pushedPoints, int passCount, int failCount) {
        if (context == null || groupName == null) {
            return;
        }
        
        SharedPreferences prefs = PreferenceManager.getDefaultSharedPreferences(context);
        SharedPreferences.Editor editor = prefs.edit();
        
        editor.putInt("spp_total_points_" + groupName, totalPoints);
        editor.putInt("spp_pushed_points_" + groupName, pushedPoints);
        editor.putInt("spp_pass_count_" + groupName, passCount);
        editor.putInt("spp_fail_count_" + groupName, failCount);
        
        editor.apply();
        
        LOG.debug("Updated group statistics for {}: total={}, pushed={}, pass={}, fail={}", 
                 groupName, totalPoints, pushedPoints, passCount, failCount);
    }
    
    /**
     * Update global statistics
     */
    public static void updateGlobalStatistics(Context context, int totalPoints, int pushedPoints, 
                                            int passCount, int failCount) {
        if (context == null) {
            return;
        }
        
        SharedPreferences prefs = PreferenceManager.getDefaultSharedPreferences(context);
        SharedPreferences.Editor editor = prefs.edit();
        
        editor.putInt("spp_global_total_points", totalPoints);
        editor.putInt("spp_global_pushed_points", pushedPoints);
        editor.putInt("spp_global_pass_count", passCount);
        editor.putInt("spp_global_fail_count", failCount);
        
        editor.apply();
        
        LOG.debug("Updated global statistics: total={}, pushed={}, pass={}, fail={}", 
                 totalPoints, pushedPoints, passCount, failCount);
    }
    
    /**
     * Set current push original information
     */
    public static void setCurrentPushOriginalInfo(Context context, String originalInfo) {
        if (context == null) {
            return;
        }
        
        SharedPreferences prefs = PreferenceManager.getDefaultSharedPreferences(context);
        SharedPreferences.Editor editor = prefs.edit();
        editor.putString("spp_current_push_original_info", originalInfo != null ? originalInfo : "");
        editor.apply();
        
        LOG.debug("Set current push original info: {}", originalInfo);
    }
}
