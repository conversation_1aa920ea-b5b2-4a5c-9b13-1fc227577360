<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <!-- Status Bar -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="#E3F2FD"
        android:orientation="horizontal"
        android:padding="8dp"
        android:gravity="center_vertical">

        <!-- Statistics section -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="horizontal"
            android:gravity="center_vertical">

            <TextView
                android:id="@+id/push_progress_text"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="推送进度：0/0"
                android:textColor="#1976D2"
                android:textSize="11sp"
                android:layout_marginEnd="12dp"
                android:gravity="center_vertical" />

            <TextView
                android:id="@+id/pass_count_text"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="通过：0"
                android:textColor="#4CAF50"
                android:textSize="11sp"
                android:layout_marginEnd="8dp"
                android:gravity="center_vertical" />

            <TextView
                android:id="@+id/fail_count_text"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="不通过：0"
                android:textColor="#F44336"
                android:textSize="11sp"
                android:layout_marginEnd="8dp"
                android:gravity="center_vertical" />

            <TextView
                android:id="@+id/group_count_text"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="分组：0"
                android:textColor="#1976D2"
                android:textSize="11sp"
                android:gravity="center_vertical" />

        </LinearLayout>

        <!-- Refresh button -->
        <Button
            android:id="@+id/refresh_button"
            android:layout_width="wrap_content"
            android:layout_height="28dp"
            android:background="@drawable/button_background"
            android:minWidth="50dp"
            android:minHeight="28dp"
            android:paddingHorizontal="6dp"
            android:text="刷新"
            android:textColor="@android:color/white"
            android:textSize="10sp" />

    </LinearLayout>



    <!-- Content Area -->
    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1">

        <!-- RecyclerView for location groups -->
        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/location_groups_recycler"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:padding="4dp"
            android:clipToPadding="false"
            android:scrollbars="vertical" />

        <!-- Empty state view -->
        <LinearLayout
            android:id="@+id/empty_state_layout"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:gravity="center"
            android:orientation="vertical"
            android:visibility="gone">

            <ImageView
                android:layout_width="64dp"
                android:layout_height="64dp"
                android:layout_marginBottom="16dp"
                android:alpha="0.5"
                android:src="@drawable/ic_location_off" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="8dp"
                android:text="未找到Push_location.txt文件"
                android:textColor="@android:color/darker_gray"
                android:textSize="16sp"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/empty_state_description"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:text="请在GPSLogger日志文件夹中创建Push_location.txt文件"
                android:textColor="@android:color/darker_gray"
                android:textSize="14sp" />

            <Button
                android:id="@+id/create_sample_file_button"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:background="@drawable/button_background"
                android:paddingHorizontal="24dp"
                android:text="创建示例文件"
                android:textColor="@android:color/white" />

        </LinearLayout>

        <!-- Loading view -->
        <LinearLayout
            android:id="@+id/loading_layout"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:gravity="center"
            android:orientation="vertical"
            android:visibility="gone">

            <ProgressBar
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="正在加载位置数据..."
                android:textColor="@android:color/darker_gray"
                android:textSize="14sp" />

        </LinearLayout>

    </FrameLayout>

</LinearLayout>
