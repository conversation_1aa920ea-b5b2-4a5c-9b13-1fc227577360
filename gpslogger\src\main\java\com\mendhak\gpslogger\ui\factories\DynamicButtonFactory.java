/*
 * Copyright (C) 2024 mendhak
 *
 * This file is part of GPSLogger for Android.
 *
 * GPSLogger is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 2 of the License, or
 * (at your option) any later version.
 *
 * GPSLogger is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with GPSLogger.  If not, see <http://www.gnu.org/licenses/>.
 */

package com.mendhak.gpslogger.ui.factories;

import android.content.Context;
import android.graphics.drawable.GradientDrawable;
import android.speech.tts.TextToSpeech;
import android.util.Log;
import android.view.View;
import android.widget.Button;
import android.widget.ImageButton;
import android.widget.TextView;

import com.mendhak.gpslogger.R;
import com.mendhak.gpslogger.ui.models.ButtonConfig;

import java.util.Locale;

/**
 * 动态按钮工厂
 * 根据按钮配置创建不同类型的按钮
 */
public class DynamicButtonFactory {
    
    private static final String TAG = "DynamicButtonFactory";
    private static TextToSpeech textToSpeech;
    private static boolean ttsInitialized = false;
    
    /**
     * 按钮点击监听器接口
     */
    public interface OnButtonClickListener {
        void onPassClick();
        void onFailClick();
        void onCounterClick(int count);
    }
    
    /**
     * 初始化文本转语音
     */
    public static void initTextToSpeech(Context context) {
        if (textToSpeech == null) {
            textToSpeech = new TextToSpeech(context, status -> {
                if (status == TextToSpeech.SUCCESS) {
                    int result = textToSpeech.setLanguage(Locale.CHINESE);
                    if (result == TextToSpeech.LANG_MISSING_DATA || result == TextToSpeech.LANG_NOT_SUPPORTED) {
                        Log.w(TAG, "中文语音不支持，使用默认语言");
                        textToSpeech.setLanguage(Locale.getDefault());
                    }
                    ttsInitialized = true;
                    Log.d(TAG, "文本转语音初始化成功");
                } else {
                    Log.e(TAG, "文本转语音初始化失败");
                }
            });
        }
    }
    
    /**
     * 释放文本转语音资源
     */
    public static void releaseTTS() {
        if (textToSpeech != null) {
            textToSpeech.stop();
            textToSpeech.shutdown();
            textToSpeech = null;
            ttsInitialized = false;
        }
    }
    
    /**
     * 创建通过按钮
     */
    public static View createPassButton(Context context, ButtonConfig config, OnButtonClickListener listener) {
        // 无论什么模式，都显示"通过"文字，但根据配置执行不同的功能
        return createTextButton(context, "通过", config.getPassColor(), listener::onPassClick);
    }
    
    /**
     * 创建不通过按钮
     */
    public static View createFailButton(Context context, ButtonConfig config, OnButtonClickListener listener) {
        // 无论什么模式，都显示"不过"文字，但根据配置执行不同的功能
        return createTextButton(context, "不过", config.getFailColor(), listener::onFailClick);
    }
    
    /**
     * 创建文本按钮
     */
    private static Button createTextButton(Context context, String text, int color, Runnable clickListener) {
        Button button = new Button(context);
        button.setText(text);
        button.setTextColor(color);

        // 设置按钮大小和样式 - 根据文本内容调整宽度
        int buttonWidth;
        int buttonHeight;
        int textSize;
        if ("通过".equals(text) || "不过".equals(text)) {
            // 通过和不过按钮使用更大的宽度和高度
            buttonWidth = (int) (46 * context.getResources().getDisplayMetrics().density); // 46dp width
            buttonHeight = (int) (33 * context.getResources().getDisplayMetrics().density); // 33dp height
            textSize = 8; // 8sp
        } else {
            // 其他按钮（如推送）使用稍大的尺寸
            buttonWidth = (int) (44 * context.getResources().getDisplayMetrics().density); // 44dp width
            buttonHeight = (int) (33 * context.getResources().getDisplayMetrics().density); // 33dp height
            textSize = 9; // 9sp
        }

        android.widget.LinearLayout.LayoutParams params = new android.widget.LinearLayout.LayoutParams(
            buttonWidth,
            buttonHeight
        );
        params.setMargins((int) (2 * context.getResources().getDisplayMetrics().density), 0, 0, 0);
        button.setLayoutParams(params);

        // 设置文本大小
        button.setTextSize(android.util.TypedValue.COMPLEX_UNIT_SP, textSize);

        // 设置按钮样式
        GradientDrawable drawable = new GradientDrawable();
        drawable.setShape(GradientDrawable.RECTANGLE);
        drawable.setCornerRadius(8);
        drawable.setStroke(2, color);
        drawable.setColor(0x20000000 | (color & 0x00FFFFFF)); // 半透明背景
        button.setBackground(drawable);

        // 设置最小宽高为0，避免系统默认最小尺寸限制
        button.setMinWidth(0);
        button.setMinHeight(0);

        button.setOnClickListener(v -> clickListener.run());

        return button;
    }
    
    /**
     * 创建语音按钮
     */
    private static ImageButton createVoiceButton(Context context, String voiceText, int color, Runnable clickListener) {
        ImageButton button = new ImageButton(context);
        button.setImageResource(R.drawable.ic_mic_24dp);
        button.setColorFilter(color);
        
        // 设置按钮样式
        GradientDrawable drawable = new GradientDrawable();
        drawable.setShape(GradientDrawable.RECTANGLE);
        drawable.setCornerRadius(8);
        drawable.setStroke(2, color);
        drawable.setColor(0x20000000 | (color & 0x00FFFFFF)); // 半透明背景
        button.setBackground(drawable);
        
        button.setOnClickListener(v -> {
            // 播放语音
            if (ttsInitialized && textToSpeech != null) {
                textToSpeech.speak(voiceText, TextToSpeech.QUEUE_FLUSH, null, null);
            }
            clickListener.run();
        });
        
        return button;
    }
    
    /**
     * 创建计数器按钮
     */
    private static TextView createCounterButton(Context context, int maxCount, int color, CounterClickListener listener) {
        TextView button = new TextView(context);
        button.setText("0");
        button.setTextColor(color);
        button.setTextSize(16);
        button.setPadding(16, 8, 16, 8);
        button.setGravity(android.view.Gravity.CENTER);
        
        // 设置按钮样式
        GradientDrawable drawable = new GradientDrawable();
        drawable.setShape(GradientDrawable.RECTANGLE);
        drawable.setCornerRadius(8);
        drawable.setStroke(2, color);
        drawable.setColor(0x20000000 | (color & 0x00FFFFFF)); // 半透明背景
        button.setBackground(drawable);
        
        // 计数器状态
        final int[] count = {0};
        
        button.setOnClickListener(v -> {
            count[0] = (count[0] + 1) % (maxCount + 1);
            button.setText(String.valueOf(count[0]));
            listener.onCounterClick(count[0]);
        });
        
        // 长按重置
        button.setOnLongClickListener(v -> {
            count[0] = 0;
            button.setText("0");
            listener.onCounterClick(0);
            return true;
        });
        
        return button;
    }
    
    /**
     * 计数器点击监听器
     */
    private interface CounterClickListener {
        void onCounterClick(int count);
    }
    
    /**
     * 更新按钮状态
     */
    public static void updateButtonState(View button, boolean isActive, int activeColor, int inactiveColor) {
        if (button instanceof Button) {
            Button btn = (Button) button;
            btn.setTextColor(isActive ? activeColor : inactiveColor);
        } else if (button instanceof ImageButton) {
            ImageButton btn = (ImageButton) button;
            btn.setColorFilter(isActive ? activeColor : inactiveColor);
        } else if (button instanceof TextView) {
            TextView btn = (TextView) button;
            btn.setTextColor(isActive ? activeColor : inactiveColor);
        }
        
        // 更新背景
        if (button.getBackground() instanceof GradientDrawable) {
            GradientDrawable drawable = (GradientDrawable) button.getBackground();
            drawable.setStroke(2, isActive ? activeColor : inactiveColor);
            drawable.setColor(0x20000000 | ((isActive ? activeColor : inactiveColor) & 0x00FFFFFF));
        }
    }
}
