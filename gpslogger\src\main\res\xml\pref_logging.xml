<?xml version="1.0" encoding="utf-8"?>
<PreferenceScreen xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:summary="@string/pref_logging_summary"
    android:title="@string/pref_logging_title">

    <SwitchPreferenceCompat
        android:defaultValue="true"
        android:key="log_gpx"
        android:summary="@string/log_gpx_summary"
        android:title="@string/log_gpx_title"
        app:iconSpaceReserved="false" />

    <SwitchPreferenceCompat
        android:defaultValue="false"
        android:key="log_gpx_11"
        android:summary="@string/log_gpx_11_summary"
        android:title="@string/log_gpx_11_title"
        app:iconSpaceReserved="false" />

    <SwitchPreferenceCompat
        android:key="log_kml"
        android:summary="@string/log_kml_summary"
        android:title="@string/log_kml_title"
        app:iconSpaceReserved="false" />

    <com.mendhak.gpslogger.ui.components.SwitchPlusClickPreference
        android:key="log_customurl_enabled"
        android:summary="@string/log_customurl_summary"
        android:title="@string/log_customurl_title"
        app:iconSpaceReserved="false" />

    <SwitchPreferenceCompat
        android:key="log_opengts"
        android:summary="@string/log_opengts_summary"
        android:title="@string/log_opengts_title"
        app:iconSpaceReserved="false" />

    <SwitchPreferenceCompat
        android:key="log_plain_text"
        android:summary="@string/log_plain_text_summary"
        android:title="@string/log_plain_text_title"
        app:iconSpaceReserved="false" />

    <SwitchPreferenceCompat
        android:key="log_geojson"
        android:summary="@string/log_json_summary"
        android:title="@string/log_json_title"
        app:iconSpaceReserved="false" />

    <SwitchPreferenceCompat
        android:key="log_nmea"
        android:summary="@string/log_nmea_summary"
        android:title="@string/log_nmea_title"
        app:iconSpaceReserved="false" />

    <SwitchPreferenceCompat
        android:key="log_txt_annotations"
        android:defaultValue="false"
        android:summary="@string/log_txt_annotations_summary"
        android:title="@string/log_txt_annotations_title"
        app:iconSpaceReserved="false" />

    <SwitchPreferenceCompat
        android:key="annotation_sync_write"
        android:defaultValue="false"
        android:summary="@string/annotation_sync_write_summary"
        android:title="@string/annotation_sync_write_title"
        app:iconSpaceReserved="false" />

    <PreferenceCategory
        android:title="@string/pref_filedetails_title"
        app:iconSpaceReserved="false">

        <Preference
            android:key="gpslogger_folder"
            android:summary="@string/gpslogger_folder_summary"
            android:title="@string/gpslogger_folder_title"
            app:iconSpaceReserved="false" />

        <Preference
            android:defaultValue="onceaday"
            android:entries="@array/filecreation_entries"
            android:entryValues="@array/filecreation_values"
            android:key="new_file_creation"
            android:summary="@string/new_file_creation_summary"
            android:title="@string/new_file_creation_title"
            app:iconSpaceReserved="false" />

        <Preference
            android:defaultValue="gpslogger"
            android:dialogMessage="@string/new_file_custom_message"
            android:dialogTitle="@string/new_file_custom_title"
            android:key="new_file_custom_name"
            android:summary="@string/new_file_custom_summary"
            android:title="@string/new_file_custom_title"
            app:iconSpaceReserved="false" />

        <SwitchPreferenceCompat
            android:defaultValue="true"
            android:key="new_file_custom_keep_changing"
            android:summary="@string/new_file_custom_keep_changing_summary"
            android:title="@string/new_file_custom_keep_changing_title"
            app:iconSpaceReserved="false" />

        <SwitchPreferenceCompat
            android:defaultValue="true"
            android:key="new_file_custom_each_time"
            android:summary="@string/new_file_custom_each_time_summary"
            android:title="@string/new_file_custom_each_time_title"
            app:iconSpaceReserved="false" />

        <SwitchPreferenceCompat
            android:defaultValue="false"
            android:key="new_file_prefix_serial"
            android:summary="@string/new_file_prefix_serial_summary"
            android:title="@string/new_file_prefix_serial_title"
            app:iconSpaceReserved="false" />

        <!-- Date Folder Settings - 日期文件夹功能已强制启用 -->
        <Preference
            android:defaultValue="{year}/{month}/{day}"
            android:dialogMessage="@string/folder_path_template_message"
            android:dialogTitle="@string/folder_path_template_title"
            android:key="folder_path_template"
            android:summary="@string/folder_path_template_summary"
            android:title="@string/folder_path_template_title"
            app:iconSpaceReserved="false" />

        <!-- Annotation Template Settings -->
        <SwitchPreferenceCompat
            android:key="annotation_template_enabled"
            android:title="启用Annotation模板"
            android:summary="使用自定义模板格式化annotation内容"
            android:defaultValue="false"
            app:iconSpaceReserved="false" />

        <Preference
            android:key="annotation_template_editor"
            android:title="编辑Annotation模板"
            android:summary="配置annotation输出格式模板"
            android:dependency="annotation_template_enabled"
            app:iconSpaceReserved="false" />

        <!-- Single Point Push Template Settings -->
        <SwitchPreferenceCompat
            android:key="single_point_push_template_enabled"
            android:title="启用单点推送模板"
            android:summary="使用自定义模板格式化单点推送结果记录"
            android:defaultValue="true"
            app:iconSpaceReserved="false" />

        <Preference
            android:key="single_point_push_template_editor"
            android:title="单点推送模板编辑器"
            android:summary="配置单点推送结果记录格式模板"
            android:dependency="single_point_push_template_enabled"
            app:iconSpaceReserved="false" />

    </PreferenceCategory>

    <PreferenceCategory android:title="@string/autoftp_advanced_settings" app:iconSpaceReserved="false">
        <SwitchPreferenceCompat
            android:defaultValue="false"
            android:key="file_logging_write_time_with_offset"
            android:summary="@string/file_logging_log_time_with_offset_summary"
            android:title="@string/file_logging_log_time_with_offset_title"
            app:iconSpaceReserved="false"/>

        <Preference
            android:title="@string/log_plain_text_csv_advanced_title"
            android:key="log_plain_text_csv_advanced"
            android:defaultValue=","
            app:iconSpaceReserved="false" />

        <Preference
            android:title="@string/logging_advanced_delete_files"
            android:summary="@string/logging_advanced_delete_files_summary"
            android:key="delete_files"
            app:iconSpaceReserved="false" />

    </PreferenceCategory>

</PreferenceScreen>
