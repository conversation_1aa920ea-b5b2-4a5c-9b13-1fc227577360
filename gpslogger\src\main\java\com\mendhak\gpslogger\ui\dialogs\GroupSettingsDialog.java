/*
 * Copyright (C) 2024 mendhak
 *
 * This file is part of GPSLogger for Android.
 *
 * GPSLogger is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 2 of the License, or
 * (at your option) any later version.
 *
 * GPSLogger is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with GPSLogger.  If not, see <http://www.gnu.org/licenses/>.
 */

package com.mendhak.gpslogger.ui.dialogs;

import android.app.AlertDialog;
import android.app.Dialog;
import android.content.Context;
import android.os.Bundle;
import android.text.TextUtils;
import android.widget.Button;
import android.widget.EditText;
import android.widget.ImageButton;
import android.widget.ImageView;
import android.widget.RadioGroup;
import android.widget.RadioButton;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;

import com.mendhak.gpslogger.R;
import com.mendhak.gpslogger.ui.managers.GroupIconManager;
import com.mendhak.gpslogger.ui.models.GroupIcon;
import com.mendhak.gpslogger.ui.models.LocationGroup;

/**
 * 分组设置对话框
 */
public class GroupSettingsDialog extends Dialog {
    
    public interface OnGroupSettingsListener {
        void onGroupRenamed(LocationGroup group, String newName);
        void onGroupIconChanged(LocationGroup group, String newIconId);
        void onGroupDeleted(LocationGroup group);
    }
    
    private EditText groupNameEdit;
    private ImageView currentIconView;
    private Button selectIconButton;
    private TextView totalPointsText;
    private TextView pushedPointsText;
    private TextView passedPointsText;
    private TextView failedPointsText;
    private Button resetDataButton;
    private Button deleteGroupButton;
    private Button cancelButton;
    private Button confirmButton;
    private ImageButton quickResetButton;

    // 新增：按钮模式配置相关控件
    private TextView buttonModeLabel;
    private RadioGroup passButtonModeGroup;
    private RadioButton radioPassTextMode;
    private RadioButton radioPassVoiceMode;
    private RadioButton radioPassCounterMode;
    private RadioGroup failButtonModeGroup;
    private RadioButton radioFailTextMode;
    private RadioButton radioFailVoiceMode;
    private RadioButton radioFailCounterMode;
    private Button configureButtonsButton;
    private Button resetButtonsButton;

    private LocationGroup locationGroup;
    private OnGroupSettingsListener listener;
    private GroupIcon selectedIcon;
    private com.mendhak.gpslogger.ui.adapters.LocationGroupAdapter groupAdapter; // 用于获取统计数据

    public GroupSettingsDialog(@NonNull Context context, LocationGroup locationGroup,
                              com.mendhak.gpslogger.ui.adapters.LocationGroupAdapter groupAdapter) {
        super(context);
        this.locationGroup = locationGroup;
        this.groupAdapter = groupAdapter;
        this.selectedIcon = GroupIconManager.getIconById(locationGroup.getIconId());
    }
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.dialog_group_settings);
        
        initViews();
        setupData();
        setupButtons();
    }
    
    private void initViews() {
        groupNameEdit = findViewById(R.id.group_name_edit);
        currentIconView = findViewById(R.id.current_icon_view);
        selectIconButton = findViewById(R.id.select_icon_button);
        totalPointsText = findViewById(R.id.total_points_text);
        pushedPointsText = findViewById(R.id.pushed_points_text);
        passedPointsText = findViewById(R.id.passed_points_text);
        failedPointsText = findViewById(R.id.failed_points_text);
        resetDataButton = findViewById(R.id.reset_data_button);
        deleteGroupButton = findViewById(R.id.delete_group_button);
        cancelButton = findViewById(R.id.cancel_button);
        confirmButton = findViewById(R.id.confirm_button);
        quickResetButton = findViewById(R.id.quick_reset_button);

        // 新增：按钮模式配置控件初始化
        buttonModeLabel = findViewById(R.id.button_mode_label);
        passButtonModeGroup = findViewById(R.id.pass_button_mode_group);
        radioPassTextMode = findViewById(R.id.radio_pass_text_mode);
        radioPassVoiceMode = findViewById(R.id.radio_pass_voice_mode);
        radioPassCounterMode = findViewById(R.id.radio_pass_counter_mode);
        failButtonModeGroup = findViewById(R.id.fail_button_mode_group);
        radioFailTextMode = findViewById(R.id.radio_fail_text_mode);
        radioFailVoiceMode = findViewById(R.id.radio_fail_voice_mode);
        radioFailCounterMode = findViewById(R.id.radio_fail_counter_mode);
        configureButtonsButton = findViewById(R.id.configure_buttons_button);
        resetButtonsButton = findViewById(R.id.reset_buttons_button);
    }
    
    private void setupData() {
        // 设置分组名称
        groupNameEdit.setText(locationGroup.getGroupName());

        // 设置当前图标
        updateIconDisplay();

        // 设置统计信息
        updateStatistics();

        // 设置按钮模式配置
        setupButtonModeConfiguration();
    }
    
    private void updateIconDisplay() {
        if (selectedIcon != null) {
            currentIconView.setImageResource(selectedIcon.getIconResource());
        }
    }
    
    private void updateStatistics() {
        if (groupAdapter != null) {
            // 从适配器获取真实的统计数据
            com.mendhak.gpslogger.ui.adapters.LocationGroupAdapter.GroupStatistics stats =
                groupAdapter.getGroupStatistics(locationGroup.getGroupName());

            totalPointsText.setText(String.valueOf(stats.totalPoints));
            pushedPointsText.setText(String.valueOf(stats.pushedCount));
            passedPointsText.setText(String.valueOf(stats.passedCount));
            failedPointsText.setText(String.valueOf(stats.failedCount));
        } else {
            // 如果没有适配器，只显示总数
            int totalPoints = locationGroup.getLocationPoints().size();
            totalPointsText.setText(String.valueOf(totalPoints));
            pushedPointsText.setText("0");
            passedPointsText.setText("0");
            failedPointsText.setText("0");
        }
    }
    
    private void setupButtons() {
        // 选择图标按钮
        selectIconButton.setOnClickListener(v -> showIconSelector());

        // 重置数据按钮
        resetDataButton.setOnClickListener(v -> showResetDataConfirmation());

        // 删除分组按钮
        deleteGroupButton.setOnClickListener(v -> showDeleteConfirmation());

        // 取消按钮
        cancelButton.setOnClickListener(v -> dismiss());

        // 确定按钮
        confirmButton.setOnClickListener(v -> saveChanges());

        // 配置按钮详细设置按钮
        configureButtonsButton.setOnClickListener(v -> showButtonConfigurationDialog());

        // 重置按钮配置按钮
        resetButtonsButton.setOnClickListener(v -> resetButtonConfiguration());

        // 快速重置按钮
        quickResetButton.setOnClickListener(v -> showQuickResetConfirmation());
    }
    
    private void showIconSelector() {
        IconSelectorDialog iconDialog = new IconSelectorDialog(getContext());
        iconDialog.setSelectedIcon(selectedIcon);
        iconDialog.setOnIconSelectedListener(icon -> {
            selectedIcon = icon;
            updateIconDisplay();
        });
        iconDialog.show();
    }

    private void showResetDataConfirmation() {
        new AlertDialog.Builder(getContext())
                .setTitle("重置持久化数据")
                .setMessage("确定要重置分组 \"" + locationGroup.getGroupName() + "\" 的所有状态数据吗？\n\n" +
                           "此操作将清除：\n" +
                           "• 所有位置点的推送状态\n" +
                           "• 所有位置点的通过/不通过状态\n" +
                           "• 顺序推送进度\n\n" +
                           "此操作无法恢复。")
                .setPositiveButton("重置", (dialog, which) -> {
                    resetGroupData();
                    dismiss();
                })
                .setNegativeButton("取消", null)
                .show();
    }

    private void resetGroupData() {
        if (groupAdapter != null) {
            // 清除适配器中的状态
            groupAdapter.clearGroupState(locationGroup.getGroupName());

            // 显示成功消息
            Toast.makeText(getContext(), "分组数据已重置", Toast.LENGTH_SHORT).show();
        }
    }

    private void showQuickResetConfirmation() {
        new AlertDialog.Builder(getContext())
                .setTitle("快速重置")
                .setMessage("确定要重置分组 \"" + locationGroup.getGroupName() + "\" 的持久化数据吗？\n\n" +
                           "此操作将清除所有推送状态和通过/不通过状态。")
                .setPositiveButton("重置", (dialog, which) -> {
                    resetGroupData();
                    updateStatistics(); // 刷新统计信息显示
                })
                .setNegativeButton("取消", null)
                .show();
    }

    private void showDeleteConfirmation() {
        new AlertDialog.Builder(getContext())
                .setTitle("删除分组")
                .setMessage("确定要删除分组 \"" + locationGroup.getGroupName() + "\" 吗？\n\n" +
                           "此操作将删除分组及其所有位置点数据，且无法恢复。")
                .setPositiveButton("删除", (dialog, which) -> {
                    if (listener != null) {
                        listener.onGroupDeleted(locationGroup);
                    }
                    dismiss();
                })
                .setNegativeButton("取消", null)
                .show();
    }
    
    private void saveChanges() {
        String newName = groupNameEdit.getText().toString().trim();
        
        // 验证分组名称
        if (TextUtils.isEmpty(newName)) {
            Toast.makeText(getContext(), "分组名称不能为空", Toast.LENGTH_SHORT).show();
            return;
        }
        
        boolean hasChanges = false;
        
        // 检查名称是否有变化
        if (!newName.equals(locationGroup.getGroupName())) {
            if (listener != null) {
                listener.onGroupRenamed(locationGroup, newName);
            }
            hasChanges = true;
        }
        
        // 检查图标是否有变化
        String newIconId = selectedIcon != null ? selectedIcon.getIconId() : GroupIconManager.getDefaultIconId();
        if (!newIconId.equals(locationGroup.getIconId())) {
            if (listener != null) {
                listener.onGroupIconChanged(locationGroup, newIconId);
            }
            hasChanges = true;
        }
        
        if (hasChanges) {
            Toast.makeText(getContext(), "分组设置已保存", Toast.LENGTH_SHORT).show();
        }
        
        dismiss();
    }
    
    /**
     * 设置分组设置监听器
     */
    public void setOnGroupSettingsListener(OnGroupSettingsListener listener) {
        this.listener = listener;
    }
    
    /**
     * 更新统计信息
     */
    public void updateStatistics(int pushedCount, int passedCount, int failedCount) {
        if (pushedPointsText != null) {
            pushedPointsText.setText(String.valueOf(pushedCount));
        }
        if (passedPointsText != null) {
            passedPointsText.setText(String.valueOf(passedCount));
        }
        if (failedPointsText != null) {
            failedPointsText.setText(String.valueOf(failedCount));
        }
    }

    /**
     * 设置按钮模式配置
     */
    private void setupButtonModeConfiguration() {
        // 获取当前按钮配置管理器
        com.mendhak.gpslogger.ui.managers.ButtonConfigManager configManager =
            com.mendhak.gpslogger.ui.managers.ButtonConfigManager.getInstance(getContext());

        // 设置通过按钮模式
        com.mendhak.gpslogger.ui.models.ButtonConfig passConfig = configManager.getPassButtonConfig();
        switch (passConfig.getType()) {
            case TEXT:
                radioPassTextMode.setChecked(true);
                break;
            case VOICE:
                radioPassVoiceMode.setChecked(true);
                break;
            case COUNTER:
                radioPassCounterMode.setChecked(true);
                break;
            default:
                radioPassTextMode.setChecked(true);
                break;
        }

        // 设置不过按钮模式
        com.mendhak.gpslogger.ui.models.ButtonConfig failConfig = configManager.getFailButtonConfig();
        switch (failConfig.getType()) {
            case TEXT:
                radioFailTextMode.setChecked(true);
                break;
            case VOICE:
                radioFailVoiceMode.setChecked(true);
                break;
            case COUNTER:
                radioFailCounterMode.setChecked(true);
                break;
            default:
                radioFailTextMode.setChecked(true);
                break;
        }

        // 设置按钮模式切换监听器
        passButtonModeGroup.setOnCheckedChangeListener((group, checkedId) -> {
            applyPassButtonModeChange(checkedId);
        });

        failButtonModeGroup.setOnCheckedChangeListener((group, checkedId) -> {
            applyFailButtonModeChange(checkedId);
        });
    }

    /**
     * 应用通过按钮模式变更
     */
    private void applyPassButtonModeChange(int checkedId) {
        com.mendhak.gpslogger.ui.managers.ButtonConfigManager configManager =
            com.mendhak.gpslogger.ui.managers.ButtonConfigManager.getInstance(getContext());

        com.mendhak.gpslogger.ui.models.ButtonConfig newConfig;
        String modeText;

        if (checkedId == radioPassTextMode.getId()) {
            newConfig = com.mendhak.gpslogger.ui.models.ButtonConfig.createDefaultTextConfig();
            modeText = "文本模式";
        } else if (checkedId == radioPassVoiceMode.getId()) {
            newConfig = com.mendhak.gpslogger.ui.models.ButtonConfig.createDefaultVoiceConfig();
            modeText = "语音模式";
        } else if (checkedId == radioPassCounterMode.getId()) {
            newConfig = com.mendhak.gpslogger.ui.models.ButtonConfig.createDefaultCounterConfig();
            modeText = "计数器模式";
        } else {
            return;
        }

        configManager.setPassButtonConfig(newConfig);
        android.widget.Toast.makeText(getContext(), "通过按钮已切换到" + modeText, android.widget.Toast.LENGTH_SHORT).show();
    }

    /**
     * 应用不过按钮模式变更
     */
    private void applyFailButtonModeChange(int checkedId) {
        com.mendhak.gpslogger.ui.managers.ButtonConfigManager configManager =
            com.mendhak.gpslogger.ui.managers.ButtonConfigManager.getInstance(getContext());

        com.mendhak.gpslogger.ui.models.ButtonConfig newConfig;
        String modeText;

        if (checkedId == radioFailTextMode.getId()) {
            newConfig = com.mendhak.gpslogger.ui.models.ButtonConfig.createDefaultTextConfig();
            modeText = "文本模式";
        } else if (checkedId == radioFailVoiceMode.getId()) {
            newConfig = com.mendhak.gpslogger.ui.models.ButtonConfig.createDefaultVoiceConfig();
            modeText = "语音模式";
        } else if (checkedId == radioFailCounterMode.getId()) {
            newConfig = com.mendhak.gpslogger.ui.models.ButtonConfig.createDefaultCounterConfig();
            modeText = "计数器模式";
        } else {
            return;
        }

        configManager.setFailButtonConfig(newConfig);
        android.widget.Toast.makeText(getContext(), "不过按钮已切换到" + modeText, android.widget.Toast.LENGTH_SHORT).show();
    }

    /**
     * 重置按钮配置
     */
    private void resetButtonConfiguration() {
        com.mendhak.gpslogger.ui.managers.ButtonConfigManager configManager =
            com.mendhak.gpslogger.ui.managers.ButtonConfigManager.getInstance(getContext());

        configManager.resetToDefault();
        setupButtonModeConfiguration(); // 刷新界面
        android.widget.Toast.makeText(getContext(), "已重置为默认配置", android.widget.Toast.LENGTH_SHORT).show();
    }

    /**
     * 显示按钮详细配置对话框
     */
    private void showButtonConfigurationDialog() {
        com.mendhak.gpslogger.ui.managers.ButtonConfigManager configManager =
            com.mendhak.gpslogger.ui.managers.ButtonConfigManager.getInstance(getContext());

        // 创建详细配置对话框
        android.app.AlertDialog.Builder builder = new android.app.AlertDialog.Builder(getContext());
        builder.setTitle("按钮详细配置");

        // 创建配置界面
        android.view.View configView = android.view.LayoutInflater.from(getContext())
            .inflate(android.R.layout.simple_list_item_2, null);

        // 显示当前配置信息
        String configInfo = "当前配置:\n";
        if (configManager.isTextMode()) {
            configInfo += "模式: 文本模式\n";
            configInfo += "通过按钮: " + configManager.getPassButtonConfig().getPassText() + "\n";
            configInfo += "不过按钮: " + configManager.getFailButtonConfig().getFailText();
        } else if (configManager.isVoiceMode()) {
            configInfo += "模式: 语音模式\n";
            configInfo += "通过语音: " + configManager.getPassButtonConfig().getPassVoiceText() + "\n";
            configInfo += "不过语音: " + configManager.getFailButtonConfig().getFailVoiceText();
        } else if (configManager.isCounterMode()) {
            configInfo += "模式: 计数器模式\n";
            configInfo += "最大计数: " + configManager.getPassButtonConfig().getMaxCount();
        }

        builder.setMessage(configInfo);
        builder.setPositiveButton("确定", null);
        builder.setNeutralButton("重置为默认", (dialog, which) -> {
            configManager.resetToDefault();
            setupButtonModeConfiguration(); // 刷新界面
            android.widget.Toast.makeText(getContext(), "已重置为默认配置", android.widget.Toast.LENGTH_SHORT).show();
        });

        builder.show();
    }
}
