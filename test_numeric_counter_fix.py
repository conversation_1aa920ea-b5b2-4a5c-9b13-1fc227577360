#!/usr/bin/env python3
"""
数值计数器修复功能测试脚本
用于自动化测试重置按钮和初始值设置功能
"""

import subprocess
import time
import sys

ADB_PATH = r"C:\Users\<USER>\AppData\Local\Android\Sdk\platform-tools\adb.exe"

def run_adb_command(command):
    """执行ADB命令"""
    try:
        result = subprocess.run([ADB_PATH] + command, 
                              capture_output=True, text=True, timeout=30)
        return result.stdout.strip(), result.stderr.strip()
    except subprocess.TimeoutExpired:
        return "", "命令超时"
    except Exception as e:
        return "", str(e)

def check_device():
    """检查设备连接"""
    print("检查设备连接...")
    stdout, stderr = run_adb_command(["devices"])
    if "device" in stdout:
        print("✅ 设备已连接")
        return True
    else:
        print("❌ 未检测到设备")
        return False

def start_app():
    """启动应用"""
    print("启动GPSLogger应用...")
    stdout, stderr = run_adb_command([
        "shell", "am", "start", "-n", 
        "com.mendhak.gpslogger/.GpsMainActivity"
    ])
    if "Starting" in stdout:
        print("✅ 应用启动成功")
        time.sleep(3)  # 等待应用完全启动
        return True
    else:
        print(f"❌ 应用启动失败: {stderr}")
        return False

def monitor_logs():
    """监控相关日志"""
    print("开始监控日志...")
    print("请在手机上进行以下操作：")
    print("1. 打开注释模板设置")
    print("2. 点击'配置数值计数器'")
    print("3. 修改num1的初始值并点击'应用'按钮")
    print("4. 点击'保存'按钮")
    print("5. 返回主界面，打开计数器管理")
    print("6. 点击num1的'重置'按钮")
    print("\n监控日志中... (按Ctrl+C停止)")
    
    try:
        # 启动日志监控
        process = subprocess.Popen([
            ADB_PATH, "logcat", "-s", 
            "NumericCounterManager:*,CounterManagerDialog:*,NumericCounterConfigDialog:*"
        ], stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
        
        while True:
            line = process.stdout.readline()
            if line:
                # 过滤关键日志
                if any(keyword in line for keyword in [
                    "RESET BUTTON CLICKED DEBUG",
                    "SAVE CONFIGURATION DEBUG", 
                    "APPLY CONFIGURATION DEBUG",
                    "Counter reset",
                    "Configuration saved",
                    "Initial value changed"
                ]):
                    print(f"🔍 {line.strip()}")
            time.sleep(0.1)
            
    except KeyboardInterrupt:
        print("\n日志监控已停止")
        process.terminate()

def run_ui_test():
    """运行UI自动化测试（如果可能）"""
    print("尝试运行UI自动化测试...")
    
    # 这里可以添加UI自动化测试代码
    # 由于需要复杂的UI交互，暂时跳过
    print("⚠️  UI自动化测试需要手动执行")
    print("请参考'数值计数器修复测试指南.md'进行手动测试")

def main():
    """主函数"""
    print("=" * 50)
    print("数值计数器修复功能测试")
    print("=" * 50)
    
    # 检查设备
    if not check_device():
        sys.exit(1)
    
    # 启动应用
    if not start_app():
        sys.exit(1)
    
    # 提供测试选项
    print("\n请选择测试方式：")
    print("1. 监控日志（推荐）")
    print("2. 查看测试指南")
    print("3. 退出")
    
    choice = input("请输入选择 (1-3): ").strip()
    
    if choice == "1":
        monitor_logs()
    elif choice == "2":
        print("\n请查看'数值计数器修复测试指南.md'文件")
        print("该文件包含详细的手动测试步骤")
    elif choice == "3":
        print("测试结束")
    else:
        print("无效选择")

if __name__ == "__main__":
    main()
