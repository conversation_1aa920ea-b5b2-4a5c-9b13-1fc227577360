/*
 * Copyright (C) 2016 mendhak
 *
 * This file is part of GPSLogger for Android.
 *
 * GPSLogger for Android is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 2 of the License, or
 * (at your option) any later version.
 *
 * GPSLogger for Android is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with GPSLogger for Android.  If not, see <http://www.gnu.org/licenses/>.
 */

package com.mendhak.gpslogger.ui.fragments.settings;

import android.content.Intent;
import android.location.Location;
import android.os.Bundle;
import android.widget.Toast;

import androidx.preference.EditTextPreference;
import androidx.preference.ListPreference;
import androidx.preference.Preference;
import androidx.preference.PreferenceFragmentCompat;
import androidx.preference.SwitchPreferenceCompat;

import com.mendhak.gpslogger.R;
import com.mendhak.gpslogger.common.PreferenceHelper;
import com.mendhak.gpslogger.common.PreferenceNames;
import com.mendhak.gpslogger.common.slf4j.Logs;
import com.mendhak.gpslogger.ui.components.template.AnnotationTemplateEngine;

import org.slf4j.Logger;

/**
 * 单点推送设置Fragment
 * 用于配置单点位置推送的Intent参数和自动检测类型
 */
public class SinglePointPushSettingsFragment extends PreferenceFragmentCompat 
        implements Preference.OnPreferenceChangeListener, Preference.OnPreferenceClickListener {

    private static final Logger LOG = Logs.of(SinglePointPushSettingsFragment.class);
    private PreferenceHelper preferenceHelper;

    @Override
    public void onCreatePreferences(Bundle savedInstanceState, String rootKey) {
        setPreferencesFromResource(R.xml.single_point_push_settings, rootKey);
        
        preferenceHelper = PreferenceHelper.getInstance();
        
        setupPreferences();
    }

    private void setupPreferences() {
        // Setup change listeners
        findPreference(PreferenceNames.SINGLE_POINT_PUSH_ENABLED).setOnPreferenceChangeListener(this);

        // Setup target type change listener for dynamic visibility
        ListPreference targetTypePref = findPreference(PreferenceNames.SINGLE_POINT_PUSH_TARGET_TYPE);
        if (targetTypePref != null) {
            targetTypePref.setOnPreferenceChangeListener(this);
        }

        // Setup click listeners for test and help
        findPreference("single_point_push_test").setOnPreferenceClickListener(this);
        findPreference("single_point_push_help").setOnPreferenceClickListener(this);

        // Setup change listeners for all EditTextPreferences to update summaries
        setupEditTextPreferenceSummaries();
        setupListPreferenceSummaries();

        // Update visibility based on current target type
        updatePreferenceVisibility();
    }

    private void setupEditTextPreferenceSummaries() {
        String[] editTextKeys = {
            PreferenceNames.SINGLE_POINT_PUSH_ACTION,
            PreferenceNames.SINGLE_POINT_PUSH_CLASS,
            PreferenceNames.SINGLE_POINT_PUSH_PACKAGE,
            PreferenceNames.SINGLE_POINT_PUSH_DATA,
            PreferenceNames.SINGLE_POINT_PUSH_MIME_TYPE,
            PreferenceNames.SINGLE_POINT_PUSH_FLAGS,
            "single_point_push_extra_key_1",
            "single_point_push_extra_value_1",
            "single_point_push_extra_key_2",
            "single_point_push_extra_value_2",
            "single_point_push_extra_key_3",
            "single_point_push_extra_value_3",
            "single_point_push_extra_key_4",
            "single_point_push_extra_value_4",
            "single_point_push_extra_key_5",
            "single_point_push_extra_value_5",
            "single_point_push_extra_key_6",
            "single_point_push_extra_value_6"
        };

        for (String key : editTextKeys) {
            EditTextPreference pref = findPreference(key);
            if (pref != null) {
                pref.setOnPreferenceChangeListener(this);
                updateEditTextSummary(pref);
            }
        }
    }

    private void setupListPreferenceSummaries() {
        String[] listKeys = {
            PreferenceNames.SINGLE_POINT_PUSH_TARGET_TYPE,
            "single_point_push_auto_detect_type_1",
            "single_point_push_auto_detect_type_2",
            "single_point_push_auto_detect_type_3",
            "single_point_push_auto_detect_type_4",
            "single_point_push_auto_detect_type_5",
            "single_point_push_auto_detect_type_6"
        };

        for (String key : listKeys) {
            ListPreference pref = findPreference(key);
            if (pref != null) {
                pref.setOnPreferenceChangeListener(this);
                updateListSummary(pref);
            }
        }
    }

    private void updateEditTextSummary(EditTextPreference preference) {
        String value = preference.getText();
        if (value == null || value.trim().isEmpty()) {
            preference.setSummary("未设置");
        } else {
            preference.setSummary(value);
        }
    }

    private void updateListSummary(ListPreference preference) {
        CharSequence entry = preference.getEntry();
        if (entry != null) {
            preference.setSummary(entry);
        } else {
            preference.setSummary("未选择");
        }
    }

    private void updatePreferenceVisibility() {
        String targetType = preferenceHelper.getSinglePointPushTargetType();

        // All preferences are visible for now
        // In the future, we could hide certain preferences based on target type
        // For example, hide certain flags for broadcast vs activity
        LOG.debug("Current target type: {}", targetType);
    }

    @Override
    public boolean onPreferenceChange(Preference preference, Object newValue) {
        String key = preference.getKey();

        LOG.debug("Preference changed: {} = {}", key, newValue);

        if (key.equals(PreferenceNames.SINGLE_POINT_PUSH_ENABLED)) {
            boolean enabled = (Boolean) newValue;
            LOG.info("Single point push enabled: {}", enabled);
            return true;
        }

        if (key.equals(PreferenceNames.SINGLE_POINT_PUSH_TARGET_TYPE)) {
            String targetType = (String) newValue;
            LOG.info("Target type changed to: {}", targetType);
            // Update visibility after the preference is saved
            getView().post(this::updatePreferenceVisibility);
        }

        // Update summaries for EditTextPreferences
        if (preference instanceof EditTextPreference) {
            EditTextPreference editTextPref = (EditTextPreference) preference;
            String value = (String) newValue;
            if (value == null || value.trim().isEmpty()) {
                editTextPref.setSummary("未设置");
            } else {
                editTextPref.setSummary(value);
            }
            return true;
        }

        // Update summaries for ListPreferences
        if (preference instanceof ListPreference) {
            ListPreference listPref = (ListPreference) preference;
            String value = (String) newValue;
            int index = listPref.findIndexOfValue(value);
            if (index >= 0) {
                listPref.setSummary(listPref.getEntries()[index]);
            }
            return true;
        }

        return true;
    }

    @Override
    public boolean onPreferenceClick(Preference preference) {
        String key = preference.getKey();
        
        if ("single_point_push_test".equals(key)) {
            testIntentSending();
            return true;
        }
        
        if ("single_point_push_help".equals(key)) {
            showHelpDialog();
            return true;
        }
        
        return false;
    }

    private void testIntentSending() {
        try {
            // Get configuration values
            String targetType = preferenceHelper.getSinglePointPushTargetType();
            String action = preferenceHelper.getSinglePointPushAction();
            String className = preferenceHelper.getSinglePointPushClass();
            String packageName = preferenceHelper.getSinglePointPushPackage();
            String data = preferenceHelper.getSinglePointPushData();
            String mimeType = preferenceHelper.getSinglePointPushMimeType();
            String flagsStr = preferenceHelper.getSinglePointPushFlags();

            // Create test location for template processing
            Location testLocation = new Location("test");
            testLocation.setLatitude(39.9042);
            testLocation.setLongitude(116.4074);
            testLocation.setAltitude(50.0);
            testLocation.setAccuracy(5.0f);
            testLocation.setTime(System.currentTimeMillis());

            // Process template variables in data field
            AnnotationTemplateEngine templateEngine = AnnotationTemplateEngine.getInstance();
            if (!data.trim().isEmpty()) {
                data = templateEngine.processTemplateString(data, getContext(), testLocation, null, null, 0, null);
                LOG.debug("Processed data field: {}", data);
            }

            // Create test intent
            Intent testIntent = new Intent(action);

            if (!packageName.trim().isEmpty()) {
                testIntent.setPackage(packageName);
            }

            if (!className.trim().isEmpty()) {
                testIntent.setClassName(packageName, className);
            }

            if (!data.trim().isEmpty()) {
                testIntent.setDataAndType(android.net.Uri.parse(data),
                    mimeType.trim().isEmpty() ? null : mimeType);
            } else if (!mimeType.trim().isEmpty()) {
                testIntent.setType(mimeType);
            }

            // Set flags
            try {
                int flags = Integer.parseInt(flagsStr);
                if (flags != 0) {
                    testIntent.setFlags(flags);
                }
            } catch (NumberFormatException e) {
                LOG.warn("Invalid flags value: {}", flagsStr);
            }

            // Add extra data fields with template processing
            for (int i = 1; i <= 6; i++) {
                String extraKey = preferenceHelper.getSinglePointPushExtraKey(i);
                String extraValue = preferenceHelper.getSinglePointPushExtraValue(i);

                if (!extraKey.trim().isEmpty() && !extraValue.trim().isEmpty()) {
                    // Process template variables in extra value
                    extraValue = templateEngine.processTemplateString(extraValue, getContext(), testLocation, null, null, 0, null);
                    testIntent.putExtra(extraKey, extraValue);
                    LOG.debug("Added extra data: {} = {}", extraKey, extraValue);
                }
            }

            // Add test data
            testIntent.putExtra("test_data", "单点推送测试");
            testIntent.putExtra("timestamp", System.currentTimeMillis());
            testIntent.putExtra("latitude", testLocation.getLatitude());
            testIntent.putExtra("longitude", testLocation.getLongitude());
            testIntent.putExtra("altitude", testLocation.getAltitude());
            testIntent.putExtra("accuracy", testLocation.getAccuracy());

            // Send intent based on target type
            switch (targetType) {
                case "activity":
                    testIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                    getContext().startActivity(testIntent);
                    break;
                case "service":
                    getContext().startService(testIntent);
                    break;
                case "broadcast":
                default:
                    getContext().sendBroadcast(testIntent);
                    break;
            }

            Toast.makeText(getContext(), "测试Intent已发送 (" + targetType + ")", Toast.LENGTH_SHORT).show();
            LOG.info("Test intent sent: type={}, action={}, package={}, class={}",
                targetType, action, packageName, className);

        } catch (Exception e) {
            LOG.error("Error sending test intent", e);
            Toast.makeText(getContext(), "发送测试Intent失败: " + e.getMessage(), Toast.LENGTH_LONG).show();
        }
    }

    private void showHelpDialog() {
        String helpText = "单点推送设置帮助\n\n" +
                "全局变量支持：\n" +
                "在\"数据\"字段和\"附加数据\"的\"输入数值\"字段中，您可以使用以下全局变量：\n\n" +
                "位置相关：\n" +
                "• {latitude} - 当前纬度（十进制度数）\n" +
                "• {longitude} - 当前经度（十进制度数）\n" +
                "• {altitude} - 海拔高度（米）\n" +
                "• {accuracy} - GPS精度（米）\n" +
                "• {speed} - 当前速度（米/秒）\n" +
                "• {bearing} - 当前方向（度数）\n" +
                "• {coordinates} - 纬度,经度坐标对\n\n" +
                "时间相关：\n" +
                "• {timestamp} - 当前时间戳\n\n" +
                "用户自定义变量：\n" +
                "• {Push_location} - 可在用户变量设置中自定义推送位置格式\n\n" +
                "使用示例：\n" +
                "数据字段：geo:{latitude},{longitude}\n" +
                "附加数据：location_info = {Push_location}\n\n" +
                "注意：{Push_location}变量需要在'用户自定义变量设置'中配置具体格式。\n" +
                "变量将在Intent发送时被实际值替换。";

        new androidx.appcompat.app.AlertDialog.Builder(getContext())
                .setTitle("单点推送设置帮助")
                .setMessage(helpText)
                .setPositiveButton("确定", null)
                .show();
    }

    private String getStringPreference(String key, String defaultValue) {
        EditTextPreference pref = findPreference(key);
        if (pref != null && pref.getText() != null) {
            return pref.getText();
        }
        return defaultValue;
    }
}
