/*
 * Copyright (C) 2016 mendhak
 *
 * This file is part of GPSLogger for Android.
 *
 * GPSLogger for Android is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 2 of the License, or
 * (at your option) any later version.
 *
 * GPSLogger for Android is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with GPSLogger for Android.  If not, see <http://www.gnu.org/licenses/>.
 */

package com.mendhak.gpslogger.senders;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import com.mendhak.gpslogger.common.PreferenceHelper;
import com.mendhak.gpslogger.common.Session;
import com.mendhak.gpslogger.common.Strings;
import com.mendhak.gpslogger.common.slf4j.Logs;
import org.slf4j.Logger;

/**
 * BroadcastReceiver for handling scheduled send alarms
 */
public class ScheduledSendReceiver extends BroadcastReceiver {
    
    private static final Logger LOG = Logs.of(ScheduledSendReceiver.class);
    
    @Override
    public void onReceive(Context context, Intent intent) {
        try {
            String scheduledTime = intent.getStringExtra("scheduled_time");
            LOG.info("Scheduled send triggered at: {}", scheduledTime);
            
            PreferenceHelper preferenceHelper = PreferenceHelper.getInstance();
            
            // Check if scheduled sending is still enabled
            if (!preferenceHelper.isScheduledSendEnabled()) {
                LOG.debug("Scheduled sending is disabled, ignoring alarm");
                return;
            }
            
            // Check if auto send is enabled
            if (!preferenceHelper.isAutoSendEnabled()) {
                LOG.debug("Auto send is disabled, ignoring scheduled send");
                return;
            }
            
            // Get current filename to send
            String fileToSend = Strings.getFormattedFileName();
            if (fileToSend == null || fileToSend.trim().isEmpty()) {
                LOG.warn("No current filename available for scheduled send");
                return;
            }
            
            LOG.info("Executing scheduled send for file: {}", fileToSend);
            
            // Trigger the file sending
            FileSenderFactory.autoSendFiles(fileToSend);
            
        } catch (Exception e) {
            LOG.error("Error in scheduled send receiver", e);
        }
    }
}
