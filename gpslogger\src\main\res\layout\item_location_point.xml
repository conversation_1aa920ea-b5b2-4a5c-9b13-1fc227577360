<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginVertical="2dp"
    app:cardCornerRadius="6dp"
    app:cardElevation="1dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="6dp"
        android:gravity="center_vertical"
        android:minHeight="40dp">

        <!-- Column 1: Sequence Number -->
        <TextView
            android:id="@+id/sequence_number_text"
            android:layout_width="20dp"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:gravity="center"
            android:text="1"
            android:textColor="@android:color/black"
            android:textSize="11sp"
            android:textStyle="bold" />

        <!-- Column 2: Coordinates -->
        <TextView
            android:id="@+id/coordinates_text"
            android:layout_width="80dp"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_marginStart="3dp"
            android:text="29.7575536,\n106.6363669"
            android:textColor="@android:color/black"
            android:textSize="9sp"
            android:fontFamily="monospace"
            android:maxLines="2"
            android:lineSpacingExtra="-2dp" />

        <!-- Column 3: Description - 使用权重占用剩余空间 -->
        <TextView
            android:id="@+id/description_text"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:layout_gravity="center_vertical"
            android:layout_marginStart="4dp"
            android:layout_marginEnd="4dp"
            android:text="第1个点"
            android:textColor="@android:color/black"
            android:textSize="10sp"
            android:maxLines="2"
            android:ellipsize="end"
            android:lineSpacingExtra="-1dp" />

        <!-- Column 4: Action Buttons - 右对齐 -->
        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical|end"
            android:orientation="horizontal">

            <!-- Dynamic Button Container for Pass/Fail buttons -->
            <LinearLayout
                android:id="@+id/button_container"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <!-- Default buttons (fallback when dynamic buttons are not available) -->
                <Button
                    android:id="@+id/pass_button"
                    android:layout_width="46dp"
                    android:layout_height="33dp"
                    android:background="@drawable/button_background_green"
                    android:minWidth="0dp"
                    android:minHeight="0dp"
                    android:text="通过"
                    android:textColor="@android:color/white"
                    android:textSize="8sp" />

                <Button
                    android:id="@+id/fail_button"
                    android:layout_width="46dp"
                    android:layout_height="33dp"
                    android:layout_marginStart="2dp"
                    android:background="@drawable/button_background_red"
                    android:minWidth="0dp"
                    android:minHeight="0dp"
                    android:text="不过"
                    android:textColor="@android:color/white"
                    android:textSize="8sp" />

            </LinearLayout>

            <Button
                android:id="@+id/push_button"
                android:layout_width="44dp"
                android:layout_height="33dp"
                android:layout_marginStart="2dp"
                android:background="@drawable/button_background"
                android:minWidth="0dp"
                android:minHeight="0dp"
                android:text="推送"
                android:textColor="@android:color/white"
                android:textSize="9sp" />

        </LinearLayout>

    </LinearLayout>

</androidx.cardview.widget.CardView>
