# 文本输入后台支持和模板变量修复

## 🎯 问题描述

用户反馈两个关键问题：

1. **{input_text}变量没有及时更新**：文本输入后模板变量没有正确设置
2. **文本输入框无法后台触发**：以前支持后台弹出悬浮文本输入框，现在不行了

## 🔍 问题分析

### 问题1：模板变量更新问题
- **根本原因**：外部设备触发的文本输入处理逻辑正确，但缺少后台支持
- **影响范围**：主要影响后台触发的文本输入场景

### 问题2：后台文本输入支持缺失
- **根本原因**：文本输入方法没有检测应用前台/后台状态
- **技术原因**：
  - 前台时：使用`TextInputDialog`（传统对话框）
  - 后台时：应该使用`OverlayTextInputManager`（悬浮窗）
  - 当前实现：只使用传统对话框，后台无法显示

### 现有的后台支持基础设施
项目中已经有完整的后台文本输入支持：
- ✅ `OverlayTextInputManager`：悬浮窗文本输入管理器
- ✅ `OverlayPermissionManager`：悬浮窗权限管理
- ✅ 悬浮窗布局和交互逻辑
- ❌ **缺失**：前台/后台状态检测和智能切换

## ✅ 修复方案

### 修复1：添加前台/后台状态检测

**新增方法**：`isAppInForeground()`
```java
private boolean isAppInForeground() {
    try {
        ActivityManager activityManager = (ActivityManager) getSystemService(Context.ACTIVITY_SERVICE);
        if (activityManager == null) return false;

        List<ActivityManager.RunningAppProcessInfo> appProcesses = activityManager.getRunningAppProcesses();
        if (appProcesses == null) return false;

        String packageName = getPackageName();
        for (ActivityManager.RunningAppProcessInfo appProcess : appProcesses) {
            if (appProcess.processName.equals(packageName)) {
                return appProcess.importance == ActivityManager.RunningAppProcessInfo.IMPORTANCE_FOREGROUND;
            }
        }
        return false;
    } catch (Exception e) {
        LOG.warn("Failed to check if app is in foreground: {}", e.getMessage());
        return true; // Default to foreground if check fails
    }
}
```

### 修复2：智能文本输入方法选择

#### 注释按钮文本输入修复

**修改前**：
```java
private void showAnnotationTextInputDialog(String buttonText, int buttonIndex, String groupName) {
    // 只使用传统对话框
    TextInputDialog.show(this, buttonText, listener);
}
```

**修改后**：
```java
private void showAnnotationTextInputDialog(String buttonText, int buttonIndex, String groupName) {
    // 智能选择输入方法
    if (isAppInForeground()) {
        showForegroundTextInputDialog(buttonText, buttonIndex, groupName);
    } else {
        showBackgroundTextInputOverlay(buttonText, buttonIndex, groupName);
    }
}
```

#### 快速文本输入修复

**修改前**：
```java
private void onQuickTextInputClicked() {
    // 只使用传统对话框
    TextInputDialog.show(this, "一键文本输入", listener);
}
```

**修改后**：
```java
private void onQuickTextInputClicked() {
    // 智能选择输入方法
    if (isAppInForeground()) {
        showQuickTextInputDialog();
    } else {
        showQuickTextInputOverlay();
    }
}
```

### 修复3：统一的文本输入结果处理

#### 注释按钮文本输入结果处理
```java
private void handleAnnotationTextInputResult(String inputText, String buttonText, int buttonIndex, String groupName) {
    if (inputText == null || inputText.trim().isEmpty()) {
        LOG.warn("Annotation text input result is empty");
        return;
    }

    // Store input text for template variable and clear voice text for mutual exclusion
    BasicVariableProvider.setInputText(this, inputText);
    
    // Clear voice text to ensure mutual exclusion between input_text and voice_text variables
    Session.getInstance().clearTemplateVoiceText();

    // Post annotation event with preserved button context
    EventBus.getDefault().post(new CommandEvents.Annotate(inputText, "",
                                                          buttonText, buttonIndex, groupName));
}
```

#### 快速文本输入结果处理
```java
private void handleQuickTextInputResult(String inputText) {
    // Store input text for template variable and clear voice text for mutual exclusion
    BasicVariableProvider.setInputText(this, inputText);
    
    // Clear voice text to ensure mutual exclusion between input_text and voice_text variables
    Session.getInstance().clearTemplateVoiceText();

    // Post annotation event with input text
    EventBus.getDefault().post(new CommandEvents.Annotate(inputText, "",
                                                          "一键文本输入", 0, "快速输入"));
}
```

## 🔧 技术实现细节

### 前台文本输入流程
```
用户触发 → 检测前台状态 → 使用TextInputDialog → 用户输入 → handleTextInputResult()
    ↓
BasicVariableProvider.setInputText() // 设置{input_text}
Session.getInstance().clearTemplateVoiceText() // 清空{voice_text}
    ↓
CommandEvents.Annotate(inputText, "", buttonName, buttonIndex, groupName)
    ↓
模板处理：{input_text} = "用户输入内容", {voice_text} = ""
```

### 后台文本输入流程
```
用户触发 → 检测后台状态 → 使用OverlayTextInputManager → 悬浮窗输入 → handleTextInputResult()
    ↓
BasicVariableProvider.setInputText() // 设置{input_text}
Session.getInstance().clearTemplateVoiceText() // 清空{voice_text}
    ↓
CommandEvents.Annotate(inputText, "", buttonName, buttonIndex, groupName)
    ↓
模板处理：{input_text} = "用户输入内容", {voice_text} = ""
```

### 悬浮窗权限处理
```java
// OverlayTextInputManager.showTextInputOverlay()
if (!OverlayPermissionManager.hasOverlayPermission(context)) {
    LOG.warn("Overlay permission not granted, cannot show overlay text input");
    Toast.makeText(context, "需要悬浮窗权限才能在后台弹出文本输入框", Toast.LENGTH_LONG).show();
    OverlayPermissionManager.requestOverlayPermission(context);
    return;
}
```

### 错误处理和降级机制
```java
private void showBackgroundTextInputOverlay(String buttonText, int buttonIndex, String groupName) {
    try {
        // 尝试使用悬浮窗
        OverlayTextInputManager overlayManager = new OverlayTextInputManager(this);
        overlayManager.showTextInputOverlay(buttonText, listener);
    } catch (Exception e) {
        LOG.error("Failed to show overlay text input, falling back to foreground dialog", e);
        // 降级到前台对话框
        showForegroundTextInputDialog(buttonText, buttonIndex, groupName);
    }
}
```

## 🎯 修复效果

### 1. 前台使用场景
- **触发方式**：应用在前台时触发文本输入
- **显示方式**：传统AlertDialog对话框
- **用户体验**：与之前完全一致
- **模板变量**：`{input_text}` = 用户输入，`{voice_text}` = ""

### 2. 后台使用场景
- **触发方式**：应用在后台时触发文本输入（外部设备、通知栏等）
- **显示方式**：悬浮窗文本输入框
- **用户体验**：可以在任何应用上方显示，无需切换到GPSLogger
- **模板变量**：`{input_text}` = 用户输入，`{voice_text}` = ""

### 3. 权限处理
- **首次使用**：自动检测悬浮窗权限，未授权时引导用户授权
- **权限缺失**：降级到前台对话框模式
- **权限恢复**：下次使用时自动恢复悬浮窗模式

## 🧪 验证建议

### 测试场景1：前台文本输入
1. **操作**：应用在前台，点击"一键文本输入"或注释按钮（文本模式）
2. **预期**：弹出传统文本输入对话框
3. **验证**：输入文本后，检查txt文件中`{input_text}`变量是否正确

### 测试场景2：后台文本输入
1. **操作**：应用在后台，通过外部设备或通知栏触发文本输入
2. **预期**：弹出悬浮窗文本输入框
3. **验证**：输入文本后，检查txt文件中`{input_text}`变量是否正确

### 测试场景3：权限处理
1. **操作**：首次后台触发文本输入（未授权悬浮窗权限）
2. **预期**：提示需要悬浮窗权限，引导用户授权
3. **验证**：授权后再次触发，应该正常显示悬浮窗

### 测试场景4：模板变量互斥
1. **配置模板**：`{date} {time} -{group_name}_{button_name}_ {voice_text}{input_text}`
2. **操作**：依次进行语音输入和文本输入
3. **验证**：
   - 语音输入后：`{voice_text}` 有内容，`{input_text}` 为空
   - 文本输入后：`{input_text}` 有内容，`{voice_text}` 为空

### 测试场景5：外部设备映射
1. **配置**：映射外部设备按键到注释按钮（文本模式）
2. **操作**：应用在后台，按下外部设备按键
3. **预期**：弹出悬浮窗文本输入框
4. **验证**：输入文本后，按注释按钮的模板进行转换并写入txt文件

## 🔄 支持的触发方式

现在所有文本输入触发方式都支持智能前台/后台切换：

- ✅ **一键文本输入**：主页菜单按钮
- ✅ **通知栏文本输入**：通知栏按钮  
- ✅ **注释页面文本按钮**：注释面板中的文本模式按钮
- ✅ **外部设备映射文本按钮**：映射到外部设备的注释文本按钮
- ✅ **外部设备直接文本输入**：外部设备的文本输入功能

## 🎉 关键改进点

1. **智能输入方法选择**：根据应用前台/后台状态自动选择最佳输入方式
2. **完整的后台支持**：恢复了后台悬浮文本输入功能
3. **统一的结果处理**：所有文本输入都使用相同的模板变量处理逻辑
4. **权限智能处理**：自动检测和引导悬浮窗权限授权
5. **降级机制**：权限缺失时自动降级到前台对话框
6. **模板变量互斥**：确保`{voice_text}`和`{input_text}`正确互斥

现在文本输入功能应该能够：
- ✅ 在前台和后台都正常工作
- ✅ 正确更新`{input_text}`模板变量
- ✅ 保持与语音输入的变量互斥效果
- ✅ 支持所有触发方式的一致体验

APK已经构建并安装完成，请测试验证修复效果！🚀
