/*
 * Copyright (C) 2024 mendhak
 *
 * This file is part of GPSLogger for Android.
 *
 * GPSLogger is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 2 of the License, or
 * (at your option) any later version.
 *
 * GPSLogger is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with GPSLogger.  If not, see <http://www.gnu.org/licenses/>.
 */

package com.mendhak.gpslogger.ui.components.template;

import android.content.Context;
import android.location.Location;
import com.mendhak.gpslogger.common.PreferenceHelper;
import com.mendhak.gpslogger.common.slf4j.Logs;
import com.mendhak.gpslogger.ui.components.template.providers.BasicVariableProvider;
import com.mendhak.gpslogger.ui.components.template.providers.LocationVariableProvider;
import com.mendhak.gpslogger.ui.components.template.providers.TimeVariableProvider;
import com.mendhak.gpslogger.ui.components.template.providers.DeviceVariableProvider;
import com.mendhak.gpslogger.ui.components.template.providers.CounterVariableProvider;
import com.mendhak.gpslogger.ui.components.template.providers.UserVariableProvider;
import com.mendhak.gpslogger.ui.components.template.providers.SinglePointPushVariableProvider;
import com.mendhak.gpslogger.ui.components.template.providers.NumericCounterVariableProvider;
import com.mendhak.gpslogger.ui.components.template.providers.EnvironmentVariableProvider;
import com.mendhak.gpslogger.ui.components.template.providers.GeocodingVariableProvider;

import org.slf4j.Logger;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Main template engine for processing annotation templates
 */
public class AnnotationTemplateEngine {
    
    private static final Logger LOG = Logs.of(AnnotationTemplateEngine.class);
    private static final String DEFAULT_TEMPLATE = "{voice_text}";
    
    private final TemplateParser parser;
    private final List<VariableProvider> providers;
    private final Map<String, String> templateCache;
    private final PreferenceHelper preferenceHelper;
    private final AdvancedTemplateProcessor advancedProcessor;
    private final TemplatePerformanceMonitor performanceMonitor;
    
    // Singleton instance
    private static AnnotationTemplateEngine instance;
    
    private AnnotationTemplateEngine() {
        this.parser = new TemplateParser();
        this.providers = new ArrayList<>();
        this.templateCache = new HashMap<>();
        this.preferenceHelper = PreferenceHelper.getInstance();
        this.advancedProcessor = new AdvancedTemplateProcessor();
        this.performanceMonitor = new TemplatePerformanceMonitor();
        
        // Register default providers
        registerProvider(new BasicVariableProvider());
        registerProvider(new LocationVariableProvider());
        registerProvider(new TimeVariableProvider());
        registerProvider(new DeviceVariableProvider());
        registerProvider(new CounterVariableProvider());
        registerProvider(new NumericCounterVariableProvider());
        registerProvider(new UserVariableProvider());
        registerProvider(new SinglePointPushVariableProvider());
        registerProvider(new EnvironmentVariableProvider());
        registerProvider(new GeocodingVariableProvider());

        
        // Sort providers by priority (highest first)
        Collections.sort(providers, new Comparator<VariableProvider>() {
            @Override
            public int compare(VariableProvider p1, VariableProvider p2) {
                return Integer.compare(p2.getPriority(), p1.getPriority());
            }
        });
    }
    
    /**
     * Get singleton instance
     * @return AnnotationTemplateEngine instance
     */
    public static synchronized AnnotationTemplateEngine getInstance() {
        if (instance == null) {
            instance = new AnnotationTemplateEngine();
        }
        return instance;
    }
    
    /**
     * Register a variable provider
     * @param provider The provider to register
     */
    public void registerProvider(VariableProvider provider) {
        if (provider != null && !providers.contains(provider)) {
            providers.add(provider);
            LOG.debug("Registered variable provider: {}", provider.getClass().getSimpleName());
        }
    }
    
    /**
     * Process annotation template and return final text
     * @param context Android context
     * @param location Current location (may be null)
     * @param voiceText Original voice recognition text
     * @param buttonName Name of the annotation button
     * @param buttonIndex Index of the button in group
     * @param groupName Name of the button group
     * @return Processed annotation text
     */
    public String processTemplate(Context context, Location location, String voiceText,
                                String buttonName, int buttonIndex, String groupName) {
        
        long startTime = System.currentTimeMillis();
        
        try {
            // Get template from preferences
            String template = getTemplate();

            // Check if template contains counter variables or dynamic variables
            boolean hasCounterVariables = templateContainsCounterVariables(template);
            boolean hasDynamicVariables = templateContainsDynamicVariables(template);

            // Check cache first (only for templates without counter or dynamic variables)
            String cacheKey = null;
            if (!hasCounterVariables && !hasDynamicVariables) {
                cacheKey = generateCacheKey(template, voiceText, buttonName, buttonIndex, groupName);
                if (templateCache.containsKey(cacheKey)) {
                    LOG.debug("Using cached template result");
                    performanceMonitor.recordCacheHit();
                    return templateCache.get(cacheKey);
                }
                performanceMonitor.recordCacheMiss();
            } else {
                LOG.debug("Template contains counter or dynamic variables, skipping cache");
            }

            // Process template with advanced features first
            String preprocessedTemplate = advancedProcessor.processAdvancedFeatures(template);

            // Process template variables
            String result = processTemplateInternal(preprocessedTemplate, context, location, voiceText,
                                                  buttonName, buttonIndex, groupName, false);

            // Cache result only for templates without counter or dynamic variables
            if (!hasCounterVariables && !hasDynamicVariables && cacheKey != null) {
                // Limit cache size
                if (templateCache.size() > 100) {
                    templateCache.clear();
                }
                templateCache.put(cacheKey, result);
            }
            
            long processingTime = System.currentTimeMillis() - startTime;
            performanceMonitor.recordProcessingTime(template, processingTime);
            LOG.debug("Template processed in {}ms: '{}' -> '{}'", processingTime, template, result);

            return result;
            
        } catch (Exception e) {
            LOG.error("Error processing template, falling back to default", e);
            return voiceText != null ? voiceText : "";
        }
    }

    /**
     * Process a custom template string (for filename processing)
     * @param templateString Custom template string to process
     * @param context Android context
     * @param location Current location (may be null)
     * @param voiceText Original voice recognition text
     * @param buttonName Name of the annotation button
     * @param buttonIndex Index of the button in group
     * @param groupName Name of the button group
     * @return Processed template string
     */
    public String processTemplateString(String templateString, Context context, Location location,
                                      String voiceText, String buttonName, int buttonIndex, String groupName) {

        long startTime = System.currentTimeMillis();

        try {
            if (templateString == null || templateString.isEmpty()) {
                return voiceText != null ? voiceText : "";
            }

            // Check if template contains counter variables
            boolean hasCounterVariables = templateContainsCounterVariables(templateString);

            // Process template with advanced features first
            String preprocessedTemplate = advancedProcessor.processAdvancedFeatures(templateString);

            // Process template variables
            String result = processTemplateInternal(preprocessedTemplate, context, location, voiceText,
                                                  buttonName, buttonIndex, groupName, false);

            long processingTime = System.currentTimeMillis() - startTime;
            performanceMonitor.recordProcessingTime(templateString, processingTime);
            LOG.debug("Template string processed in {}ms: '{}' -> '{}'", processingTime, templateString, result);

            return result;

        } catch (Exception e) {
            LOG.error("Error processing template string, falling back to original", e);
            return templateString;
        }
    }

    /**
     * Get current template from preferences
     * @return Template string
     */
    public String getTemplate() {
        return preferenceHelper.getAnnotationTemplate();
    }
    
    /**
     * Set template in preferences
     * @param template Template string
     */
    public void setTemplate(String template) {
        if (template == null || template.trim().isEmpty()) {
            template = DEFAULT_TEMPLATE;
        }
        preferenceHelper.setAnnotationTemplate(template);
        templateCache.clear(); // Clear cache when template changes
    }
    
    /**
     * Validate template syntax
     * @param template Template to validate
     * @return Validation result
     */
    public TemplateParser.ValidationResult validateTemplate(String template) {
        return parser.validateTemplate(template);
    }
    
    /**
     * Get all available variables from all providers
     * @return Map of category -> variables
     */
    public Map<String, Map<String, String>> getAllAvailableVariables() {
        Map<String, Map<String, String>> allVariables = new HashMap<>();

        // Process providers in priority order (they are already sorted by priority)
        for (VariableProvider provider : providers) {
            String category = provider.getCategory();
            Map<String, String> variables = provider.getAvailableVariables();

            // Merge variables if category already exists, maintaining order
            if (allVariables.containsKey(category)) {
                Map<String, String> existingVariables = allVariables.get(category);
                // Create a new LinkedHashMap to maintain insertion order
                Map<String, String> mergedVariables = new java.util.LinkedHashMap<>(existingVariables);
                mergedVariables.putAll(variables);
                allVariables.put(category, mergedVariables);
            } else {
                // Use LinkedHashMap to maintain insertion order
                allVariables.put(category, new java.util.LinkedHashMap<>(variables));
            }
        }

        return allVariables;
    }
    
    /**
     * Preview template with sample data
     * @param template Template to preview
     * @param context Android context
     * @return Preview result
     */
    public String previewTemplate(String template, Context context) {
        // Use sample data for preview
        Location sampleLocation = new Location("gps");
        sampleLocation.setLatitude(39.9042);
        sampleLocation.setLongitude(116.4074);
        sampleLocation.setAltitude(50.0);
        sampleLocation.setAccuracy(5.0f);
        sampleLocation.setSpeed(2.5f);
        sampleLocation.setBearing(45.0f);

        return processTemplateInternal(template, context, sampleLocation,
                                     "Sample voice text", "Sample Button", 1, "Sample Group", true);
    }
    
    /**
     * Internal template processing method
     */
    private String processTemplateInternal(String template, Context context, Location location,
                                         String voiceText, String buttonName, int buttonIndex, String groupName, boolean isPreview) {
        
        if (template == null || template.isEmpty()) {
            return voiceText != null ? voiceText : "";
        }
        
        // Parse template to find variables
        List<TemplateParser.TemplateVariable> variables = parser.parseTemplate(template);
        
        if (variables.isEmpty()) {
            return template; // No variables to process
        }
        
        // Process variables in reverse order to maintain correct positions
        StringBuilder result = new StringBuilder(template);
        for (int i = variables.size() - 1; i >= 0; i--) {
            TemplateParser.TemplateVariable variable = variables.get(i);
            String value = resolveVariable(variable, context, location, voiceText,
                                         buttonName, buttonIndex, groupName, isPreview);

            if (value != null) {
                result.replace(variable.getStartIndex(), variable.getEndIndex(), value);
            }
        }
        
        return result.toString();
    }
    
    /**
     * Resolve a single variable
     */
    private String resolveVariable(TemplateParser.TemplateVariable variable, Context context,
                                 Location location, String voiceText, String buttonName,
                                 int buttonIndex, String groupName, boolean isPreview) {
        
        String variableName = variable.getName();
        String parameter = variable.getParameter();
        
        // Handle special cases with parameters
        if (variableName.equals("date_custom") || variableName.equals("time_custom")) {
            for (VariableProvider provider : providers) {
                if (provider instanceof TimeVariableProvider) {
                    return ((TimeVariableProvider) provider).resolveCustomFormat(variableName, parameter);
                }
            }
        }
        
        // Handle counter variables specially to avoid unwanted increments during preview
        if (isCounterVariable(variableName)) {
            CounterManager counterManager = CounterManager.getInstance();
            return counterManager.getCounterValueForTemplate(variableName, context, buttonName,
                                                            buttonIndex, groupName, isPreview);
        }

        // Try each provider in priority order
        for (VariableProvider provider : providers) {
            if (provider.supportsVariable(variableName)) {
                String value = provider.resolveVariable(variableName, context, location,
                                                      voiceText, buttonName, buttonIndex, groupName);
                if (value != null) {
                    return value;
                }
            }
        }
        
        // Variable not found, return placeholder
        LOG.warn("Unknown variable: {}", variableName);
        return "{" + variableName + "}";
    }
    
    /**
     * Generate cache key for template result
     */
    private String generateCacheKey(String template, String voiceText, String buttonName,
                                  int buttonIndex, String groupName) {
        return template + "|" + voiceText + "|" + buttonName + "|" + buttonIndex + "|" + groupName;
    }

    /**
     * Check if a variable is a counter variable
     */
    private boolean isCounterVariable(String variableName) {
        // Traditional counter variables
        if (variableName.equals("sequence") ||
            variableName.equals("daily_sequence") ||
            variableName.equals("session_sequence") ||
            variableName.equals("button_sequence") ||
            variableName.equals("hourly_sequence") ||
            variableName.equals("monthly_sequence")) {
            return true;
        }

        // Numeric counter variables (num1 through num20)
        return variableName.matches("^num([1-9]|1[0-9]|20)$");
    }

    /**
     * Check if template contains any counter variables
     */
    private boolean templateContainsCounterVariables(String template) {
        if (template == null || template.isEmpty()) {
            return false;
        }

        // Parse template to find variables
        List<TemplateParser.TemplateVariable> variables = parser.parseTemplate(template);

        // Check if any variable is a counter variable
        for (TemplateParser.TemplateVariable variable : variables) {
            if (isCounterVariable(variable.getName())) {
                return true;
            }
        }

        return false;
    }

    /**
     * Check if template contains any dynamic variables that change between calls
     */
    private boolean templateContainsDynamicVariables(String template) {
        if (template == null || template.isEmpty()) {
            return false;
        }

        // Parse template to find variables
        List<TemplateParser.TemplateVariable> variables = parser.parseTemplate(template);

        // Check if any variable is a dynamic variable
        for (TemplateParser.TemplateVariable variable : variables) {
            if (isDynamicVariable(variable.getName())) {
                return true;
            }
        }

        return false;
    }

    /**
     * Check if a variable is dynamic (changes between calls)
     */
    private boolean isDynamicVariable(String variableName) {
        // Variables that can change between calls and should not be cached
        return variableName.equals("input_text") ||
               variableName.equals("date") ||
               variableName.equals("time") ||
               variableName.equals("timestamp") ||
               variableName.equals("datetime") ||
               variableName.startsWith("date_custom") ||
               variableName.startsWith("time_custom");
    }

    /**
     * Get performance statistics
     */
    public TemplatePerformanceMonitor.PerformanceStats getPerformanceStats() {
        return performanceMonitor.getPerformanceStats();
    }

    /**
     * Get optimization recommendations
     */
    public java.util.List<String> getOptimizationRecommendations() {
        return performanceMonitor.getOptimizationRecommendations();
    }

    /**
     * Reset performance statistics
     */
    public void resetPerformanceStats() {
        performanceMonitor.reset();
    }
}
