# GPSLogger 三级按钮层次结构用户指南

## 📋 功能概述

GPSLogger现在支持三级按钮层次结构，让您可以更精细地组织和管理注释按钮：

```
组名(Group) → 一级按钮(Primary Button) → 二级按钮(Secondary Button)
```

### 🎯 主要优势

- **更好的组织结构**：将相关功能按层次分组
- **减少界面混乱**：通过折叠式设计节省屏幕空间
- **灵活的触发模式**：每个二级按钮可以独立设置语音/文本/计数器模式
- **模板变量绑定**：支持动态内容生成和变量替换
- **向后兼容**：现有两级结构继续正常工作

---

## 🚀 快速开始

### 1. 设置流程

#### 步骤1：管理分组
1. 打开GPSLogger → 注释视图
2. 长按任意分组标题 → 选择"编辑分组"
3. 创建或编辑分组信息

#### 步骤2：编辑一级按钮
1. 长按任意按钮 → 选择"编辑按钮"
2. 在增强编辑界面中：
   - 设置按钮基本信息（名称、颜色、触发模式）
   - 勾选"设为一级按钮"选项
   - 点击"管理二级按钮"

#### 步骤3：配置二级按钮
1. 在二级按钮管理界面中：
   - 点击"添加二级按钮"
   - 设置按钮文本、触发模式
   - 可选：设置模板变量绑定
   - 保存设置

### 2. 使用流程

#### 日常使用
1. **点击一级按钮**：
   - 如果有二级按钮：弹出二级按钮矩阵
   - 如果没有二级按钮：直接触发功能

2. **选择二级按钮**：
   - 在弹出的矩阵中点击所需的二级按钮
   - 根据触发模式执行相应操作

3. **触发模式说明**：
   - **语音输入**：启动语音识别，识别结果用于注释
   - **文本输入**：弹出文本输入框，手动输入注释内容
   - **计数器**：直接生成带计数器的注释

---

## 🔧 高级功能

### 模板变量绑定

二级按钮支持以下模板变量：

#### 基础变量
- `{secondary_button_text}` - 当前二级按钮文本
- `{secondary_button_index}` - 二级按钮索引
- `{primary_button_text}` - 父级一级按钮文本
- `{button_hierarchy}` - 完整层次结构路径

#### 输入内容变量
- `{secondary_input}` - 二级按钮的文本输入内容
- `{secondary_voice}` - 二级按钮的语音输入内容
- `{secondary_counter}` - 二级按钮的计数器值

#### 自定义变量
- `{secondary_var1}` 到 `{secondary_var10}` - 自定义变量

### 模板变量使用示例

```
模板: {primary_button_text} > {secondary_button_text}: {secondary_voice}
输出: 工作记录 > 会议记录: 今天的项目进展讨论
```

```
模板: [{button_hierarchy}] {secondary_input} - {date} {time}
输出: [工作记录 → 会议记录] 项目进展讨论 - 2024-07-19 14:30
```

---

## 📱 界面说明

### 视觉指示

- **一级按钮标识**：有二级按钮的一级按钮显示为 `按钮名 ▼(数量)`
- **二级按钮矩阵**：网格布局，支持滚动
- **触发模式图标**：
  - 🎤 语音输入模式
  - ✏️ 文本输入模式
  - 🔢 计数器模式

### 管理界面

- **增强按钮编辑**：支持三级层次结构设置
- **二级按钮管理**：专门的二级按钮管理界面
- **拖拽排序**：支持二级按钮的拖拽重排序

---

## 🔄 数据迁移

### 向后兼容性

- 现有的两级结构（组名→按钮）继续正常工作
- 现有按钮自动识别为普通按钮（非一级按钮）
- 数据升级是渐进式的，不会丢失现有配置

### 升级建议

1. **逐步迁移**：不需要一次性重构所有按钮
2. **测试验证**：在重要场景中先测试新功能
3. **备份设置**：建议在大规模修改前备份按钮配置

---

## 🛠️ 故障排除

### 常见问题

**Q: 点击一级按钮没有弹出二级按钮矩阵？**
A: 检查该按钮是否设置为"一级按钮"且已添加二级按钮

**Q: 二级按钮的模板变量不生效？**
A: 确保在设置中启用了"注释模板"功能

**Q: 语音识别在二级按钮中不工作？**
A: 检查设备的语音识别权限和网络连接

**Q: 二级按钮的注释格式不正确？**
A: 检查模板变量设置，确保语法正确

### 性能优化

- 二级按钮数量建议控制在20个以内
- 复杂模板变量可能影响响应速度
- 定期清理不使用的按钮配置

---

## 📚 技术细节

### 数据结构

```json
{
  "isPrimaryButton": true,
  "templateVariable": "{primary_button_text} > {secondary_button_text}: {secondary_voice}",
  "secondaryButtons": [
    {
      "id": "secondary_1",
      "text": "会议记录",
      "triggerMode": "VOICE_INPUT",
      "templateVariable": "{secondary_voice}",
      "order": 0,
      "isEnabled": true
    }
  ]
}
```

### API接口

- `SecondaryButtonManager` - 二级按钮管理工具类
- `SecondaryButtonVariableProvider` - 模板变量提供者
- `SecondaryButtonMatrixDialog` - 二级按钮矩阵界面
- `EnhancedButtonEditDialog` - 增强按钮编辑界面

---

## 🎉 使用建议

### 最佳实践

1. **合理分层**：
   - 一级按钮：主要功能分类（如"工作记录"、"生活记录"）
   - 二级按钮：具体操作（如"会议记录"、"任务完成"）

2. **命名规范**：
   - 使用简洁明了的按钮名称
   - 避免过长的文本影响界面美观

3. **模板设计**：
   - 充分利用模板变量提高效率
   - 设计统一的注释格式便于后续处理

4. **功能测试**：
   - 新建按钮后先测试各种触发模式
   - 验证模板变量是否按预期工作

### 应用场景

- **项目管理**：项目 → 阶段 → 具体任务
- **日常记录**：生活 → 分类 → 具体事件
- **工作流程**：部门 → 流程 → 步骤
- **学习笔记**：科目 → 章节 → 知识点

---

## 📞 技术支持

如果在使用过程中遇到问题，请：

1. 检查本指南的故障排除部分
2. 查看GPSLogger的日志输出
3. 尝试重启应用或重新安装
4. 联系开发团队获取技术支持

---

*本指南基于GPSLogger三级按钮层次结构功能编写，版本：V94+*
