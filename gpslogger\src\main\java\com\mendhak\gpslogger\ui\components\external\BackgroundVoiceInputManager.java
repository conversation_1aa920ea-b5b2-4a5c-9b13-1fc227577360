/*
 * Copyright (C) 2024 mendhak
 *
 * This file is part of GPSLogger for Android.
 *
 * GPSLogger is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 2 of the License, or
 * (at your option) any later version.
 *
 * GPSLogger is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with GPSLogger.  If not, see <http://www.gnu.org/licenses/>.
 */

package com.mendhak.gpslogger.ui.components.external;

import android.app.ActivityManager;
import android.content.Context;
import android.content.Intent;
import android.widget.Toast;
import com.mendhak.gpslogger.common.PreferenceHelper;
import com.mendhak.gpslogger.common.slf4j.Logs;
import com.mendhak.gpslogger.common.events.CommandEvents;
import com.mendhak.gpslogger.ui.components.AudioRecordingManager;
import com.mendhak.gpslogger.ui.components.AudioPermissionManager;
import com.mendhak.gpslogger.ui.components.external.ExternalDeviceGroupNameStandardizer;
import de.greenrobot.event.EventBus;
import org.slf4j.Logger;

/**
 * Manager for handling background voice input operations
 * Provides optimized voice input experience when app is in background
 */
public class BackgroundVoiceInputManager {
    
    private static final Logger LOG = Logs.of(BackgroundVoiceInputManager.class);
    
    private final Context context;
    private final PreferenceHelper preferenceHelper;
    
    /**
     * Constructor
     * @param context Android context
     */
    public BackgroundVoiceInputManager(Context context) {
        this.context = context.getApplicationContext();
        this.preferenceHelper = PreferenceHelper.getInstance();
    }
    
    /**
     * Start background voice input operation
     * @param source The source that triggered the voice input
     * @return true if voice input was started, false otherwise
     */
    public boolean startBackgroundVoiceInput(String source) {
        LOG.debug("Starting background voice input from source: {}", source);

        // Check if voice input is enabled
        if (!preferenceHelper.isVoiceInputEnabled()) {
            LOG.debug("Voice input is disabled, trying audio recording instead");
            return startBackgroundAudioRecording(source);
        }

        // Check if app is in foreground
        if (isAppInForeground()) {
            LOG.debug("App is in foreground, using traditional voice input");
            return startForegroundVoiceInput(source);
        }

        // For background operation with voice input enabled, try to use voice recognition
        LOG.info("App is in background, attempting background voice recognition from source: {}", source);
        return startBackgroundVoiceRecognition(source);
    }
    
    /**
     * Start foreground voice input (traditional approach)
     */
    private boolean startForegroundVoiceInput(String source) {
        try {
            // Post event to trigger traditional voice input
            EventBus.getDefault().post(new CommandEvents.RequestQuickVoiceInput(source));
            LOG.debug("Posted RequestQuickVoiceInput event for foreground operation");
            return true;
        } catch (Exception e) {
            LOG.error("Failed to start foreground voice input", e);
            return false;
        }
    }

    /**
     * Start background voice recognition (brings app to foreground for voice input)
     */
    private boolean startBackgroundVoiceRecognition(String source) {
        try {
            LOG.info("Starting background voice recognition, will bring app to foreground");

            // Post event to trigger voice input - this will bring the app to foreground
            EventBus.getDefault().post(new CommandEvents.RequestQuickVoiceInput(source));

            // Provide audio feedback for voice input
            com.mendhak.gpslogger.ui.components.AudioFeedbackManager.getInstance(context)
                .playButtonFeedback(com.mendhak.gpslogger.ui.components.AudioFeedbackManager.BUTTON_TYPE_VOICE_INPUT);

            LOG.debug("Posted RequestQuickVoiceInput event for background voice recognition");
            return true;
        } catch (Exception e) {
            LOG.error("Failed to start background voice recognition", e);
            // Fallback to audio recording if voice recognition fails
            LOG.debug("Falling back to audio recording mode");
            return startBackgroundAudioRecording(source);
        }
    }
    
    /**
     * Start background audio recording for voice input
     */
    private boolean startBackgroundAudioRecording(String source) {
        try {
            // Check audio recording permission
            if (!hasAudioRecordingPermission()) {
                LOG.warn("Audio recording permission not granted");
                showError("需要录音权限才能进行语音输入");
                return false;
            }
            
            // Create audio recording manager with listener
            AudioRecordingManager recordingManager = new AudioRecordingManager(context, new AudioRecordingManager.AudioRecordingListener() {
                @Override
                public void onRecordingStarted() {
                    LOG.info("Background audio recording started for voice input from source: {}", source);
                    showFeedback("开始语音录制...");
                }

                @Override
                public void onRecordingFinished(String audioFilePath) {
                    LOG.info("Background audio recording completed: {}", audioFilePath);
                    handleAudioRecordingResult(audioFilePath, source);
                }

                @Override
                public void onRecordingError(String error) {
                    LOG.error("Background audio recording error: {}", error);
                    showError("语音录制失败: " + error);
                }

                @Override
                public void onRecordingCancelled() {
                    LOG.debug("Background audio recording cancelled");
                    showFeedback("语音录制已取消");
                }

                @Override
                public void onRecordingProgress(long durationMs) {
                    // Optional: could show progress feedback
                }

                @Override
                public void onSilenceDetected(long silenceDurationMs) {
                    LOG.debug("Silence detected for {}ms during background recording", silenceDurationMs);
                }
            });

            // Standardize the group name for consistent processing
            String standardizedGroupName = ExternalDeviceGroupNameStandardizer.standardizeGroupName(source);
            LOG.debug("Standardized group name: {} -> {}", source, standardizedGroupName);

            // Start audio recording with context and standardized group name
            boolean started = recordingManager.startRecordingWithSource(
                "语音输入", // voiceText
                "后台语音输入", // buttonName
                0, // buttonIndex
                standardizedGroupName, // groupName - 使用标准化的组名
                null, // location - will be determined by recording manager
                source // original source for logging and conflict resolution
            );
            
            if (started) {
                LOG.info("Background audio recording started successfully for source: {}", source);
                return true;
            } else {
                LOG.warn("Failed to start background audio recording");
                showError("无法启动语音录制");
                return false;
            }
            
        } catch (Exception e) {
            LOG.error("Error starting background audio recording", e);
            showError("启动语音录制时出错");
            return false;
        }
    }
    
    /**
     * Handle audio recording result
     */
    private void handleAudioRecordingResult(String audioFilePath, String source) {
        if (audioFilePath == null || audioFilePath.trim().isEmpty()) {
            LOG.warn("Audio recording result is empty");
            showError("语音录制文件为空");
            return;
        }
        
        try {
            // For now, create a simple annotation with audio file reference
            // In the future, this could be enhanced with speech-to-text processing
            String annotationText = "语音录制 - " + source + " (" + System.currentTimeMillis() + ")";
            String buttonName = "后台语音输入";
            int buttonIndex = 0;
            String groupName = source;
            
            // Post annotation event
            EventBus.getDefault().post(new CommandEvents.Annotate(annotationText, audioFilePath,
                                                                  buttonName, buttonIndex, groupName));
            
            LOG.info("Posted CommandEvents.Annotate for background voice input: '{}', audio: '{}', source: '{}'",
                    annotationText, audioFilePath, source);
            
            showFeedback("语音注释已添加: " + annotationText);
            
        } catch (Exception e) {
            LOG.error("Error processing audio recording result", e);
            showError("处理语音录制结果时出错");
        }
    }
    
    /**
     * Check if app is currently in foreground
     */
    private boolean isAppInForeground() {
        try {
            ActivityManager activityManager = (ActivityManager) context.getSystemService(Context.ACTIVITY_SERVICE);
            if (activityManager == null) {
                return false;
            }
            
            ActivityManager.RunningAppProcessInfo appProcessInfo = new ActivityManager.RunningAppProcessInfo();
            ActivityManager.getMyMemoryState(appProcessInfo);
            
            boolean inForeground = appProcessInfo.importance == ActivityManager.RunningAppProcessInfo.IMPORTANCE_FOREGROUND;
            LOG.debug("App foreground status: {}", inForeground);
            return inForeground;
            
        } catch (Exception e) {
            LOG.warn("Failed to check app foreground status", e);
            return false;
        }
    }
    
    /**
     * Check if audio recording permission is granted using unified permission manager
     */
    private boolean hasAudioRecordingPermission() {
        AudioPermissionManager.PermissionResult result =
            AudioPermissionManager.checkRecordAudioPermission(context, "BackgroundVoiceInputManager");

        AudioPermissionManager.logPermissionResult(result);
        return result.granted;
    }
    
    /**
     * Show error message to user
     */
    private void showError(String message) {
        try {
            Toast.makeText(context, message, Toast.LENGTH_LONG).show();
        } catch (Exception e) {
            LOG.warn("Failed to show error message: {}", message, e);
        }
    }
    
    /**
     * Show feedback message to user
     */
    private void showFeedback(String message) {
        try {
            Toast.makeText(context, message, Toast.LENGTH_SHORT).show();
        } catch (Exception e) {
            LOG.warn("Failed to show feedback message: {}", message, e);
        }
    }
    
    /**
     * Check if background voice input is supported
     * @return true if supported, false otherwise
     */
    public static boolean isBackgroundVoiceInputSupported() {
        // Background voice input is supported on all Android versions
        // The implementation may vary based on available features
        return true;
    }
    
    /**
     * Get background voice input status description
     * @param context Android context
     * @return human-readable status description
     */
    public static String getBackgroundVoiceInputStatus(Context context) {
        PreferenceHelper helper = PreferenceHelper.getInstance();
        
        if (!helper.isVoiceInputEnabled()) {
            return "语音输入已禁用";
        }
        
        BackgroundVoiceInputManager manager = new BackgroundVoiceInputManager(context);
        if (!manager.hasAudioRecordingPermission()) {
            return "缺少录音权限";
        }
        
        return "可用";
    }
}
