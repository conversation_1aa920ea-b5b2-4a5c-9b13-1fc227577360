/*
 * Copyright (C) 2016 mendhak
 *
 * This file is part of GPSLogger for Android.
 *
 * GPSLogger for Android is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 2 of the License, or
 * (at your option) any later version.
 *
 * GPSLogger for Android is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with GPSLogger for Android.  If not, see <http://www.gnu.org/licenses/>.
 */

package com.mendhak.gpslogger.ui.preferences;

import android.content.Context;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.EditText;
import android.widget.Toast;
import androidx.appcompat.app.AlertDialog;
import androidx.preference.Preference;
import com.mendhak.gpslogger.R;
import com.mendhak.gpslogger.common.PreferenceHelper;
import com.mendhak.gpslogger.common.slf4j.Logs;
import com.mendhak.gpslogger.senders.ScheduledSendManager;
import org.slf4j.Logger;

import java.util.List;

/**
 * Custom preference for setting scheduled send times
 */
public class ScheduledTimesPreference extends Preference {

    private static final Logger LOG = Logs.of(ScheduledTimesPreference.class);
    private PreferenceHelper preferenceHelper;

    public ScheduledTimesPreference(Context context, AttributeSet attrs, int defStyleAttr, int defStyleRes) {
        super(context, attrs, defStyleAttr, defStyleRes);
        init();
    }

    public ScheduledTimesPreference(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init();
    }

    public ScheduledTimesPreference(Context context, AttributeSet attrs) {
        super(context, attrs);
        init();
    }

    public ScheduledTimesPreference(Context context) {
        super(context);
        init();
    }

    private void init() {
        try {
            preferenceHelper = PreferenceHelper.getInstance();
            updateSummary();

            setOnPreferenceClickListener(preference -> {
                showScheduledTimesDialog();
                return true; // Consume the click event
            });
        } catch (Exception e) {
            LOG.error("Error initializing ScheduledTimesPreference", e);
        }
    }
    
    private void showScheduledTimesDialog() {
        try {
            // Create a simple input dialog without custom layout to avoid issues
            AlertDialog.Builder builder = new AlertDialog.Builder(getContext());

            // Create EditText programmatically
            final EditText editText = new EditText(getContext());
            editText.setHint("请输入时间，格式为HH:mm，多个时间用逗号分隔\n(例如：08:00, 14:30, 18:00)");
            editText.setText(preferenceHelper.getScheduledSendTimes());
            editText.setInputType(android.text.InputType.TYPE_CLASS_TEXT);
            editText.setSingleLine(false);
            editText.setMaxLines(3);

            // Set padding for better appearance
            int padding = (int) (16 * getContext().getResources().getDisplayMetrics().density);
            editText.setPadding(padding, padding, padding, padding);

            builder.setTitle("设置发送时间")
                    .setView(editText)
                    .setPositiveButton("确定", (dialog, which) -> {
                        String timesText = editText.getText().toString().trim();

                        if (validateTimesInput(timesText)) {
                            // Save the times
                            preferenceHelper.setScheduledSendTimes(timesText);

                            // Update the alarms
                            ScheduledSendManager.setupScheduledSendAlarms();

                            // Update summary
                            updateSummary();

                            // Show success message
                            Toast.makeText(getContext(), "定时发送时间已设置", Toast.LENGTH_SHORT).show();

                            LOG.info("Scheduled send times updated: {}", timesText);
                        } else {
                            // Show error message
                            Toast.makeText(getContext(), "时间格式无效，请使用HH:mm格式", Toast.LENGTH_LONG).show();
                        }
                    })
                    .setNegativeButton("取消", null)
                    .show();

        } catch (Exception e) {
            LOG.error("Error showing scheduled times dialog", e);
            Toast.makeText(getContext(), "显示对话框时出错", Toast.LENGTH_SHORT).show();
        }
    }
    
    /**
     * Validate the times input format
     */
    private boolean validateTimesInput(String timesText) {
        if (timesText == null || timesText.trim().isEmpty()) {
            return true; // Empty is valid (disables scheduled sending)
        }
        
        String[] times = timesText.split(",");
        for (String time : times) {
            String trimmed = time.trim();
            if (!trimmed.isEmpty() && !isValidTimeFormat(trimmed)) {
                return false;
            }
        }
        
        return true;
    }
    
    /**
     * Validate individual time format (HH:mm)
     */
    private boolean isValidTimeFormat(String time) {
        if (time == null || time.length() != 5) {
            return false;
        }
        
        try {
            String[] parts = time.split(":");
            if (parts.length != 2) {
                return false;
            }
            
            int hour = Integer.parseInt(parts[0]);
            int minute = Integer.parseInt(parts[1]);
            
            return hour >= 0 && hour <= 23 && minute >= 0 && minute <= 59;
        } catch (NumberFormatException e) {
            return false;
        }
    }
    
    /**
     * Update the preference summary
     */
    private void updateSummary() {
        try {
            List<String> times = preferenceHelper.getScheduledSendTimesList();

            if (times.isEmpty()) {
                setSummary(getContext().getString(R.string.autosend_scheduled_disabled));
            } else {
                String nextSend = ScheduledSendManager.getNextScheduledSendTime();
                setSummary(String.format(getContext().getString(R.string.autosend_scheduled_next_send), nextSend));
            }
        } catch (Exception e) {
            LOG.error("Error updating summary", e);
            setSummary("Error loading scheduled times");
        }
    }
    
}
