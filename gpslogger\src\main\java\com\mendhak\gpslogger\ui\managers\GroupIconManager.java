/*
 * Copyright (C) 2024 mendhak
 *
 * This file is part of GPSLogger for Android.
 *
 * GPSLogger is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 2 of the License, or
 * (at your option) any later version.
 *
 * GPSLogger is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with GPSLogger.  If not, see <http://www.gnu.org/licenses/>.
 */

package com.mendhak.gpslogger.ui.managers;

import com.mendhak.gpslogger.R;
import com.mendhak.gpslogger.ui.models.GroupIcon;

import java.util.ArrayList;
import java.util.List;

/**
 * 分组图标管理器
 */
public class GroupIconManager {
    
    private static final String DEFAULT_ICON_ID = "folder";
    
    /**
     * 获取所有可用的预设图标
     */
    public static List<GroupIcon> getAvailableIcons() {
        List<GroupIcon> icons = new ArrayList<>();
        
        // 文件夹类图标
        icons.add(new GroupIcon(R.drawable.ic_folder_24dp, "文件夹", "folder"));
        icons.add(new GroupIcon(R.drawable.ic_folder_open_24dp, "打开文件夹", "folder_open"));
        
        // 位置类图标
        icons.add(new GroupIcon(R.drawable.ic_place_24dp, "位置", "place"));
        icons.add(new GroupIcon(R.drawable.ic_navigation_24dp, "导航", "navigation"));
        icons.add(new GroupIcon(R.drawable.ic_home_24dp, "家", "home"));
        
        // 工作类图标
        icons.add(new GroupIcon(R.drawable.ic_work_24dp, "工作", "work"));
        icons.add(new GroupIcon(R.drawable.ic_apps_24dp, "应用", "apps"));
        
        // 标记类图标
        icons.add(new GroupIcon(R.drawable.ic_bookmark_24dp, "书签", "bookmark"));
        icons.add(new GroupIcon(R.drawable.ic_label_24dp, "标签", "label"));
        icons.add(new GroupIcon(R.drawable.ic_star_24dp, "星标", "star"));
        icons.add(new GroupIcon(R.drawable.ic_favorite_24dp, "收藏", "favorite"));
        
        // 其他图标
        icons.add(new GroupIcon(R.drawable.ic_info_24dp, "信息", "info"));
        icons.add(new GroupIcon(R.drawable.ic_settings, "设置", "settings"));
        icons.add(new GroupIcon(R.drawable.ic_help, "帮助", "help"));
        icons.add(new GroupIcon(R.drawable.ic_add_24dp, "添加", "add"));
        icons.add(new GroupIcon(R.drawable.ic_edit_24dp, "编辑", "edit"));
        icons.add(new GroupIcon(R.drawable.ic_delete_24dp, "删除", "delete"));
        
        return icons;
    }
    
    /**
     * 根据图标ID获取图标
     */
    public static GroupIcon getIconById(String iconId) {
        if (iconId == null || iconId.isEmpty()) {
            iconId = DEFAULT_ICON_ID;
        }
        
        List<GroupIcon> icons = getAvailableIcons();
        for (GroupIcon icon : icons) {
            if (iconId.equals(icon.getIconId())) {
                return icon;
            }
        }
        
        // 如果找不到，返回默认图标
        return getDefaultIcon();
    }
    
    /**
     * 获取默认图标
     */
    public static GroupIcon getDefaultIcon() {
        return new GroupIcon(R.drawable.ic_folder_24dp, "文件夹", DEFAULT_ICON_ID);
    }
    
    /**
     * 获取默认图标ID
     */
    public static String getDefaultIconId() {
        return DEFAULT_ICON_ID;
    }
}
