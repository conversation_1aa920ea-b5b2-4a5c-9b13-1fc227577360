/*
 * Copyright (C) 2024 mendhak
 *
 * This file is part of GPSLogger for Android.
 *
 * GPSLogger is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 2 of the License, or
 * (at your option) any later version.
 *
 * GPSLogger is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with GPSLogger.  If not, see <http://www.gnu.org/licenses/>.
 */

package com.mendhak.gpslogger.ui.components.external;

import android.content.Context;
import android.content.SharedPreferences;

import com.mendhak.gpslogger.common.PreferenceHelper;
import com.mendhak.gpslogger.common.slf4j.Logs;
import com.mendhak.gpslogger.ui.components.ButtonGroup;
import com.mendhak.gpslogger.ui.components.ButtonGroupManager;
import com.mendhak.gpslogger.ui.fragments.display.AnnotationViewFragment.ButtonWrapper;
import com.mendhak.gpslogger.ui.fragments.display.AnnotationViewFragment.SecondaryButton;
import com.mendhak.gpslogger.common.events.CommandEvents;
import de.greenrobot.event.EventBus;

import org.json.JSONArray;
import org.json.JSONObject;
import org.slf4j.Logger;

import java.util.ArrayList;
import java.util.List;

/**
 * Manages dynamic button mapping for external devices
 * Supports both primary and secondary buttons
 */
public class DynamicButtonMapper {
    
    private static final Logger LOG = Logs.of(DynamicButtonMapper.class);
    private static final String PREFS_NAME = "dynamic_button_mapping";
    
    /**
     * Button mapping entry
     */
    public static class ButtonMapping {
        public String displayName;
        public String buttonName;
        public String primaryButtonName; // null for primary buttons
        public boolean isSecondaryButton;
        
        public ButtonMapping(String displayName, String buttonName, String primaryButtonName, boolean isSecondaryButton) {
            this.displayName = displayName;
            this.buttonName = buttonName;
            this.primaryButtonName = primaryButtonName;
            this.isSecondaryButton = isSecondaryButton;
        }
        
        @Override
        public String toString() {
            return displayName;
        }
    }
    
    /**
     * Get all available button mappings (primary and secondary buttons)
     */
    public static List<ButtonMapping> getAllButtonMappings(Context context) {
        List<ButtonMapping> mappings = new ArrayList<>();
        
        if (context == null) {
            return mappings;
        }
        
        try {
            // Get button settings directly from PreferenceHelper
            PreferenceHelper preferenceHelper = PreferenceHelper.getInstance();
            String settings = preferenceHelper.getAnnotationButtonSettings();

            if (settings == null || settings.isEmpty()) {
                LOG.debug("No annotation button settings found");
                return mappings;
            }

            JSONObject settingsObject = new JSONObject(settings);

            // Check if this is the new format with groups and secondary buttons
            int version = settingsObject.optInt("version", 0);

            if (version >= 2) {
                // New format with groups and secondary buttons
                JSONArray buttonsArray = settingsObject.optJSONArray("buttons");
                if (buttonsArray != null) {
                    for (int i = 0; i < buttonsArray.length(); i++) {
                        JSONObject buttonJson = buttonsArray.getJSONObject(i);
                        // Use 'label' field for button name (consistent with old format)
                        String buttonText = buttonJson.optString("label", "按钮" + (i + 1));

                        // Add primary button with proper display name
                        String primaryDisplayName = "一级按钮：" + buttonText;
                        mappings.add(new ButtonMapping(primaryDisplayName, buttonText, null, false));

                        // Add secondary buttons if they exist
                        JSONArray secondaryArray = buttonJson.optJSONArray("secondaryButtons");
                        if (secondaryArray != null) {
                            for (int j = 0; j < secondaryArray.length(); j++) {
                                JSONObject secondaryJson = secondaryArray.getJSONObject(j);
                                String secondaryText = secondaryJson.optString("text", "二级按钮" + (j + 1));
                                boolean isEnabled = secondaryJson.optBoolean("isEnabled", true);

                                if (isEnabled) {
                                    String secondaryDisplayName = "二级按钮：" + buttonText + " > " + secondaryText;
                                    mappings.add(new ButtonMapping(secondaryDisplayName, secondaryText, buttonText, true));
                                }
                            }
                        }
                    }
                }
            } else {
                // Old format - only primary buttons
                JSONArray buttonsArray = settingsObject.optJSONArray("buttons");
                if (buttonsArray != null) {
                    for (int i = 0; i < buttonsArray.length(); i++) {
                        JSONObject buttonJson = buttonsArray.getJSONObject(i);
                        String buttonText = buttonJson.optString("label", "按钮" + (i + 1));
                        String primaryDisplayName = "一级按钮：" + buttonText;
                        mappings.add(new ButtonMapping(primaryDisplayName, buttonText, null, false));
                    }
                }
            }

            LOG.debug("Found {} button mappings from settings (version {})", mappings.size(), version);

        } catch (Exception e) {
            LOG.error("Error parsing annotation button settings", e);
        }
        
        return mappings;
    }
    
    /**
     * Save button mapping for external device
     */
    public static void saveButtonMapping(Context context, String deviceKey, String mappingKey, ButtonMapping mapping) {
        if (context == null || deviceKey == null || mappingKey == null) {
            return;
        }
        
        SharedPreferences prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE);
        SharedPreferences.Editor editor = prefs.edit();
        
        String prefKey = deviceKey + "_" + mappingKey;
        if (mapping != null) {
            // Save mapping as JSON-like string
            String mappingStr = mapping.buttonName + "|" + 
                               (mapping.primaryButtonName != null ? mapping.primaryButtonName : "") + "|" + 
                               mapping.isSecondaryButton;
            editor.putString(prefKey, mappingStr);
        } else {
            editor.remove(prefKey);
        }
        
        editor.apply();
        LOG.debug("Saved button mapping: {} -> {}", prefKey, mapping != null ? mapping.displayName : "null");
    }
    
    /**
     * Load button mapping for external device
     */
    public static ButtonMapping loadButtonMapping(Context context, String deviceKey, String mappingKey) {
        if (context == null || deviceKey == null || mappingKey == null) {
            return null;
        }
        
        SharedPreferences prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE);
        String prefKey = deviceKey + "_" + mappingKey;
        String mappingStr = prefs.getString(prefKey, null);
        
        if (mappingStr == null || mappingStr.isEmpty()) {
            return null;
        }
        
        try {
            String[] parts = mappingStr.split("\\|");
            if (parts.length >= 3) {
                String buttonName = parts[0];
                String primaryButtonName = parts[1].isEmpty() ? null : parts[1];
                boolean isSecondaryButton = Boolean.parseBoolean(parts[2]);
                
                String displayName = isSecondaryButton ? 
                    (primaryButtonName + " > " + buttonName) : buttonName;
                
                return new ButtonMapping(displayName, buttonName, primaryButtonName, isSecondaryButton);
            }
        } catch (Exception e) {
            LOG.error("Error parsing button mapping: {}", mappingStr, e);
        }
        
        return null;
    }
    
    /**
     * Trigger button by mapping
     */
    public static boolean triggerButton(Context context, ButtonMapping mapping) {
        if (context == null || mapping == null) {
            return false;
        }
        
        try {
            if (mapping.isSecondaryButton) {
                // Trigger secondary button
                return triggerSecondaryButton(context, mapping.primaryButtonName, mapping.buttonName);
            } else {
                // Trigger primary button
                return triggerPrimaryButton(context, mapping.buttonName);
            }
        } catch (Exception e) {
            LOG.error("Error triggering button: {}", mapping.displayName, e);
            return false;
        }
    }

    /**
     * Trigger button by action name (used by external device mapping)
     */
    public static boolean triggerButtonByActionName(Context context, String actionName) {
        LOG.info("=== DYNAMIC BUTTON MAPPER DEBUG ===");
        LOG.info("Triggering button by action name: {}", actionName);

        if (actionName == null || context == null) {
            LOG.error("Invalid parameters: actionName={}, context={}", actionName, context);
            return false;
        }

        // Find the button mapping that corresponds to this action name
        List<ButtonMapping> allMappings = getAllButtonMappings(context);
        LOG.info("Found {} button mappings", allMappings.size());

        for (ButtonMapping mapping : allMappings) {
            String mappingActionName;
            if (mapping.isSecondaryButton) {
                mappingActionName = "DYNAMIC_SECONDARY_" + mapping.primaryButtonName.hashCode() + "_" + mapping.buttonName.hashCode();
                LOG.debug("Checking secondary button mapping: {} -> {}", mapping.buttonName, mappingActionName);
            } else {
                mappingActionName = "DYNAMIC_PRIMARY_" + mapping.buttonName.hashCode();
                LOG.debug("Checking primary button mapping: {} -> {}", mapping.buttonName, mappingActionName);
            }

            if (mappingActionName.equals(actionName)) {
                LOG.info("Found matching button mapping: {} (isSecondary: {})", mapping.buttonName, mapping.isSecondaryButton);
                LOG.info("Found matching button mapping for action {}: {}", actionName, mapping.displayName);
                return triggerButton(context, mapping);
            }
        }

        LOG.warn("No button mapping found for action name: {}", actionName);
        return false;
    }
    
    /**
     * Trigger primary button
     */
    private static boolean triggerPrimaryButton(Context context, String buttonName) {
        // This will be handled by existing annotation button trigger system
        // Post event to request annotation button by name
        de.greenrobot.event.EventBus.getDefault().post(
            new com.mendhak.gpslogger.common.events.CommandEvents.RequestAnnotationButtonByName(buttonName));
        return true;
    }

    /**
     * Trigger secondary button
     */
    private static boolean triggerSecondaryButton(Context context, String primaryButtonName, String secondaryButtonName) {
        LOG.info("=== TRIGGER SECONDARY BUTTON DEBUG ===");
        LOG.info("Triggering secondary button: {} > {}", primaryButtonName, secondaryButtonName);

        // Post event to request secondary button trigger
        com.mendhak.gpslogger.common.events.CommandEvents.RequestSecondaryButton event =
            new com.mendhak.gpslogger.common.events.CommandEvents.RequestSecondaryButton(primaryButtonName, secondaryButtonName);

        LOG.info("Posting RequestSecondaryButton event to EventBus");
        de.greenrobot.event.EventBus.getDefault().post(event);

        LOG.info("Successfully posted secondary button trigger event");
        return true;
    }
}
