/*
 * Copyright (C) 2016 mendhak
 *
 * This file is part of GPSLogger for Android.
 *
 * GPSLogger for Android is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 2 of the License, or
 * (at your option) any later version.
 *
 * GPSLogger for Android is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with GPSLogger for Android.  If not, see <http://www.gnu.org/licenses/>.
 */

package com.mendhak.gpslogger.senders;

import android.app.AlarmManager;
import android.app.PendingIntent;
import android.content.Context;
import android.content.Intent;
import android.os.Build;
import com.mendhak.gpslogger.common.AppSettings;
import com.mendhak.gpslogger.common.PreferenceHelper;
import com.mendhak.gpslogger.common.slf4j.Logs;
import org.slf4j.Logger;

import java.util.Calendar;
import java.util.List;

/**
 * Manager for scheduled sending functionality
 * Handles setting up and managing AlarmManager for specific time-based sending
 */
public class ScheduledSendManager {
    
    private static final Logger LOG = Logs.of(ScheduledSendManager.class);
    private static final int SCHEDULED_SEND_REQUEST_CODE_BASE = 10000;
    
    /**
     * Setup all scheduled send alarms based on user preferences
     */
    public static void setupScheduledSendAlarms() {
        PreferenceHelper preferenceHelper = PreferenceHelper.getInstance();
        
        if (!preferenceHelper.isScheduledSendEnabled()) {
            LOG.debug("Scheduled sending is disabled, clearing all alarms");
            clearAllScheduledSendAlarms();
            return;
        }
        
        List<String> scheduledTimes = preferenceHelper.getScheduledSendTimesList();
        if (scheduledTimes.isEmpty()) {
            LOG.debug("No scheduled times configured, clearing all alarms");
            clearAllScheduledSendAlarms();
            return;
        }
        
        LOG.info("Setting up {} scheduled send alarms", scheduledTimes.size());
        
        // Clear existing alarms first
        clearAllScheduledSendAlarms();
        
        // Set up new alarms for each scheduled time
        for (int i = 0; i < scheduledTimes.size(); i++) {
            String time = scheduledTimes.get(i);
            setupSingleScheduledAlarm(time, i);
        }
    }
    
    /**
     * Setup a single scheduled alarm for a specific time
     */
    private static void setupSingleScheduledAlarm(String time, int index) {
        try {
            String[] timeParts = time.split(":");
            int hour = Integer.parseInt(timeParts[0]);
            int minute = Integer.parseInt(timeParts[1]);
            
            Calendar calendar = Calendar.getInstance();
            calendar.set(Calendar.HOUR_OF_DAY, hour);
            calendar.set(Calendar.MINUTE, minute);
            calendar.set(Calendar.SECOND, 0);
            calendar.set(Calendar.MILLISECOND, 0);
            
            // If the time has already passed today, schedule for tomorrow
            if (calendar.getTimeInMillis() <= System.currentTimeMillis()) {
                calendar.add(Calendar.DAY_OF_MONTH, 1);
            }
            
            Context context = AppSettings.getInstance().getApplicationContext();
            AlarmManager alarmManager = (AlarmManager) context.getSystemService(Context.ALARM_SERVICE);
            
            Intent intent = new Intent(context, ScheduledSendReceiver.class);
            intent.putExtra("scheduled_time", time);
            
            int requestCode = SCHEDULED_SEND_REQUEST_CODE_BASE + index;
            int flags = PendingIntent.FLAG_UPDATE_CURRENT;
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                flags |= PendingIntent.FLAG_MUTABLE;
            }
            
            PendingIntent pendingIntent = PendingIntent.getBroadcast(context, requestCode, intent, flags);
            
            // Use setRepeating for daily repetition
            alarmManager.setRepeating(
                AlarmManager.RTC_WAKEUP,
                calendar.getTimeInMillis(),
                AlarmManager.INTERVAL_DAY,
                pendingIntent
            );
            
            LOG.info("Scheduled send alarm set for {} (next trigger: {})", 
                time, calendar.getTime().toString());
                
        } catch (Exception e) {
            LOG.error("Failed to setup scheduled alarm for time: {}", time, e);
        }
    }
    
    /**
     * Clear all scheduled send alarms
     */
    public static void clearAllScheduledSendAlarms() {
        Context context = AppSettings.getInstance().getApplicationContext();
        AlarmManager alarmManager = (AlarmManager) context.getSystemService(Context.ALARM_SERVICE);
        
        // Clear up to 10 possible scheduled alarms (should be enough for most users)
        for (int i = 0; i < 10; i++) {
            Intent intent = new Intent(context, ScheduledSendReceiver.class);
            int requestCode = SCHEDULED_SEND_REQUEST_CODE_BASE + i;
            int flags = PendingIntent.FLAG_UPDATE_CURRENT;
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                flags |= PendingIntent.FLAG_MUTABLE;
            }
            
            PendingIntent pendingIntent = PendingIntent.getBroadcast(context, requestCode, intent, flags);
            alarmManager.cancel(pendingIntent);
        }
        
        LOG.debug("Cleared all scheduled send alarms");
    }
    
    /**
     * Get next scheduled send time as formatted string
     */
    public static String getNextScheduledSendTime() {
        PreferenceHelper preferenceHelper = PreferenceHelper.getInstance();
        
        if (!preferenceHelper.isScheduledSendEnabled()) {
            return "Disabled";
        }
        
        List<String> scheduledTimes = preferenceHelper.getScheduledSendTimesList();
        if (scheduledTimes.isEmpty()) {
            return "No times configured";
        }
        
        // Find the next scheduled time
        Calendar now = Calendar.getInstance();
        int currentHour = now.get(Calendar.HOUR_OF_DAY);
        int currentMinute = now.get(Calendar.MINUTE);
        
        String nextTime = null;
        int minTimeDiff = Integer.MAX_VALUE;
        
        for (String time : scheduledTimes) {
            try {
                String[] parts = time.split(":");
                int hour = Integer.parseInt(parts[0]);
                int minute = Integer.parseInt(parts[1]);
                
                int timeDiff = (hour * 60 + minute) - (currentHour * 60 + currentMinute);
                if (timeDiff < 0) {
                    timeDiff += 24 * 60; // Add 24 hours for next day
                }
                
                if (timeDiff < minTimeDiff) {
                    minTimeDiff = timeDiff;
                    nextTime = time;
                }
            } catch (Exception e) {
                LOG.warn("Invalid time format: {}", time);
            }
        }
        
        if (nextTime != null) {
            if (minTimeDiff < 60) {
                return "Next: " + nextTime + " (in " + minTimeDiff + " minutes)";
            } else {
                int hours = minTimeDiff / 60;
                int minutes = minTimeDiff % 60;
                return "Next: " + nextTime + " (in " + hours + "h " + minutes + "m)";
            }
        }
        
        return "No valid times";
    }
}
