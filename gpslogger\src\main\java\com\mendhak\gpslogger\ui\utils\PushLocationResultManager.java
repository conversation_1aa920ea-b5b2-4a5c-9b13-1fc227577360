/*
 * Copyright (C) 2016 mendhak
 *
 * This file is part of GPSLogger for Android.
 *
 * GPSLogger for Android is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 2 of the License, or
 * (at your option) any later version.
 *
 * GPSLogger for Android is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with GPSLogger for Android.  If not, see <http://www.gnu.org/licenses/>.
 */

package com.mendhak.gpslogger.ui.utils;

import android.content.Context;
import android.location.Location;
import com.mendhak.gpslogger.common.AppSettings;
import com.mendhak.gpslogger.common.FilePathManager;
import com.mendhak.gpslogger.common.PreferenceHelper;
import com.mendhak.gpslogger.common.slf4j.Logs;
import com.mendhak.gpslogger.ui.components.template.AnnotationTemplateEngine;
import com.mendhak.gpslogger.ui.components.template.providers.SinglePointPushVariableProvider;
import com.mendhak.gpslogger.ui.models.LocationGroup;
import com.mendhak.gpslogger.ui.models.LocationPoint;
import org.slf4j.Logger;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;

/**
 * Manager for Push_location_result.txt file operations
 * Handles dynamic recording of single point push operations
 */
public class PushLocationResultManager {
    
    private static final Logger LOG = Logs.of(PushLocationResultManager.class);
    private static final String RESULT_FILE_NAME = "Push_location_result.txt";
    
    private static PushLocationResultManager instance;
    private final AnnotationTemplateEngine templateEngine;
    private final PreferenceHelper preferenceHelper;
    
    private PushLocationResultManager() {
        this.templateEngine = AnnotationTemplateEngine.getInstance();
        this.preferenceHelper = PreferenceHelper.getInstance();
    }
    
    /**
     * Get singleton instance
     */
    public static synchronized PushLocationResultManager getInstance() {
        if (instance == null) {
            instance = new PushLocationResultManager();
        }
        return instance;
    }
    
    /**
     * Record a push operation to the result file
     */
    public void recordPushOperation(Context context, LocationGroup group, LocationPoint point,
                                  String operationType, String additionalInfo) {
        if (context == null || group == null || point == null || operationType == null) {
            LOG.warn("Invalid parameters for recording push operation");
            return;
        }

        // Check if template is enabled
        if (!preferenceHelper.isSinglePointPushTemplateEnabled()) {
            LOG.debug("Single point push template is disabled, skipping record");
            return;
        }

        try {
            // Set current push original info for template variables
            String originalInfo = point.getLatitude() + "," + point.getLongitude() + "_" + point.getDescription();
            SinglePointPushVariableProvider.setCurrentPushOriginalInfo(context, originalInfo);

            // Note: Do not set button mode variables here - they should be set when user actually inputs text/voice
            // The variables {input_text} and {voice_text} should reflect actual user input, not operation descriptions

            // Get the result file
            File resultFile = getResultFile(context);

            // Ensure parent directory exists
            File parentDir = resultFile.getParentFile();
            if (parentDir != null && !parentDir.exists()) {
                boolean created = parentDir.mkdirs();
                if (!created) {
                    LOG.error("Failed to create parent directory: {}", parentDir.getAbsolutePath());
                    return;
                }
            }

            // Generate record content using template (without timestamp prefix)
            String recordContent = generateRecordContent(context, group, point, operationType, additionalInfo);

            // Append to file
            appendToResultFile(resultFile, recordContent);

            LOG.info("Recorded push operation: {} for point {} in group {}",
                    operationType, point.getDescription(), group.getGroupName());

        } catch (Exception e) {
            LOG.error("Error recording push operation", e);
        }
    }
    
    /**
     * Get the result file for current date
     */
    private File getResultFile(Context context) throws Exception {
        return FilePathManager.getLogFilePath(context, "Push_location_result", ".txt");
    }
    
    /**
     * Generate record content using single point push template
     */
    private String generateRecordContent(Context context, LocationGroup group, LocationPoint point,
                                       String operationType, String additionalInfo) {

        // Get single point push template
        String template = preferenceHelper.getSinglePointPushTemplate();

        if (template == null || template.trim().isEmpty()) {
            // Use default template if none configured
            template = getDefaultTemplate();
        }

        // Create location object for template processing
        Location location = createLocationFromPoint(point);

        // Process template with single point push context
        String processedContent = templateEngine.processTemplateString(
            template, context, location, additionalInfo,
            operationType, 0, group.getGroupName()
        );

        // Return processed content with newline only (no timestamp prefix)
        return processedContent + "\n";
    }
    
    /**
     * Create Location object from LocationPoint
     */
    private Location createLocationFromPoint(LocationPoint point) {
        Location location = new Location("SinglePointPush");
        location.setLatitude(point.getLatitude());
        location.setLongitude(point.getLongitude());
        location.setTime(System.currentTimeMillis());
        return location;
    }
    
    /**
     * Append content to result file
     */
    private void appendToResultFile(File resultFile, String content) throws IOException {
        try (FileWriter writer = new FileWriter(resultFile, true)) {
            writer.write(content);
            writer.flush();
        }
    }
    
    /**
     * Get default template for single point push recording
     */
    private String getDefaultTemplate() {
        return "{primary_button_name}: {push_original_info} | 分组: {group_name} | " +
               "统计: {pushed_points_count}/{total_push_points} | " +
               "通过: {pass_count} | 不过: {fail_count}";
    }
    
    /**
     * Initialize result file if it doesn't exist (no header)
     */
    public void initializeResultFile(Context context) {
        try {
            File resultFile = getResultFile(context);

            if (!resultFile.exists()) {
                // Create empty file without header
                File parentDir = resultFile.getParentFile();
                if (parentDir != null && !parentDir.exists()) {
                    parentDir.mkdirs();
                }

                // Create empty file
                boolean created = resultFile.createNewFile();
                if (created) {
                    LOG.info("Initialized empty result file: {}", resultFile.getAbsolutePath());
                } else {
                    LOG.warn("Failed to create result file: {}", resultFile.getAbsolutePath());
                }
            }
        } catch (Exception e) {
            LOG.error("Error initializing result file", e);
        }
    }
    
    /**
     * Clear result file content
     */
    public void clearResultFile(Context context) {
        try {
            File resultFile = getResultFile(context);

            if (resultFile.exists()) {
                boolean deleted = resultFile.delete();
                if (deleted) {
                    LOG.info("Cleared result file: {}", resultFile.getAbsolutePath());
                    // Reinitialize empty file (no header)
                    initializeResultFile(context);
                } else {
                    LOG.warn("Failed to delete result file: {}", resultFile.getAbsolutePath());
                }
            }
        } catch (Exception e) {
            LOG.error("Error clearing result file", e);
        }
    }
    
    /**
     * Get result file path for display
     */
    public String getResultFilePath(Context context) {
        try {
            File resultFile = getResultFile(context);
            return resultFile.getAbsolutePath();
        } catch (Exception e) {
            LOG.error("Error getting result file path", e);
            return "Error getting file path";
        }
    }
    
    /**
     * Check if result file exists
     */
    public boolean resultFileExists(Context context) {
        try {
            File resultFile = getResultFile(context);
            return resultFile.exists();
        } catch (Exception e) {
            LOG.error("Error checking if result file exists", e);
            return false;
        }
    }
    
    /**
     * Record push operation with location
     */
    public void recordPushWithLocation(Context context, LocationGroup group, LocationPoint point, Location currentLocation) {
        recordPushOperation(context, group, point, "推送", 
                          currentLocation != null ? "当前位置: " + currentLocation.getLatitude() + "," + currentLocation.getLongitude() : "");
    }
    
    /**
     * Record pass operation
     */
    public void recordPassOperation(Context context, LocationGroup group, LocationPoint point, String additionalInfo) {
        recordPushOperation(context, group, point, "通过", additionalInfo != null ? additionalInfo : "");
    }
    
    /**
     * Record fail operation
     */
    public void recordFailOperation(Context context, LocationGroup group, LocationPoint point, String additionalInfo) {
        recordPushOperation(context, group, point, "不过", additionalInfo != null ? additionalInfo : "");
    }
    
    /**
     * Record sequential push operation
     */
    public void recordSequentialPushOperation(Context context, LocationGroup group, LocationPoint point, int sequenceIndex) {
        recordPushOperation(context, group, point, "顺序推送", "序号: " + (sequenceIndex + 1));
    }


}
