/*
 * Copyright (C) 2016 mendhak
 *
 * This file is part of GPSLogger for Android.
 *
 * GPSLogger for Android is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 2 of the License, or
 * (at your option) any later version.
 *
 * GPSLogger for Android is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with GPSLogger for Android.  If not, see <http://www.gnu.org/licenses/>.
 */

package com.mendhak.gpslogger.ui.dialogs;

import android.app.Dialog;
import android.content.Context;
import android.graphics.Color;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.*;
import androidx.appcompat.app.AlertDialog;
// import com.google.android.material.textfield.TextInputEditText;

import com.mendhak.gpslogger.R;
import com.mendhak.gpslogger.ui.fragments.display.AnnotationViewFragment.ButtonWrapper;
import com.mendhak.gpslogger.ui.components.LayoutEnums.TriggerMode;
import com.mendhak.gpslogger.ui.components.SecondaryButtonManager;

/**
 * 增强的按钮编辑对话框
 * 支持基本按钮设置和二级按钮层次结构配置
 */
public class EnhancedButtonEditDialog {
    
    public interface OnButtonEditListener {
        void onButtonSaved(ButtonWrapper button);
        void onButtonCancelled();
        void onAddSecondaryButton(ButtonWrapper primaryButton);
        void onManageSecondaryButtons(ButtonWrapper primaryButton);
    }
    
    private Context context;
    private ButtonWrapper button;
    private OnButtonEditListener listener;
    private Dialog dialog;
    
    // UI组件
    private EditText editButtonText;
    private View colorPreview;
    private Button btnChooseColor;
    private Spinner spinnerTriggerMode;
    private Switch switchPrimaryButton;
    private LinearLayout secondaryButtonSettings;
    private TextView secondaryButtonCountText;
    private Button btnAddSecondaryButton;
    private Button btnManageSecondaryButtons;

    private Button btnCancel;
    private Button btnSave;
    
    private String selectedColor;
    private TriggerMode selectedTriggerMode;
    
    public EnhancedButtonEditDialog(Context context, ButtonWrapper button, OnButtonEditListener listener) {
        this.context = context;
        this.button = button;
        this.listener = listener;
        this.selectedColor = button.getColor();
        this.selectedTriggerMode = button.getTriggerMode();
    }
    
    public void show() {
        android.util.Log.d("EnhancedButtonEditDialog", "show() called");

        if (context == null) {
            android.util.Log.e("EnhancedButtonEditDialog", "Context is null, cannot show dialog");
            return;
        }

        if (button == null) {
            android.util.Log.e("EnhancedButtonEditDialog", "Button is null, cannot show dialog");
            return;
        }

        try {
            android.util.Log.d("EnhancedButtonEditDialog", "Inflating dialog layout");
            View dialogView = LayoutInflater.from(context).inflate(R.layout.dialog_edit_button_enhanced, null);

            android.util.Log.d("EnhancedButtonEditDialog", "Initializing views");
            initializeViews(dialogView);
            setupTriggerModeSpinner();
            loadButtonData();
            setupEventListeners();

            android.util.Log.d("EnhancedButtonEditDialog", "Creating AlertDialog");
            AlertDialog.Builder builder = new AlertDialog.Builder(context);
            builder.setView(dialogView);

            dialog = builder.create();

            android.util.Log.d("EnhancedButtonEditDialog", "Showing dialog");
            dialog.show();
            android.util.Log.d("EnhancedButtonEditDialog", "Dialog shown successfully");

        } catch (Exception e) {
            android.util.Log.e("EnhancedButtonEditDialog", "Error showing dialog", e);
            throw e; // 重新抛出异常，让调用者处理
        }
    }
    
    private void initializeViews(View dialogView) {
        editButtonText = dialogView.findViewById(R.id.edit_button_text);
        colorPreview = dialogView.findViewById(R.id.color_preview);
        btnChooseColor = dialogView.findViewById(R.id.btn_choose_color);
        spinnerTriggerMode = dialogView.findViewById(R.id.spinner_trigger_mode);
        switchPrimaryButton = dialogView.findViewById(R.id.switch_primary_button);
        secondaryButtonSettings = dialogView.findViewById(R.id.secondary_button_settings);
        secondaryButtonCountText = dialogView.findViewById(R.id.secondary_button_count_text);
        btnAddSecondaryButton = dialogView.findViewById(R.id.btn_add_secondary_button);
        btnManageSecondaryButtons = dialogView.findViewById(R.id.btn_manage_secondary_buttons);

        btnCancel = dialogView.findViewById(R.id.btn_cancel);
        btnSave = dialogView.findViewById(R.id.btn_save);
    }
    
    private void setupTriggerModeSpinner() {
        String[] triggerModes = {"语音输入", "文本输入", "计数器"};
        ArrayAdapter<String> adapter = new ArrayAdapter<>(context, android.R.layout.simple_spinner_item, triggerModes);
        adapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
        spinnerTriggerMode.setAdapter(adapter);
    }
    
    private void loadButtonData() {
        // 加载基本按钮数据
        editButtonText.setText(button.getText());
        updateColorPreview();
        
        // 设置触发模式
        int triggerModeIndex = getTriggerModeIndex(button.getTriggerMode());
        spinnerTriggerMode.setSelection(triggerModeIndex);
        
        // 设置二级按钮启用状态
        switchPrimaryButton.setChecked(button.isPrimaryButton());
        updateSecondaryButtonSettings();
        

    }
    
    private int getTriggerModeIndex(TriggerMode triggerMode) {
        switch (triggerMode) {
            case VOICE_INPUT: return 0;
            case TEXT_INPUT: return 1;
            case COUNTER_ONLY: return 2;
            default: return 0;
        }
    }
    
    private TriggerMode getTriggerModeFromIndex(int index) {
        switch (index) {
            case 0: return TriggerMode.VOICE_INPUT;
            case 1: return TriggerMode.TEXT_INPUT;
            case 2: return TriggerMode.COUNTER_ONLY;
            default: return TriggerMode.VOICE_INPUT;
        }
    }
    
    private void updateColorPreview() {
        try {
            int color = Color.parseColor(selectedColor);
            colorPreview.setBackgroundColor(color);
        } catch (Exception e) {
            colorPreview.setBackgroundColor(Color.parseColor("#808080"));
        }
    }
    
    private void updateSecondaryButtonSettings() {
        boolean isPrimary = switchPrimaryButton.isChecked();
        secondaryButtonSettings.setVisibility(isPrimary ? View.VISIBLE : View.GONE);
        
        if (isPrimary) {
            int count = button.getSecondaryButtonCount();
            secondaryButtonCountText.setText("二级按钮 (" + count + ")");
        }
    }
    
    private void setupEventListeners() {
        // 颜色选择
        btnChooseColor.setOnClickListener(v -> showColorPicker());
        
        // 触发模式选择
        spinnerTriggerMode.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
            @Override
            public void onItemSelected(AdapterView<?> parent, View view, int position, long id) {
                selectedTriggerMode = getTriggerModeFromIndex(position);
            }
            
            @Override
            public void onNothingSelected(AdapterView<?> parent) {}
        });
        
        // 启用二级按钮开关
        switchPrimaryButton.setOnCheckedChangeListener((buttonView, isChecked) -> {
            updateSecondaryButtonSettings();
        });
        
        // 添加二级按钮
        btnAddSecondaryButton.setOnClickListener(v -> {
            if (listener != null) {
                listener.onAddSecondaryButton(button);
            }
            updateSecondaryButtonSettings(); // 刷新计数
        });
        
        // 管理二级按钮
        btnManageSecondaryButtons.setOnClickListener(v -> {
            if (listener != null) {
                listener.onManageSecondaryButtons(button);
            }
            updateSecondaryButtonSettings(); // 刷新计数
        });
        
        // 取消按钮
        btnCancel.setOnClickListener(v -> {
            dismiss();
            if (listener != null) {
                listener.onButtonCancelled();
            }
        });
        
        // 保存按钮
        btnSave.setOnClickListener(v -> saveButton());
    }
    
    private void showColorPicker() {
        // 简单的颜色选择器实现
        String[] colors = {"#F44336", "#E91E63", "#9C27B0", "#673AB7", "#3F51B5", 
                          "#2196F3", "#03A9F4", "#00BCD4", "#009688", "#4CAF50", 
                          "#8BC34A", "#CDDC39", "#FFEB3B", "#FFC107", "#FF9800", 
                          "#FF5722", "#795548", "#9E9E9E", "#607D8B", "#000000"};
        
        AlertDialog.Builder builder = new AlertDialog.Builder(context);
        builder.setTitle("选择颜色");
        builder.setItems(colors, (dialog, which) -> {
            selectedColor = colors[which];
            updateColorPreview();
        });
        builder.show();
    }
    
    private void saveButton() {
        String text = editButtonText.getText().toString().trim();
        if (text.isEmpty()) {
            editButtonText.setError("按钮文本不能为空");
            return;
        }
        
        // 更新按钮数据
        button.setText(text);
        button.setColor(selectedColor);
        button.setTriggerMode(selectedTriggerMode);
        button.setPrimaryButton(switchPrimaryButton.isChecked());

        
        dismiss();
        if (listener != null) {
            listener.onButtonSaved(button);
        }
    }
    
    public void dismiss() {
        if (dialog != null && dialog.isShowing()) {
            dialog.dismiss();
        }
    }
    
    public boolean isShowing() {
        return dialog != null && dialog.isShowing();
    }
}
