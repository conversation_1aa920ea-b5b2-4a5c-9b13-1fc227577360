/*
 * Copyright (C) 2024 mendhak
 *
 * This file is part of GPSLogger for Android.
 *
 * GPSLogger is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 2 of the License, or
 * (at your option) any later version.
 *
 * GPSLogger is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with GPSLogger.  If not, see <http://www.gnu.org/licenses/>.
 */

package com.mendhak.gpslogger.ui.managers;

import android.content.Context;
import android.util.Log;

import com.mendhak.gpslogger.common.PreferenceHelper;
import com.mendhak.gpslogger.ui.models.LocationPointState;

import org.json.JSONException;
import org.json.JSONObject;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 状态持久化管理器
 * 管理位置点状态的保存和加载，支持文件存储
 */
public class StateManager {
    
    private static final String TAG = "StateManager";
    private static final String STATE_FILE_NAME = "location_point_states.json";
    private static final String GROUP_ORDER_FILE_NAME = "group_order.json";

    private static StateManager instance;
    private Context context;
    private Map<String, LocationPointState> groupStates = new ConcurrentHashMap<>();
    private File stateFile;
    private File groupOrderFile;
    private java.util.List<String> groupOrder = new java.util.ArrayList<>();
    
    private StateManager(Context context) {
        this.context = context.getApplicationContext();

        // 使用应用内部存储目录，而不是用户设置的GPS日志文件目录
        File appInternalDir = this.context.getFilesDir();
        this.stateFile = new File(appInternalDir, STATE_FILE_NAME);
        this.groupOrderFile = new File(appInternalDir, GROUP_ORDER_FILE_NAME);

        Log.d(TAG, "StateManager initialized");
        Log.d(TAG, "App internal directory: " + appInternalDir.getAbsolutePath());
        Log.d(TAG, "State file path: " + stateFile.getAbsolutePath());
        Log.d(TAG, "Group order file path: " + groupOrderFile.getAbsolutePath());
        Log.d(TAG, "State file exists: " + stateFile.exists());
        Log.d(TAG, "Group order file exists: " + groupOrderFile.exists());

        // 检查并迁移旧的状态文件
        migrateOldStateFiles();

        loadStates();
        loadGroupOrder();
    }
    
    public static synchronized StateManager getInstance(Context context) {
        if (instance == null) {
            instance = new StateManager(context);
        }
        return instance;
    }
    
    /**
     * 获取指定分组的状态
     */
    public LocationPointState getGroupState(String groupName) {
        return groupStates.computeIfAbsent(groupName, k -> new LocationPointState());
    }
    
    /**
     * 保存指定分组的状态
     */
    public void saveGroupState(String groupName, LocationPointState state) {
        groupStates.put(groupName, state);
        saveStates();
    }
    
    /**
     * 更新位置点推送状态
     */
    public void updatePushStatus(String groupName, int pointIndex, boolean isPushed) {
        LocationPointState state = getGroupState(groupName);
        if (isPushed) {
            state.addPushedPoint(pointIndex);
        } else {
            state.removePushedPoint(pointIndex);
        }
        saveGroupState(groupName, state);
    }
    
    /**
     * 更新位置点通过状态
     */
    public void updatePassStatus(String groupName, int pointIndex) {
        LocationPointState state = getGroupState(groupName);
        state.addPassedPoint(pointIndex);
        saveGroupState(groupName, state);
    }
    
    /**
     * 更新位置点不通过状态
     */
    public void updateFailStatus(String groupName, int pointIndex) {
        LocationPointState state = getGroupState(groupName);
        state.addFailedPoint(pointIndex);
        saveGroupState(groupName, state);
    }
    
    /**
     * 清除位置点通过/不通过状态
     */
    public void clearPassFailStatus(String groupName, int pointIndex) {
        LocationPointState state = getGroupState(groupName);
        state.removePassFailStatus(pointIndex);
        saveGroupState(groupName, state);
    }
    
    /**
     * 清除指定分组的所有状态
     */
    public void clearGroupState(String groupName) {
        LocationPointState state = getGroupState(groupName);
        state.clear();
        saveGroupState(groupName, state);
    }
    
    /**
     * 清除所有状态
     */
    public void clearAllStates() {
        groupStates.clear();
        saveStates();
    }
    
    /**
     * 从文件加载状态
     */
    private void loadStates() {
        if (!stateFile.exists()) {
            Log.d(TAG, "状态文件不存在，使用默认状态");
            return;
        }
        
        try (FileInputStream fis = new FileInputStream(stateFile)) {
            byte[] buffer = new byte[(int) stateFile.length()];
            fis.read(buffer);
            String jsonString = new String(buffer, "UTF-8");
            
            JSONObject rootJson = new JSONObject(jsonString);
            JSONObject groupsJson = rootJson.optJSONObject("groups");
            
            if (groupsJson != null) {
                Iterator<String> keys = groupsJson.keys();
                while (keys.hasNext()) {
                    String groupName = keys.next();
                    String stateJson = groupsJson.getString(groupName);
                    LocationPointState state = LocationPointState.fromJson(stateJson);
                    groupStates.put(groupName, state);
                }
            }
            
            Log.d(TAG, "成功加载状态，共 " + groupStates.size() + " 个分组");
            
        } catch (IOException | JSONException e) {
            Log.e(TAG, "加载状态失败", e);
            groupStates.clear();
        }
    }
    
    /**
     * 保存状态到文件
     */
    private void saveStates() {
        try {
            JSONObject rootJson = new JSONObject();
            JSONObject groupsJson = new JSONObject();
            
            for (Map.Entry<String, LocationPointState> entry : groupStates.entrySet()) {
                String groupName = entry.getKey();
                LocationPointState state = entry.getValue();
                groupsJson.put(groupName, state.toJson());
            }
            
            rootJson.put("groups", groupsJson);
            rootJson.put("lastSaved", System.currentTimeMillis());
            
            // 确保目录存在
            File parentDir = stateFile.getParentFile();
            if (parentDir != null && !parentDir.exists()) {
                parentDir.mkdirs();
            }
            
            try (FileOutputStream fos = new FileOutputStream(stateFile)) {
                fos.write(rootJson.toString().getBytes("UTF-8"));
                fos.flush();
            }
            
            Log.d(TAG, "成功保存状态，共 " + groupStates.size() + " 个分组");
            
        } catch (IOException | JSONException e) {
            Log.e(TAG, "保存状态失败", e);
        }
    }
    
    /**
     * 获取状态文件路径
     */
    public String getStateFilePath() {
        return stateFile.getAbsolutePath();
    }
    
    /**
     * 检查状态文件是否存在
     */
    public boolean isStateFileExists() {
        return stateFile.exists();
    }
    
    /**
     * 获取状态文件大小
     */
    public long getStateFileSize() {
        return stateFile.exists() ? stateFile.length() : 0;
    }

    /**
     * 重命名分组
     */
    public void renameGroup(String oldGroupName, String newGroupName) {
        if (oldGroupName == null || newGroupName == null || oldGroupName.equals(newGroupName)) {
            return;
        }

        LocationPointState state = groupStates.remove(oldGroupName);
        if (state != null) {
            groupStates.put(newGroupName, state);
            saveStates();
        }
    }

    /**
     * 删除分组
     */
    public void deleteGroup(String groupName) {
        if (groupName != null && groupStates.containsKey(groupName)) {
            groupStates.remove(groupName);
            saveStates();
        }
    }

    /**
     * 设置分组的顺序推送起点
     */
    public void setStartIndex(String groupName, int startIndex) {
        LocationPointState state = getGroupState(groupName);
        state.setStartIndex(startIndex);
        saveGroupState(groupName, state);
        Log.d(TAG, "Set start index for group " + groupName + ": " + startIndex);
    }

    /**
     * 获取分组的顺序推送起点
     */
    public int getStartIndex(String groupName) {
        LocationPointState state = getGroupState(groupName);
        return state.getStartIndex();
    }

    /**
     * 获取分组顺序列表
     */
    public java.util.List<String> getGroupOrder() {
        return new java.util.ArrayList<>(groupOrder);
    }

    /**
     * 设置分组顺序
     */
    public void setGroupOrder(java.util.List<String> newOrder) {
        this.groupOrder.clear();
        this.groupOrder.addAll(newOrder);
        saveGroupOrder();
        Log.d(TAG, "Updated group order: " + newOrder);
    }

    /**
     * 添加新分组到顺序列表
     */
    public void addGroupToOrder(String groupName) {
        if (!groupOrder.contains(groupName)) {
            groupOrder.add(groupName);
            saveGroupOrder();
            Log.d(TAG, "Added group to order: " + groupName);
        }
    }

    /**
     * 从顺序列表中移除分组
     */
    public void removeGroupFromOrder(String groupName) {
        if (groupOrder.remove(groupName)) {
            saveGroupOrder();
            Log.d(TAG, "Removed group from order: " + groupName);
        }
    }

    /**
     * 加载分组顺序
     */
    private void loadGroupOrder() {
        if (!groupOrderFile.exists()) {
            Log.d(TAG, "Group order file does not exist, using default order");
            return;
        }

        try (FileInputStream fis = new FileInputStream(groupOrderFile)) {
            byte[] data = new byte[(int) groupOrderFile.length()];
            fis.read(data);
            String jsonString = new String(data, "UTF-8");

            org.json.JSONArray jsonArray = new org.json.JSONArray(jsonString);
            groupOrder.clear();
            for (int i = 0; i < jsonArray.length(); i++) {
                groupOrder.add(jsonArray.getString(i));
            }

            Log.d(TAG, "Loaded group order: " + groupOrder);
        } catch (IOException e) {
            Log.e(TAG, "Error loading group order", e);
        } catch (Exception e) {
            Log.e(TAG, "Error parsing group order JSON", e);
        }
    }

    /**
     * 保存分组顺序
     */
    private void saveGroupOrder() {
        try {
            org.json.JSONArray jsonArray = new org.json.JSONArray();
            for (String groupName : groupOrder) {
                jsonArray.put(groupName);
            }

            String jsonString = jsonArray.toString();
            try (FileOutputStream fos = new FileOutputStream(groupOrderFile)) {
                fos.write(jsonString.getBytes("UTF-8"));
            }

            Log.d(TAG, "Saved group order: " + groupOrder);
        } catch (IOException e) {
            Log.e(TAG, "Error saving group order", e);
        } catch (Exception e) {
            Log.e(TAG, "Error creating group order JSON", e);
        }
    }

    /**
     * 迁移旧的状态文件从用户设置的GPS日志目录到应用内部存储目录
     */
    private void migrateOldStateFiles() {
        try {
            // 获取旧的文件位置（用户设置的GPS日志目录）
            String oldGpsLoggerFolder = PreferenceHelper.getInstance().getGpsLoggerFolder();
            File oldStateFile = new File(oldGpsLoggerFolder, STATE_FILE_NAME);
            File oldGroupOrderFile = new File(oldGpsLoggerFolder, GROUP_ORDER_FILE_NAME);

            Log.d(TAG, "Checking for old state files to migrate...");
            Log.d(TAG, "Old state file path: " + oldStateFile.getAbsolutePath());
            Log.d(TAG, "Old group order file path: " + oldGroupOrderFile.getAbsolutePath());

            boolean migrated = false;

            // 迁移状态文件
            if (oldStateFile.exists() && !stateFile.exists()) {
                if (copyFile(oldStateFile, stateFile)) {
                    Log.i(TAG, "Successfully migrated state file from " + oldStateFile.getAbsolutePath() +
                              " to " + stateFile.getAbsolutePath());
                    // 删除旧文件
                    if (oldStateFile.delete()) {
                        Log.d(TAG, "Deleted old state file: " + oldStateFile.getAbsolutePath());
                    }
                    migrated = true;
                } else {
                    Log.e(TAG, "Failed to migrate state file");
                }
            }

            // 迁移分组顺序文件
            if (oldGroupOrderFile.exists() && !groupOrderFile.exists()) {
                if (copyFile(oldGroupOrderFile, groupOrderFile)) {
                    Log.i(TAG, "Successfully migrated group order file from " + oldGroupOrderFile.getAbsolutePath() +
                              " to " + groupOrderFile.getAbsolutePath());
                    // 删除旧文件
                    if (oldGroupOrderFile.delete()) {
                        Log.d(TAG, "Deleted old group order file: " + oldGroupOrderFile.getAbsolutePath());
                    }
                    migrated = true;
                } else {
                    Log.e(TAG, "Failed to migrate group order file");
                }
            }

            if (migrated) {
                Log.i(TAG, "State file migration completed successfully");
            } else {
                Log.d(TAG, "No state files need migration");
            }

        } catch (Exception e) {
            Log.e(TAG, "Error during state file migration", e);
        }
    }

    /**
     * 复制文件的辅助方法
     */
    private boolean copyFile(File sourceFile, File destFile) {
        try {
            // 确保目标目录存在
            File destDir = destFile.getParentFile();
            if (destDir != null && !destDir.exists()) {
                destDir.mkdirs();
            }

            try (FileInputStream fis = new FileInputStream(sourceFile);
                 FileOutputStream fos = new FileOutputStream(destFile)) {

                byte[] buffer = new byte[8192];
                int bytesRead;
                while ((bytesRead = fis.read(buffer)) != -1) {
                    fos.write(buffer, 0, bytesRead);
                }
                fos.flush();
                return true;
            }
        } catch (IOException e) {
            Log.e(TAG, "Error copying file from " + sourceFile.getAbsolutePath() +
                      " to " + destFile.getAbsolutePath(), e);
            return false;
        }
    }
}
