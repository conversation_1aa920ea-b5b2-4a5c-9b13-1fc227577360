/*
 * Copyright (C) 2024 mendhak
 *
 * This file is part of GPSLogger for Android.
 *
 * GPSLogger is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 2 of the License, or
 * (at your option) any later version.
 *
 * GPSLogger is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with GPSLogger.  If not, see <http://www.gnu.org/licenses/>.
 */

package com.mendhak.gpslogger.ui.models;

import androidx.annotation.DrawableRes;

/**
 * 分组图标数据模型
 */
public class GroupIcon {
    
    @DrawableRes
    private int iconResource;
    private String iconName;
    private String iconId;
    
    public GroupIcon(@DrawableRes int iconResource, String iconName, String iconId) {
        this.iconResource = iconResource;
        this.iconName = iconName;
        this.iconId = iconId;
    }
    
    public int getIconResource() {
        return iconResource;
    }
    
    public void setIconResource(int iconResource) {
        this.iconResource = iconResource;
    }
    
    public String getIconName() {
        return iconName;
    }
    
    public void setIconName(String iconName) {
        this.iconName = iconName;
    }
    
    public String getIconId() {
        return iconId;
    }
    
    public void setIconId(String iconId) {
        this.iconId = iconId;
    }
    
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        
        GroupIcon groupIcon = (GroupIcon) obj;
        return iconResource == groupIcon.iconResource && 
               iconId != null && iconId.equals(groupIcon.iconId);
    }
    
    @Override
    public int hashCode() {
        int result = iconResource;
        result = 31 * result + (iconId != null ? iconId.hashCode() : 0);
        return result;
    }
    
    @Override
    public String toString() {
        return "GroupIcon{" +
                "iconResource=" + iconResource +
                ", iconName='" + iconName + '\'' +
                ", iconId='" + iconId + '\'' +
                '}';
    }
}
