# 模板变量互斥效果修复

## 🎯 问题描述

用户反馈模板变量互斥效果问题：

**期望的模板格式**：
```
{date} {time} -{group_name}_{button_name}_ {voice_text}{input_text}
```

**期望的互斥行为**：
- **语音模式**：`{voice_text}` = 语音内容，`{input_text}` = 空
- **文本模式**：`{input_text}` = 文本内容，`{voice_text}` = 空  
- **计数器模式**：`{voice_text}` = 空，`{input_text}` = 空

## 🔍 问题分析

### 修复前的问题
在外部设备触发的注释按钮处理中，缺少了模板变量的互斥清理逻辑：

1. **语音模式按钮**：没有清空 `{input_text}` 变量
2. **文本模式按钮**：已有清空 `{voice_text}` 的逻辑
3. **计数器模式按钮**：没有清空 `{voice_text}` 和 `{input_text}` 变量
4. **录音模式**：没有清空 `{input_text}` 变量

### 互斥逻辑的重要性
模板变量的互斥确保：
- 语音输入时，文本变量为空，避免显示上次的文本内容
- 文本输入时，语音变量为空，避免显示上次的语音内容
- 计数器模式时，两个变量都为空，只显示计数器相关内容

## ✅ 修复方案

### 修复1：语音输入结果处理

**修改前**：
```java
private void handleAnnotationVoiceInputResult(String text) {
    String trimmedText = text.trim();
    
    // 直接发送注释事件，没有清空input_text
    EventBus.getDefault().post(new CommandEvents.Annotate(trimmedText, trimmedText,
                                                          currentAnnotationButtonText, 
                                                          currentAnnotationButtonIndex, 
                                                          currentAnnotationGroupName));
}
```

**修改后**：
```java
private void handleAnnotationVoiceInputResult(String text) {
    String trimmedText = text.trim();
    
    // Clear input text to ensure mutual exclusion between voice_text and input_text variables
    com.mendhak.gpslogger.ui.components.template.providers.BasicVariableProvider
        .clearInputText(this);
    
    // Post annotation event with preserved button context
    EventBus.getDefault().post(new CommandEvents.Annotate(trimmedText, trimmedText,
                                                          currentAnnotationButtonText, 
                                                          currentAnnotationButtonIndex, 
                                                          currentAnnotationGroupName));
}
```

### 修复2：录音结果处理

**修改前**：
```java
private void handleAnnotationAudioRecordingResult(String audioFilePath) {
    // 直接发送注释事件，没有清空input_text
    EventBus.getDefault().post(new CommandEvents.Annotate("语音录制", audioFilePath,
                                                          currentAnnotationButtonText, 
                                                          currentAnnotationButtonIndex, 
                                                          currentAnnotationGroupName));
}
```

**修改后**：
```java
private void handleAnnotationAudioRecordingResult(String audioFilePath) {
    // Clear input text to ensure mutual exclusion between voice_text and input_text variables
    com.mendhak.gpslogger.ui.components.template.providers.BasicVariableProvider
        .clearInputText(this);
    
    // Post annotation event with audio file path and preserved button context
    EventBus.getDefault().post(new CommandEvents.Annotate("语音录制", audioFilePath,
                                                          currentAnnotationButtonText, 
                                                          currentAnnotationButtonIndex, 
                                                          currentAnnotationGroupName));
}
```

### 修复3：计数器模式处理

**修改前**：
```java
private boolean handleDirectCounterOnly(String buttonText, int buttonIndex) {
    String groupName = getButtonGroupName(buttonIndex);
    
    // 直接发送注释事件，没有清空voice_text和input_text
    EventBus.getDefault().post(new CommandEvents.Annotate("", "",
                                                          buttonText, buttonIndex, groupName, true));
    return true;
}
```

**修改后**：
```java
private boolean handleDirectCounterOnly(String buttonText, int buttonIndex) {
    String groupName = getButtonGroupName(buttonIndex);
    
    // Clear both voice_text and input_text for counter-only buttons
    com.mendhak.gpslogger.ui.components.template.providers.BasicVariableProvider
        .clearInputText(this);
    Session.getInstance().clearTemplateVoiceText();
    
    EventBus.getDefault().post(new CommandEvents.Annotate("", "",
                                                          buttonText, buttonIndex, groupName, true));
    return true;
}
```

## 🔧 技术实现细节

### 互斥清理方法

#### 清空输入文本变量
```java
com.mendhak.gpslogger.ui.components.template.providers.BasicVariableProvider.clearInputText(context);
```
- 清空 `{input_text}` 模板变量
- 从SharedPreferences中删除手动输入的文本内容

#### 清空语音文本变量
```java
Session.getInstance().clearTemplateVoiceText();
```
- 清空 `{voice_text}` 模板变量
- 从Session中删除语音识别的文本内容

### 完整的互斥逻辑流程

#### 语音模式触发流程
```
外部设备按键 → 弹出语音输入界面 → 用户语音输入
    ↓
handleAnnotationVoiceInputResult()
    ↓
BasicVariableProvider.clearInputText() // 清空{input_text}
    ↓
CommandEvents.Annotate(voiceText, voiceText, ...) // 设置{voice_text}
    ↓
模板处理：{voice_text} = "语音内容", {input_text} = ""
```

#### 文本模式触发流程
```
外部设备按键 → 弹出文本输入对话框 → 用户文本输入
    ↓
showAnnotationTextInputDialog.onTextInputResult()
    ↓
BasicVariableProvider.setInputText() // 设置{input_text}
Session.getInstance().clearTemplateVoiceText() // 清空{voice_text}
    ↓
CommandEvents.Annotate(inputText, "", ...) // 传递文本内容
    ↓
模板处理：{input_text} = "文本内容", {voice_text} = ""
```

#### 计数器模式触发流程
```
外部设备按键 → 直接执行计数器逻辑
    ↓
handleDirectCounterOnly()
    ↓
BasicVariableProvider.clearInputText() // 清空{input_text}
Session.getInstance().clearTemplateVoiceText() // 清空{voice_text}
    ↓
CommandEvents.Annotate("", "", ..., true) // isCounterOnly=true
    ↓
模板处理：{voice_text} = "", {input_text} = "", {counter_xxx} = 计数值
```

#### 录音模式触发流程
```
外部设备按键 → 弹出语音输入界面 → 用户选择录音
    ↓
handleAnnotationAudioRecordingResult()
    ↓
BasicVariableProvider.clearInputText() // 清空{input_text}
    ↓
CommandEvents.Annotate("语音录制", audioFilePath, ...) // 设置{voice_text}
    ↓
模板处理：{voice_text} = "语音录制", {input_text} = ""
```

## 📊 修复效果验证

### 测试模板
```
{date} {time} -{group_name}_{button_name}_ {voice_text}{input_text}
```

### 预期结果

#### 语音模式按钮触发
**输入**：语音识别结果 "停车记录"
**输出**：`2024-08-01 14:30:25 -外部设备_停车记录_ 停车记录`
- `{voice_text}` = "停车记录"
- `{input_text}` = ""（空）

#### 文本模式按钮触发
**输入**：手动输入 "加油站记录"
**输出**：`2024-08-01 14:31:10 -外部设备_加油记录_ 加油站记录`
- `{voice_text}` = ""（空）
- `{input_text}` = "加油站记录"

#### 计数器模式按钮触发
**输入**：无用户输入
**输出**：`2024-08-01 14:32:05 -外部设备_计数测试_ `
- `{voice_text}` = ""（空）
- `{input_text}` = ""（空）
- `{counter_button_3}` = "1"（如果有计数器变量）

#### 录音模式按钮触发
**输入**：录音文件
**输出**：`2024-08-01 14:33:20 -外部设备_语音记录_ 语音录制`
- `{voice_text}` = "语音录制"
- `{input_text}` = ""（空）

## 🔄 其他触发方式的一致性

### 已验证的互斥逻辑

#### 1. 快速语音输入（一键语音输入）
```java
// GpsMainActivity.handleQuickVoiceInputResult()
BasicVariableProvider.clearInputText(this); // ✅ 清空{input_text}
```

#### 2. 快速文本输入（一键文本输入）
```java
// GpsMainActivity.handleQuickTextInputResult()
BasicVariableProvider.setInputText(this, inputText); // ✅ 设置{input_text}
Session.getInstance().clearTemplateVoiceText(); // ✅ 清空{voice_text}
```

#### 3. 通知栏语音输入
```java
// NotificationVoiceInputActivity.handleVoiceInputResult()
BasicVariableProvider.clearInputText(this); // ✅ 清空{input_text}
```

#### 4. 通知栏文本输入
```java
// NotificationTextInputActivity.handleQuickTextInputResult()
BasicVariableProvider.setInputText(this, inputText); // ✅ 设置{input_text}
Session.getInstance().clearTemplateVoiceText(); // ✅ 清空{voice_text}
```

#### 5. 注释页面按钮
```java
// AnnotationViewFragment文本输入
BasicVariableProvider.setInputText(getContext(), inputText); // ✅ 设置{input_text}
Session.getInstance().clearTemplateVoiceText(); // ✅ 清空{voice_text}

// AnnotationViewFragment语音输入
BasicVariableProvider.clearInputText(getContext()); // ✅ 清空{input_text}
```

## 🎯 关键改进点

1. **完整的互斥逻辑**：所有输入模式都正确实现了变量互斥
2. **一致性保证**：外部设备触发与其他触发方式行为完全一致
3. **模板变量清理**：确保不会显示上次输入的残留内容
4. **计数器模式支持**：正确清空所有输入变量，只保留计数器变量
5. **录音模式支持**：录音文件路径作为语音内容，清空文本变量

## 🧪 验证建议

### 测试步骤

1. **配置模板**：
   ```
   {date} {time} -{group_name}_{button_name}_ {voice_text}{input_text}
   ```

2. **配置注释按钮**：
   - Button1：语音模式
   - Button2：文本模式  
   - Button3：计数器模式

3. **映射外部设备按键**并依次测试

4. **验证输出格式**：
   - 语音模式：`..._ 语音内容`（{input_text}为空）
   - 文本模式：`..._ 文本内容`（{voice_text}为空）
   - 计数器模式：`..._ `（两个变量都为空）

现在所有触发方式的模板变量互斥效果都已正确实现！🎉
