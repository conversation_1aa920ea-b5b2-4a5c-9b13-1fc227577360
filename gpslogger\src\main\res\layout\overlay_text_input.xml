<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:background="@drawable/overlay_background"
    android:padding="16dp"
    android:layout_margin="16dp"
    android:elevation="8dp">

    <!-- 标题栏 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:paddingBottom="8dp">

        <ImageView
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:src="@drawable/ic_edit_24dp"
            android:tint="@color/primaryColor"
            android:layout_marginEnd="8dp" />

        <TextView
            android:id="@+id/overlay_title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="文本输入"
            android:textSize="18sp"
            android:textStyle="bold"
            android:textColor="@color/primaryTextColor" />

    </LinearLayout>

    <!-- 输入提示 -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="请输入注释内容："
        android:textSize="14sp"
        android:textColor="@color/primaryTextColor"
        android:paddingBottom="8dp" />

    <!-- 文本输入框 -->
    <EditText
        android:id="@+id/overlay_input"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:minHeight="80dp"
        android:maxHeight="120dp"
        android:background="@drawable/overlay_input_background"
        android:padding="12dp"
        android:hint="在此输入文本内容..."
        android:textSize="16sp"
        android:textColor="@color/primaryTextColor"
        android:textColorHint="@color/hintTextColor"
        android:inputType="textMultiLine|textCapSentences"
        android:maxLines="3"
        android:scrollbars="vertical"
        android:gravity="top|start"
        android:layout_marginBottom="16dp" />

    <!-- 按钮栏 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="end">

        <Button
            android:id="@+id/overlay_cancel"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="取消"
            android:textSize="14sp"
            android:textColor="@color/secondaryTextColor"
            android:background="@drawable/overlay_button_secondary"
            android:paddingHorizontal="20dp"
            android:paddingVertical="8dp"
            android:layout_marginEnd="12dp"
            android:minWidth="80dp" />

        <Button
            android:id="@+id/overlay_confirm"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="确认"
            android:textSize="14sp"
            android:textColor="@android:color/white"
            android:background="@drawable/overlay_button_primary"
            android:paddingHorizontal="20dp"
            android:paddingVertical="8dp"
            android:minWidth="80dp" />

    </LinearLayout>

</LinearLayout>
