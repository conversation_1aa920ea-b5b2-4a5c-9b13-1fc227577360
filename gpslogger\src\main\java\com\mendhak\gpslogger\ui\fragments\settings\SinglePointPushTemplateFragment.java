/*
 * Copyright (C) 2024 mendhak
 *
 * This file is part of GPSLogger for Android.
 *
 * GPSLogger is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 2 of the License, or
 * (at your option) any later version.
 *
 * GPSLogger is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with GPSLogger.  If not, see <http://www.gnu.org/licenses/>.
 */

package com.mendhak.gpslogger.ui.fragments.settings;

import android.content.Context;
import android.os.Bundle;
import android.text.Editable;
import android.text.TextWatcher;
import android.view.LayoutInflater;
import android.view.Menu;
import android.view.MenuInflater;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.EditText;
import android.widget.Switch;
import android.widget.TextView;
import android.widget.Toast;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import com.mendhak.gpslogger.R;
import com.mendhak.gpslogger.common.PreferenceHelper;
import com.mendhak.gpslogger.common.slf4j.Logs;
import com.mendhak.gpslogger.ui.components.template.AnnotationTemplateEngine;
import com.mendhak.gpslogger.ui.components.template.TemplateParser;
import com.mendhak.gpslogger.ui.dialogs.VariableSelectorDialog;
import com.mendhak.gpslogger.ui.dialogs.TemplatePresetsDialog;
import org.slf4j.Logger;

/**
 * Fragment for editing single point push templates
 */
public class SinglePointPushTemplateFragment extends Fragment {

    private static final Logger LOG = Logs.of(SinglePointPushTemplateFragment.class);

    private EditText templateEditText;
    private TextView previewTextView;
    private TextView validationTextView;
    private Switch enabledSwitch;
    private Button variableSelectorButton;
    private Button presetsButton;
    private Button configUserVariablesButton;
    private Button manageCountersButton;
    private Button configNumericCountersButton;
    private Button saveButton;
    private Button resetButton;
    private Button saveAsPresetButton;

    private PreferenceHelper preferenceHelper;
    private AnnotationTemplateEngine templateEngine;

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setHasOptionsMenu(true);

        preferenceHelper = PreferenceHelper.getInstance();
        templateEngine = AnnotationTemplateEngine.getInstance();

        LOG.debug("SinglePointPushTemplateFragment created");
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.fragment_annotation_template, container, false);

        initializeViews(view);
        setupListeners();
        loadCurrentTemplate();
        updatePreviewAndValidation();

        LOG.debug("SinglePointPushTemplateFragment view created");
        return view;
    }

    @Override
    public void onCreateOptionsMenu(@NonNull Menu menu, @NonNull MenuInflater inflater) {
        inflater.inflate(R.menu.menu_annotation_template, menu);
        super.onCreateOptionsMenu(menu, inflater);
    }

    @Override
    public boolean onOptionsItemSelected(@NonNull MenuItem item) {
        if (item.getItemId() == R.id.action_save) {
            saveTemplate();
            return true;
        } else if (item.getItemId() == R.id.action_import) {
            // Import functionality can be added later
            Toast.makeText(getContext(), "导入功能暂未实现", Toast.LENGTH_SHORT).show();
            return true;
        } else if (item.getItemId() == R.id.action_export) {
            // Export functionality can be added later
            Toast.makeText(getContext(), "导出功能暂未实现", Toast.LENGTH_SHORT).show();
            return true;
        }
        return super.onOptionsItemSelected(item);
    }

    /**
     * Initialize all views
     */
    private void initializeViews(View view) {
        templateEditText = view.findViewById(R.id.template_edit_text);
        previewTextView = view.findViewById(R.id.preview_text_view);
        validationTextView = view.findViewById(R.id.validation_text_view);
        enabledSwitch = view.findViewById(R.id.enabled_switch);
        variableSelectorButton = view.findViewById(R.id.variable_selector_button);
        presetsButton = view.findViewById(R.id.presets_button);
        configUserVariablesButton = view.findViewById(R.id.config_user_variables_button);
        manageCountersButton = view.findViewById(R.id.manage_counters_button);
        configNumericCountersButton = view.findViewById(R.id.config_numeric_counters_button);
        saveButton = view.findViewById(R.id.save_button);
        resetButton = view.findViewById(R.id.reset_button);
        saveAsPresetButton = view.findViewById(R.id.save_as_preset_button);

        LOG.debug("Views initialized");
    }

    /**
     * Setup all event listeners
     */
    private void setupListeners() {
        // Template text change listener
        templateEditText.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {}

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {}

            @Override
            public void afterTextChanged(Editable s) {
                updatePreviewAndValidation();
            }
        });

        // Button listeners
        variableSelectorButton.setOnClickListener(v -> showVariableSelector());
        presetsButton.setOnClickListener(v -> showTemplatePresets());
        configUserVariablesButton.setOnClickListener(v -> showUserVariableConfigDialog());
        manageCountersButton.setOnClickListener(v -> showCounterManagerDialog());
        configNumericCountersButton.setOnClickListener(v -> showNumericCounterConfigDialog());
        saveButton.setOnClickListener(v -> saveTemplate());
        resetButton.setOnClickListener(v -> resetTemplate());
        saveAsPresetButton.setOnClickListener(v -> saveAsPreset());

        LOG.debug("Event listeners setup complete");
    }

    /**
     * Load current template from preferences
     */
    private void loadCurrentTemplate() {
        String template = preferenceHelper.getSinglePointPushTemplate();
        boolean enabled = preferenceHelper.isSinglePointPushTemplateEnabled();

        templateEditText.setText(template);
        enabledSwitch.setChecked(enabled);

        LOG.debug("Loaded template: enabled={}, template='{}'", enabled, template);
    }

    /**
     * Update preview and validation
     */
    private void updatePreviewAndValidation() {
        String template = templateEditText.getText().toString();

        // Update validation
        TemplateParser.ValidationResult validation = templateEngine.validateTemplate(template);
        if (validation.isValid()) {
            validationTextView.setText("✓ 模板语法正确");
            validationTextView.setTextColor(getResources().getColor(android.R.color.holo_green_dark));
        } else {
            validationTextView.setText("✗ " + validation.getErrorMessage());
            validationTextView.setTextColor(getResources().getColor(android.R.color.holo_red_dark));
        }

        // Update preview
        updatePreview(template);
    }

    /**
     * Update template preview
     */
    private void updatePreview(String template) {
        if (template.trim().isEmpty()) {
            previewTextView.setText("预览将在这里显示");
            return;
        }

        try {
            // Create sample context for preview
            Context context = getContext();
            if (context == null) {
                previewTextView.setText("无法生成预览");
                return;
            }

            // Process template with sample data
            String preview = templateEngine.processTemplateString(
                template, context, null, "示例操作", "推送", 1, "示例分组"
            );

            previewTextView.setText(preview);

        } catch (Exception e) {
            LOG.error("Error updating preview", e);
            previewTextView.setText("预览错误: " + e.getMessage());
        }
    }

    /**
     * Show variable selector dialog
     */
    private void showVariableSelector() {
        VariableSelectorDialog dialog = new VariableSelectorDialog();
        dialog.setOnVariableSelectedListener(variable -> {
            insertVariableAtCursor(variable);
        });
        dialog.show(getParentFragmentManager(), "variable_selector");
    }

    /**
     * Show template presets dialog
     */
    private void showTemplatePresets() {
        TemplatePresetsDialog dialog = new TemplatePresetsDialog();
        // Set custom storage key for single point push template presets
        dialog.setStorageKey("single_point_push_template_presets");
        dialog.setOnTemplateSelectedListener(template -> {
            templateEditText.setText(template);
        });
        dialog.show(getParentFragmentManager(), "template_presets");
    }

    /**
     * Show user variable configuration dialog
     */
    private void showUserVariableConfigDialog() {
        com.mendhak.gpslogger.ui.dialogs.UserVariableConfigDialog dialog =
            new com.mendhak.gpslogger.ui.dialogs.UserVariableConfigDialog();
        dialog.setOnVariablesUpdatedListener(() -> {
            // Refresh preview to show updated variable values
            updatePreviewAndValidation();
        });
        dialog.show(getParentFragmentManager(), "user_variable_config");
    }

    /**
     * Show counter manager dialog
     */
    private void showCounterManagerDialog() {
        com.mendhak.gpslogger.ui.dialogs.CounterManagerDialog dialog =
            new com.mendhak.gpslogger.ui.dialogs.CounterManagerDialog();
        dialog.setOnCountersUpdatedListener(() -> {
            // Refresh preview to show updated counter values
            updatePreviewAndValidation();
        });
        dialog.show(getParentFragmentManager(), "counter_manager");
    }

    /**
     * Show numeric counter configuration dialog
     */
    private void showNumericCounterConfigDialog() {
        com.mendhak.gpslogger.ui.dialogs.NumericCounterConfigDialog dialog =
            new com.mendhak.gpslogger.ui.dialogs.NumericCounterConfigDialog();
        dialog.setOnCountersUpdatedListener(() -> {
            // Refresh preview to show updated values
            updatePreviewAndValidation();
        });
        dialog.show(getParentFragmentManager(), "numeric_counter_config");
    }

    /**
     * Insert variable at cursor position
     */
    private void insertVariableAtCursor(String variable) {
        int start = templateEditText.getSelectionStart();
        int end = templateEditText.getSelectionEnd();

        // Format variable with braces if not already formatted
        String formattedVariable = variable.startsWith("{") ? variable : "{" + variable + "}";

        Editable editable = templateEditText.getText();
        editable.replace(start, end, formattedVariable);

        // Move cursor to end of inserted variable
        templateEditText.setSelection(start + formattedVariable.length());

        LOG.debug("Inserted variable '{}' at position {}", formattedVariable, start);
    }

    /**
     * Save template to preferences
     */
    private void saveTemplate() {
        String template = templateEditText.getText().toString();
        boolean enabled = enabledSwitch.isChecked();

        // Validate before saving
        if (enabled && !templateEngine.validateTemplate(template).isValid()) {
            Toast.makeText(getContext(), "模板语法错误，无法保存", Toast.LENGTH_SHORT).show();
            return;
        }

        preferenceHelper.setSinglePointPushTemplate(template);
        preferenceHelper.setSinglePointPushTemplateEnabled(enabled);

        Toast.makeText(getContext(), "单点推送模板已保存", Toast.LENGTH_SHORT).show();
        LOG.info("Single point push template saved: enabled={}, template='{}'", enabled, template);
    }

    /**
     * Reset template to default
     */
    private void resetTemplate() {
        templateEditText.setText("{primary_button_name}: {push_original_info} | 分组: {group_name}");
        updatePreviewAndValidation();
    }

    /**
     * Save current template as preset
     */
    private void saveAsPreset() {
        String template = templateEditText.getText().toString();
        if (template.trim().isEmpty()) {
            Toast.makeText(getContext(), "模板不能为空", Toast.LENGTH_SHORT).show();
            return;
        }

        com.mendhak.gpslogger.ui.dialogs.EditTemplatePresetDialog dialog =
            new com.mendhak.gpslogger.ui.dialogs.EditTemplatePresetDialog();
        dialog.setPreset("我的单点推送模板", template, "自定义单点推送模板");
        dialog.setOnPresetEditedListener((name, presetTemplate, description) -> {
            // Save preset to single point push template presets storage
            saveSinglePointPushPreset(name, presetTemplate, description);
            Toast.makeText(getContext(), "模板已保存为预设", Toast.LENGTH_SHORT).show();
        });
        dialog.show(getParentFragmentManager(), "save_as_preset");
    }

    /**
     * Save single point push template preset
     */
    private void saveSinglePointPushPreset(String name, String template, String description) {
        try {
            android.content.SharedPreferences prefs = getContext().getSharedPreferences("single_point_push_template_presets", android.content.Context.MODE_PRIVATE);
            String presetsJson = prefs.getString("user_presets", "[]");

            org.json.JSONArray jsonArray = new org.json.JSONArray(presetsJson);
            org.json.JSONObject newPreset = new org.json.JSONObject();
            newPreset.put("name", name);
            newPreset.put("template", template);
            newPreset.put("description", description);
            jsonArray.put(newPreset);

            prefs.edit().putString("user_presets", jsonArray.toString()).apply();
            LOG.debug("Saved single point push template preset: {}", name);
        } catch (Exception e) {
            LOG.error("Error saving single point push template preset", e);
        }
    }
}