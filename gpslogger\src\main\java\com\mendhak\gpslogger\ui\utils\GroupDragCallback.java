/*
 * Copyright (C) 2024 mendhak
 *
 * This file is part of GPSLogger for Android.
 *
 * GPSLogger is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 2 of the License, or
 * (at your option) any later version.
 *
 * GPSLogger is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with GPSLogger.  If not, see <http://www.gnu.org/licenses/>.
 */

package com.mendhak.gpslogger.ui.utils;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.ItemTouchHelper;
import androidx.recyclerview.widget.RecyclerView;

/**
 * 分组拖动排序回调类
 * 实现ItemTouchHelper.Callback，处理分组的拖动排序逻辑
 */
public class GroupDragCallback extends ItemTouchHelper.Callback {

    private final OnItemMoveListener listener;

    public interface OnItemMoveListener {
        /**
         * 当项目被移动时调用
         * @param fromPosition 起始位置
         * @param toPosition 目标位置
         * @return 是否成功移动
         */
        boolean onItemMove(int fromPosition, int toPosition);

        /**
         * 当拖动开始时调用
         * @param viewHolder 被拖动的ViewHolder
         */
        void onItemDragStart(RecyclerView.ViewHolder viewHolder);

        /**
         * 当拖动结束时调用
         * @param viewHolder 被拖动的ViewHolder
         */
        void onItemDragEnd(RecyclerView.ViewHolder viewHolder);
    }

    public GroupDragCallback(OnItemMoveListener listener) {
        this.listener = listener;
    }

    @Override
    public boolean isLongPressDragEnabled() {
        // 禁用长按拖动，我们将通过拖动手柄来触发拖动
        return false;
    }

    @Override
    public boolean isItemViewSwipeEnabled() {
        // 禁用滑动删除
        return false;
    }

    @Override
    public int getMovementFlags(@NonNull RecyclerView recyclerView, @NonNull RecyclerView.ViewHolder viewHolder) {
        // 只允许上下拖动
        int dragFlags = ItemTouchHelper.UP | ItemTouchHelper.DOWN;
        int swipeFlags = 0; // 不允许滑动
        return makeMovementFlags(dragFlags, swipeFlags);
    }

    @Override
    public boolean onMove(@NonNull RecyclerView recyclerView, 
                         @NonNull RecyclerView.ViewHolder viewHolder, 
                         @NonNull RecyclerView.ViewHolder target) {
        if (listener != null) {
            return listener.onItemMove(viewHolder.getAdapterPosition(), target.getAdapterPosition());
        }
        return false;
    }

    @Override
    public void onSwiped(@NonNull RecyclerView.ViewHolder viewHolder, int direction) {
        // 不处理滑动事件
    }

    @Override
    public void onSelectedChanged(RecyclerView.ViewHolder viewHolder, int actionState) {
        super.onSelectedChanged(viewHolder, actionState);
        
        if (actionState == ItemTouchHelper.ACTION_STATE_DRAG) {
            // 拖动开始
            if (listener != null && viewHolder != null) {
                listener.onItemDragStart(viewHolder);
            }
        } else if (actionState == ItemTouchHelper.ACTION_STATE_IDLE) {
            // 拖动结束
            if (listener != null && viewHolder != null) {
                listener.onItemDragEnd(viewHolder);
            }
        }
    }

    @Override
    public void clearView(@NonNull RecyclerView recyclerView, @NonNull RecyclerView.ViewHolder viewHolder) {
        super.clearView(recyclerView, viewHolder);
        
        // 拖动结束，恢复ViewHolder的外观
        if (listener != null) {
            listener.onItemDragEnd(viewHolder);
        }
    }

    /**
     * 开始拖动指定的ViewHolder
     * 通常由拖动手柄的触摸事件触发
     */
    public void startDrag(RecyclerView.ViewHolder viewHolder, ItemTouchHelper itemTouchHelper) {
        if (itemTouchHelper != null && viewHolder != null) {
            itemTouchHelper.startDrag(viewHolder);
        }
    }
}
