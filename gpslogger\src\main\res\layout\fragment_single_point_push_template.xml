<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:padding="16dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <!-- Header -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="单点推送模板编辑器"
            android:textSize="20sp"
            android:textStyle="bold"
            android:layout_marginBottom="16dp"
            android:gravity="center" />

        <!-- Enable Switch -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="16dp">

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="启用单点推送结果记录"
                android:textSize="16sp" />

            <Switch
                android:id="@+id/enabled_switch"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content" />

        </LinearLayout>

        <!-- Template Input -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="模板内容："
            android:textSize="16sp"
            android:textStyle="bold"
            android:layout_marginBottom="8dp" />

        <EditText
            android:id="@+id/template_edit_text"
            android:layout_width="match_parent"
            android:layout_height="120dp"
            android:gravity="top|start"
            android:inputType="textMultiLine"
            android:scrollbars="vertical"
            android:background="@drawable/edittext_background"
            android:padding="12dp"
            android:hint="输入单点推送结果记录模板..."
            android:layout_marginBottom="16dp" />

        <!-- Action Buttons Row 1 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="8dp">

            <Button
                android:id="@+id/insert_variable_button"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="插入变量"
                android:layout_marginEnd="4dp"
                style="@style/Widget.AppCompat.Button.Colored" />

            <Button
                android:id="@+id/preset_templates_button"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="预设模板"
                android:layout_marginStart="4dp"
                style="@style/Widget.AppCompat.Button.Colored" />

        </LinearLayout>

        <!-- Action Buttons Row 2 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="16dp">

            <Button
                android:id="@+id/counter_manager_button"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="管理计数器"
                android:layout_marginEnd="4dp"
                style="@style/Widget.AppCompat.Button.Colored" />

            <Button
                android:id="@+id/numeric_counter_button"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="数值计数器"
                android:layout_marginStart="4dp"
                style="@style/Widget.AppCompat.Button.Colored" />

        </LinearLayout>

        <!-- Preview Section -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="预览："
            android:textSize="16sp"
            android:textStyle="bold"
            android:layout_marginBottom="8dp" />

        <TextView
            android:id="@+id/preview_text"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:minHeight="60dp"
            android:background="@android:drawable/edit_text"
            android:padding="12dp"
            android:text="预览将在此显示..."
            android:textColor="@android:color/black"
            android:layout_marginBottom="16dp" />

        <!-- Help Text -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="提示：\n• 使用 {变量名} 格式插入变量\n• 单点推送专用变量：{total_push_points}, {pushed_points_count}, {pass_count}, {fail_count}, {push_original_info}\n• 支持所有标准注释变量\n• 模板将用于生成 Push_location_result.txt 文件内容"
            android:textSize="12sp"
            android:textColor="@android:color/darker_gray"
            android:background="@android:drawable/edit_text"
            android:padding="12dp"
            android:layout_marginBottom="16dp" />

        <!-- Save and Reset Buttons -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <Button
                android:id="@+id/save_button"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="保存"
                android:layout_marginEnd="8dp"
                style="@style/Widget.AppCompat.Button.Colored" />

            <Button
                android:id="@+id/reset_button"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="重置"
                android:layout_marginStart="8dp"
                style="@style/Widget.AppCompat.Button.Borderless.Colored" />

        </LinearLayout>

    </LinearLayout>

</ScrollView>
